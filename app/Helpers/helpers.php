<?php
use Hidehalo\Nanoid\Client;

/**
 * 生成唯一ID
 *
 * @return string
 */
function nanoid($ts = false)
{
    $client = new Client();

    $result = $client->generateId($size = 21, $mode = Client::MODE_DYNAMIC);;
    if ($ts) {
        $result = intval(microtime(true) * 10000) . '-' . $result;
    }

    return $result;
}

/**
 * 查询构造器打印sql
 * @param $query
 * @return void
 * //use App\Models\User;
 * //$query = User::where('name', 'John')->where('age', 30);
 */
function printToSql($query='')
{
    // 获取带占位符的 SQL（如：select * from users where name = ? and age = ?）
    $sql = $query->toSql();
    // 获取绑定的参数
    $bindings = $query->getBindings();
    // 安全替换占位符（使用 PDO 参数转义）
    $fullSql = vsprintf(str_replace('?', '%s', $sql), array_map(function ($value) use ($query) {
        return $query->getConnection()->getPdo()->quote($value);
    }, $bindings));

    dd($fullSql);
}
