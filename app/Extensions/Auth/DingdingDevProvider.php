<?php
namespace App\Extensions\Auth;

use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Auth\UserProvider;

class DingdingDevProvider implements UserProvider{

    // 函数通常接收表示用户的主键，例如 MySQL 数据库中的自动递增 ID。方法应检索并返回与 ID 匹配的 Authenticatable 实现。
    public function retrieveById($identifier)
    {
        // TODO: Implement retrieveById() method.
    }

    // 函数通过用户唯一的 $identifier 和 「记住我」的 $token 检索用户，通常存储在数据库列中，如 remember_token 。与前面的方法一样，此方法应返回具有匹配令牌值的 Authenticatable 实现
    public function retrieveByToken($identifier, $token)
    {
        // TODO: Implement retrieveByToken() method.
    }

    public function updateRememberToken(Authenticatable $user, $token)
    {
        // TODO: Implement updateRememberToken() method.
    }

    // 方法接收传递给 Auth::attempt 方法的凭据数组。然后，该方法应该「查询」底层的持久性存储以查找与这些凭据匹配的用户。
    //通常，此方法将运行带有「where」条件的查询，以搜索「username」与 $credentials['username'] 的值匹配的用户记录。
    //该方法应返回 Authenticatable 的实现。 此方法不应尝试执行任何密码验证或身份验证。
    public function retrieveByCredentials(array $credentials)
    {
        // TODO: Implement retrieveByCredentials() method.
    }

    // 方法应将给定的 $user 与 $credentials 进行比较，以对用户进行身份验证。例如，
    //此方法通常会使用 Hash::check 方法将 $user->getAuthPassword() 的值与 $credentials['password'] 的值进行比较。
    //此方法应返回 true 或 false，指示密码是否有效。
    public function validateCredentials(Authenticatable $user, array $credentials)
    {
        // TODO: Implement validateCredentials() method.
    }
}
