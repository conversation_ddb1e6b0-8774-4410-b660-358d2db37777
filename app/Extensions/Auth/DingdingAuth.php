<?php
namespace App\Extensions\Auth;

use Illuminate\Contracts\Auth\Authenticatable;

class DingdingAuth implements Authenticatable{

    // 回用户的「主键」字段的名称
    public function getAuthIdentifierName()
    {
        // TODO: Implement getAuthIdentifierName() method.
    }

    // 方法应返回用户的「主键」
    public function getAuthIdentifier()
    {
        // TODO: Implement getAuthIdentifier() method.
    }

    // 返回用户的 hash 密码
    public function getAuthPassword()
    {
        // TODO: Implement getAuthPassword() method.
    }

    public function getRememberToken()
    {
        // TODO: Implement getRememberToken() method.
    }

    public function setRememberToken($value)
    {
        // TODO: Implement setRememberToken() method.
    }

    public function getRememberTokenName()
    {
        // TODO: Implement getRememberTokenName() method.
    }
}
