<?php
namespace App\Extensions\Auth;

use App\Exceptions\YunzhijiaOAuth2Exception;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Arr;
use EasyWeChat\Work\Application;


class YunzhijiaGuard implements Guard{

    // 确定当前用户是否已通过身份验证
    public function check()
    {
        if (session()->missing("user")) {

            $corpid = Request::input('corpid');
            $guard = Request::input('guard');
            $appid = Request::input('appid');
            $ticket = Request::input('ticket', '');
            // $code = Request::input('code', '');
            $microtime = microtime(true);

            if (empty($guard) || empty($appid) || empty($ticket)) {
                throw new YunzhijiaOAuth2Exception("yunzhijia missing params, guard:{$guard} or appid:{$appid} or ticket:{$ticket}");
            }

            $appConfig = config("qingyingyong.{$guard}.{$corpid}.{$appid}", []);
            if (empty($appConfig)) {
                throw new YunzhijiaOAuth2Exception("yunzhijia missing config, qingyingyong.{$guard}.{$corpid}.{$appid}");
            }

            $client = new \GuzzleHttp\Client();

            $form = [
                'timestamp' => intval($microtime * 1000),
                'appId' => $appConfig['appid'], //$yzjAppId,
                'secret' => $appConfig['secret'], //$yzjAppSecret,
                'scope' => 'app',
                'eid' => $appConfig['corpid'],
            ];
            // 获得access token
            $response = $client->request('POST', 'https://yunzhijia.com/gateway/oauth2/token/getAccessToken', [
                    'form_params' => $form
                ]
            );
            $tokenContent = $response->getBody()->getContents();
            $tokenContent = json_decode($tokenContent, true);
            $accessToken = '';
            if ($tokenContent['errorCode'] == 0) {
                $accessToken = $tokenContent['data']['accessToken'];
            }

            // 获取用户信息
            $form = [
                'eid' => '17916472',
                'ticket' => $ticket,
                'appid' => $appConfig['appid'],
            ];
            $ticketResponse = $client->request('POST', 'https://yunzhijia.com/gateway/ticket/user/acquirecontext?accessToken=' . $accessToken, [
                'headers' => ['Content-Type' => 'application/json'],
                'json' => $form
            ]);

            // {"success":true,"errorCode":0,"data":{"appid":"500829898","eid":"17916472","openid":"60e26896e4b01596045c9c38","username":"\u5434\u78ca","uid":"135634918","tid":"17916472","userid":"60e267a0e4b0d3fe4ced0e5c","oid":"60e26896e4b01596045c9c38","networkid":"5ddce092e4b0b2767968c07e","xtid":"60e26896e4b01596045c9c35","ticket":null,"deviceId":"f14ee28aa417a179c91bbc54a34555e3","jobNo":"0110210366"},"error":null}
            $ticketResponse = $ticketResponse->getBody()->getContents();
            $ticketResponse = json_decode($ticketResponse, true);
            // if ($ticketResponse['errorCode'] == 0) {
            //    $openId = $ticketResponse['data']['openid'];
            // }


            session()->put('user', [
                'id' => $ticketResponse['data']['jobNo'],
                'userid' => $ticketResponse['data']['jobNo'],
                'name' => $ticketResponse['data']['username'],
                'guard' => $guard,
                'appid' => $appid,
                'openid' => $ticketResponse['data']['openid'],
            ]);





//                    $form = [
//                        'timestamp' => intval($microtime * 1000),
//                        'appId' => $yzjAppId,
//                        'secret' => $yzjResGroupSecret,
//                        'scope' => 'resGroupSecret',
//                        'eid' => '17916472',
//                    ];
//                    $resGroupTokenResponse = $client->request('POST', 'https://yunzhijia.com/gateway/oauth2/token/getAccessToken', [
//                            'form_params' => $form
//                        ]
//                    );
//                    $resGroupToken = '';
//                    $resGroupTokenResponse = $resGroupTokenResponse->getBody()->getContents();
//                    $resGroupTokenResponse = json_decode($resGroupTokenResponse, true);
//                    if ($resGroupTokenResponse['errorCode'] == 0) {
//                        $resGroupToken = $resGroupTokenResponse['data']['accessToken'];
//                    }
//                    if (empty($resGroupToken)) {
//                        Log::error(json_encode([
//                            'c' => 'AuthYunzhijia',
//                            'a' => 'handle',
//                            's' => 'resGroupAccessToken',
//                            'errMsg' => 'Get resGroupAsccessToken failed',
//                            'form' => $form,
//                        ]));
//
//                        return redirect(route('yzj.nologin'));
//                    }
//
//                    $form = [
//                        'eid' => '17916472',
//                        'data' => json_encode(['type'=>1, 'array'=>[$openId]]),
//                    ];
//                    $userResponse = $client->request('POST', 'https://yunzhijia.com/gateway/openimport/open/person/get?accessToken=' . $resGroupToken, [
//                        'headers' => ['Content-Type' => 'application/x-www-form-urlencoded'],
//                        'form_params' => $form,
//                    ]);
//
//
//                    // {"success":true,"errorCode":0,"data":{"appid":"500829898","eid":"17916472","openid":"60e26896e4b01596045c9c38","username":"\u5434\u78ca","uid":"135634918","tid":"17916472","userid":"60e267a0e4b0d3fe4ced0e5c","oid":"60e26896e4b01596045c9c38","networkid":"5ddce092e4b0b2767968c07e","xtid":"60e26896e4b01596045c9c35","ticket":null,"deviceId":"f14ee28aa417a179c91bbc54a34555e3","jobNo":"0110210366"},"error":null}
//                    $userResponse = json_decode($userResponse->getBody()->getContents(), true);
//
//                    if ($userResponse['success'] == true) {
//                        $phone = ($userResponse['data'][0]['phone']) ?? '';
//                        $name = ($userResponse['data'][0]['name']) ?? '';
//                        $photoUrl = ($userResponse['data'][0]['photoUrl']) ?? '';
//                        $jobNo = ($userResponse['data'][0]['jobNo']) ?? '';
//                    }
//
//                    $request->session()->put("yzjUser", [
//                        'openId' => $openId,
//                        'phone' => $phone,
//                        'name' => $name,
//                        'photoUrl' => $photoUrl,
//                        'jobNo' => $jobNo,
//                    ]);


        }

        return session()->has('user');
    }

    public function guest()
    {
        // TODO: Implement guest() method.
    }

    // 获取当前的认证用户信息
    public function user()
    {
        return session()->get('user', []);
    }

    // 获取当前的认证用户ID
    public function id()
    {
        // TODO: Implement id() method.
        return session()->get('user')['id'];
    }

    public function validate(array $credentials = [])
    {
        // TODO: Implement validate() method.
    }

    public function hasUser()
    {
        // TODO: Implement hasUser() method.
    }

    public function setUser(Authenticatable $user)
    {
        // TODO: Implement setUser() method.
    }
}
