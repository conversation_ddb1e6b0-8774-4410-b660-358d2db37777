<?php
namespace App\Extensions\Auth;

//include app_path('Lib/dingding-sdk/TopSdk.php');

use App\Exceptions\DingdingLoginException;
use App\Exceptions\WechatOAuth2Exception;
use App\Exceptions\DingdingOAuth2Exception;

use App\Exceptions\WeixinDingdingAuthException;
use App\Exceptions\WeixinDingdingException;
use App\Exceptions\WeixinDingdingShipeiException;
use App\Models\DdAppshipei;
use App\Services\BumenService;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Arr;

use AlibabaCloud\SDK\Dingtalk\Voauth2_1_0\Dingtalk;
use \Exception;
use AlibabaCloud\Tea\Exception\TeaError;
use AlibabaCloud\Tea\Utils\Utils;

use Darabonba\OpenApi\Models\Config;
use AlibabaCloud\SDK\Dingtalk\Voauth2_1_0\Models\GetAccessTokenRequest;
use AlibabaCloud\SDK\Dingtalk\Voauth2_1_0\Models\GetUserTokenRequest;


use AlibabaCloud\SDK\Dingtalk\Vcontact_1_0\Dingtalk as Dingtalk20240606;
use AlibabaCloud\SDK\Dingtalk\Vcontact_1_0\Models\GetUserHeaders as GetUserHeaders20240606;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
use Illuminate\Support\Facades\Log;


class DingdingGuard implements Guard {

    // 确定当前用户是否已通过身份验证
    public function check()
    {

//        var_dump(Request::get('fromtype'));
//        exit();

        $corpid = Request::input('corpid');
        $corpid = ($corpid == '$CORPID$' ? '' : $corpid);
        $guard = Request::input('guard');
        $appid = Request::input('appid');
        $code = Request::input('code', '');

        if($appid == "074c6127-59c6-4179-bc1e-7f6ed7a3ed26") {
            Log::info("99997777" . date("Y-m-d H:i:s", time()) . "++ aa ++" . $corpid . "**" . $corpid . "**" . $guard . "**" . $appid . "**" . $code . "**" . "++ aa ++");
        }
        if (Request::has(['fromtype'])) {
            $fromtype = Request::input('fromtype', "");

            $ddAppShipei = DdAppshipei::where("appid", $appid)->first();
            if ($ddAppShipei) {

                if ($fromtype == 'pc' && !in_array($ddAppShipei['shipei'], [1,3])) {
                    throw new WeixinDingdingShipeiException("此轻应用不适配PC端请从移动端登录");
                }

                if ($fromtype == 'mobile' && !in_array($ddAppShipei['shipei'], [2,3])) {
                    throw new WeixinDingdingShipeiException("此轻应用不适配移动端请从PC端登录");
                }
            }
        }

        if (session()->missing("user") ||
            (Request::has(['guard', 'appid']) && !empty(Request::input('code')) && !Cache::has("DDCODE:" . Request::input('code')))    ) {



            if($appid == "074c6127-59c6-4179-bc1e-7f6ed7a3ed26") {
                Log::info("99997777" . date("Y-m-d H:i:s", time()) . "++ bb ++ zoujinpanduan ++ bb ++");
            }
//            echo "3322";



            if (empty($corpid) || empty($guard) || empty($appid)) {
                throw new WeixinDingdingException("weixindingding missing params, guard:{$guard}, appid:{$appid}, corpid:{$corpid}");
            }

            $appConfig = config("qingyingyong.{$guard}.{$corpid}.{$appid}", []);
            if (empty($appConfig)) {
                throw new WeixinDingdingException("weixindingding missing config, qingyingyong.{$guard}.{$corpid}.{$appid}");
            }

            if($appid == "074c6127-59c6-4179-bc1e-7f6ed7a3ed26") {
                Log::info("99997777" . date("Y-m-d H:i:s", time()) . "++ cc ++ tonguo code ++ cc ++");
            }
            // 鉴权
            if (empty($code)) {
                throw new WeixinDingdingAuthException("weixindingding missing params code");
            }


            Log::info("99997777".date("Y-m-d H:i:s",time())."++ dd ++ tonguo code ++ dd ++");

            if ($code) {


                $config = new Config([]);
                $config->protocol = "https";
                $config->regionId = "central";
                $dingtalk = new Dingtalk($config);

                $getAccessTokenRequest = new GetAccessTokenRequest([
                    "appKey" => $appConfig['clientid'], // "dingxss5qhqjzoi70crc",
                    "appSecret" => $appConfig['secret'], //"auufrN2LCENcmNYIleofPv5y8u9VxZVUkn0j2E7Rxww6Bf6KzhMY-GQx0ZDmtNJa"
                ]);


////                try {
//                    $getUserTokenRequest = new GetUserTokenRequest([
//                        "clientId" => "dingxss5qhqjzoi70crc", //"dingxxx",
//                        "clientSecret" => "auufrN2LCENcmNYIleofPv5y8u9VxZVUkn0j2E7Rxww6Bf6KzhMY-GQx0ZDmtNJa", //"1234",
//                        "code" => $code,
//                        //                    "refreshToken" => "abcd",
//                        "grantType" => "authorization_code"
//                    ]);
//                    $userToken = $dingtalk->getUserToken($getUserTokenRequest);
//                    var_dump($userToken);
////                } catch (Exception $err) {
////
////                        if (!($err instanceof TeaError)) {
////                            $err = new TeaError([], $err->getMessage(), $err->getCode(), $err);
////                        }
////                        if (!Utils::empty_($err->code) && !Utils::empty_($err->message)) {
////                            // err 中含有 code 和 message 属性，可帮助开发定位问题
////                        }
////                }
//                exit();


//                try {
                    // 获得 accessToken
                    // {"accessToken":"54446bfdf42d3449a71730476b5b14a5","expireIn":7200}
                    $accessToken = $dingtalk->getAccessToken($getAccessTokenRequest);
                    $accessToken = $accessToken->body;
//                }
//                catch (Exception $err) {
//                    if (!($err instanceof TeaError)) {
//                        $err = new TeaError([], $err->getMessage(), $err->getCode(), $err);
//                    }
//                    if (!Utils::empty_($err->code) && !Utils::empty_($err->message)) {
//                        // err 中含有 code 和 message 属性，可帮助开发定位问题
//                    }
//                }

//                // 20240606版新SDK
//                $config = new Config([]);
//                $config->protocol = "https";
//                $config->regionId = "central";
//                $dingtalk20240606 = new Dingtalk20240606($config);
//
//                $getUserHeaders20240606 = new GetUserHeaders20240606([]);
//                $getUserHeaders20240606->xAcsDingtalkAccessToken = $accessToken->accessToken;
//
//                $arr = $dingtalk20240606->getUserWithOptions("me", $getUserHeaders20240606, new RuntimeOptions([]));
//
//                var_dump($arr);
//                exit();

//                $getUserHeaders = new GetUserHeaders([]);
//                $getUserHeaders->xAcsDingtalkAccessToken = $accessToken->accessToken;
//
//                $arr = $dingtalk->getUserWithOptions("me", $getUserHeaders, new RuntimeOptions([]));
//
//                var_dump($arr);
//                exit();



                $client = new \GuzzleHttp\Client();
                // {"errcode":0,"errmsg":"ok","result":{"device_id":"5c7d15076ca0ec17e90fa2cea545f438","name":"\u5434","sys":true,"sys_level":1,"unionid":"ckFCvGcnlmtfW8Lo7kkwXgiEiE","userid":"***************"},"request_id":"16lw6rf4yafb1"}
                $response = $client->request('POST', "https://oapi.dingtalk.com/topapi/v2/user/getuserinfo?access_token=" . $accessToken->accessToken, [
                    'form_params' => [
                        'code' => $code,
                    ]
                ]);

                $content = $response->getBody()->getContents();
                $content = json_decode($content, true);
//                echo json_encode($content);
//                exit();



                // 获取详情
                // {"errcode":0,"errmsg":"ok","result":{"active":true,"admin":true,"avatar":"","boss":false,"create_time":"2021-06-02T01:25:25.000Z","dept_id_list":[1,*********,*********],"dept_order_list":[{"dept_id":*********,"order":176283789558140512},{"dept_id":1,"order":176295024654139512},{"dept_id":*********,"order":176283789558140512}],"email":"","exclusive_account":false,"hide_mobile":false,"job_number":"01","leader_in_dept":[{"dept_id":*********,"leader":false},{"dept_id":1,"leader":false},{"dept_id":*********,"leader":false}],"mobile":"***********","name":"\u5434","real_authed":true,"remark":"","role_list":[{"group_name":"\u5c97\u4f4d","id":**********,"name":"\u7ecf\u7406"},{"group_name":"\u5c97\u4f4d","id":**********,"name":"\u666e\u901a\u5458\u5de5"},{"group_name":"\u9ed8\u8ba4","id":**********,"name":"\u4e3b\u7ba1\u7406\u5458"}],"senior":false,"state_code":"86","telephone":"","title":"","union_emp_ext":[],"unionid":"ckFCvGcnlmtfW8Lo7kkwXgiEiE","userid":"***************","work_place":""},"request_id":"15r1i3y88vn91"}
                $response = $client->request('POST', "https://oapi.dingtalk.com/topapi/v2/user/get?access_token=" . $accessToken->accessToken, [
                    'form_params' => [
                        'language' => 'zh_CN',
                        'userid' => $content['result']['userid']
                    ]
                ]);
                $user = $response->getBody()->getContents();
                $user = json_decode($user, true);
//                echo json_encode($user);
//                exit();

//                if($appid == "0db4c33a-717f-4393-91b0-139c3add5074") {
//                    print_r($user);
//                    echo '---------获取session部分';
////                    die;
//                }

                $time = time();

//                echo json_encode($content);
//                echo "\n";
//                echo "\n";
//                echo json_encode($user);
//
//                exit();

                session()->put('user', [
                    'id' => $content['result']['userid'],
                    'userid' => $content['result']['userid'],
                    'name' => $content['result']['name'],
                    'guard' => $guard,
                    'appid' => $appid,
                    'corpid' => $corpid,
                    'code' => $code,
                    'unionid' => $content['result']['unionid'],
                    'job_number' => $user['result']['job_number']??'',
                    'mobile' => $user['result']['mobile']??'',
                    'login_at' => $time,
                    'refresh_at' => $time,
                ]);

//                var_dump(session()->get('user'));
//                exit();

                $bumenService = new BumenService();
                $xiaoqubianhaoList = [];

                try {
//                    echo "!11";
                    $xiaoqubianhaoList = $bumenService->getXiaoquQuanxian($user['result']['job_number']);
//                    echo "!222";
                } catch (\Exception $e) {
//                    var_dump($e->getMessage());
//                    exit();
                }

//                echo "sadfasdf";


                session()->put("yzjUser", [
                    'userid' => $content['result']['userid'],
                    'name' => $content['result']['name'],
                    'openId' => $content['result']['userid'],
                    'phone' => $user['result']['mobile']??'',
                    'name' => $content['result']['name'],
                    'photoUrl' => '',
                    'jobNo' => $user['result']['job_number']??'',
                    'xiaoqubianhaoList' => $xiaoqubianhaoList,
                ]);


//                var_dump(session()->get('yzjUser'));
//                exit();

//                    var_dump(session()->get("yzjUser"));

                if($appid == "074c6127-59c6-4179-bc1e-7f6ed7a3ed26") {
                    Log::info("99997777" . date("Y-m-d H:i:s", time()) . "++ xx ++ 走通 ++ xx ++");
                }

                Cache::add("DDCODE:{$code}", "1", 86400);


                //put('bar', 'baz', 600); // 10 分钟


            }
        }





//        echo "111asdf";
//        exit();


        return session()->has('user');
    }

    public function guest()
    {
        // TODO: Implement guest() method.
    }

    // 获取当前的认证用户信息
    public function user()
    {
        return session()->get('user', []);
    }

    // 获取当前的认证用户ID
    public function id()
    {
        // TODO: Implement id() method.
        return session()->get('user')['id'];
    }

    public function validate(array $credentials = [])
    {
        // TODO: Implement validate() method.
    }

    public function hasUser()
    {
        // TODO: Implement hasUser() method.
    }

    public function setUser(Authenticatable $user)
    {
        // TODO: Implement setUser() method.
    }
}
