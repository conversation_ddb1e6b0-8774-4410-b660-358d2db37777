<?php
namespace App\Extensions\Auth;

use App\Exceptions\WechatOAuth2Exception;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Arr;
use EasyWeChat\Work\Application;


class WechatGuard implements Guard{

    // 确定当前用户是否已通过身份验证
    public function check()
    {
        if (session()->missing("user") || Request::has(['corpid', 'guard', 'appid'])) {

            $corpid = Request::input('corpid');
            $guard = Request::input('guard');
            $appid = Request::input('appid');
            $code = Request::input('code', '');

            if (empty($guard) || empty($appid)) {
                throw new WechatOAuth2Exception("wechat missing params, guard:{$guard} or appid:{$appid} or corpid:{$corpid}");
            }

//            $appConfig = config("qingyingyong.{$guard}.{$corpid}.{$appid}", []);

            $appConfig = config("qingyingyong.{$guard}.{$corpid}.{$appid}", []);
            if (empty($appConfig)) {
                throw new WechatOAuth2Exception("wechat missing config, qingyingyong.{$guard}.{$corpid}.{$appid}");
            }

            $config = [
                'corp_id' => $appConfig['corpid'],
                'agent_id' => $appConfig['appid'],
                'secret' =>  $appConfig['secret'],
                'token' => 'easywechat',
                'aes_key' => '',
                'http' => [
                    'throw'  => true, // 状态码非 200、300 时是否抛出异常，默认为开启
                    'timeout' => 5.0,
                    'retry' => true, // 使用默认重试配置
                ]
            ];


            $app = new Application($config);



            // 鉴权
            if (empty($code)) {
                $callbackUrl = Request::url();
                $callbackParams = Request::toArray();

                $callbackUrl = $callbackUrl . "?" . Arr::query($callbackParams);
                $redirectUrl = $app->getOAuth()->redirect($callbackUrl);

//                var_dump($callbackUrl);
//                var_dump($redirectUrl);
//                exit();

                header("HTTP/1.1 302 Found");
                header("Location: {$redirectUrl}");
                return false;
            }


            if ($code) {

                $user = $app->getOAuth()->detailed()->userFromCode($code);

                // 获取用户信息
                $user->getId(); // 对应企业微信英文名（userid）
                $userRaw = $user->getRaw(); // 获取企业微信接口返回的原始信息

                session()->put('user', [
                    'id' => $userRaw['userid'],
                    'userid' => $userRaw['userid'],
                    'name' => $userRaw['name'],
                    'position' => $userRaw['position'],
                    'guard' => $guard,
                    'appid' => $appid,
                ]);
            }
        }

        return session()->has('user');
    }

    public function guest()
    {
        // TODO: Implement guest() method.
    }

    // 获取当前的认证用户信息
    public function user()
    {
        return session()->get('user', []);
    }

    // 获取当前的认证用户ID
    public function id()
    {
        // TODO: Implement id() method.
        return session()->get('user')['id'];
    }

    public function validate(array $credentials = [])
    {
        // TODO: Implement validate() method.
    }

    public function hasUser()
    {
        // TODO: Implement hasUser() method.
    }

    public function setUser(Authenticatable $user)
    {
        // TODO: Implement setUser() method.
    }
}
