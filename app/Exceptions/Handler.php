<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\Request;
use Throwable;
use Inertia\Inertia;

class Handler extends ExceptionHandler
{
    /**
     * A list of exception types with their corresponding custom log levels.
     *
     * @var array<class-string<\Throwable>, \Psr\Log\LogLevel::*>
     */
    protected $levels = [
        //
    ];

    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<\Throwable>>
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register()
    {
        $this->reportable(function (Throwable $e) {
            //
        });


        // 微信OAuth2 异常
        $this->renderable(function (WechatOAuth2Exception $e, Request $request) {
            return response()->view('Errors.WechatOAuth2Exception', [], 500);
        });

        // 钉钉OAuth2 异常
        $this->renderable(function (DingdingOAuth2Exception $e, Request $request) {
            return response()->view('Errors.DingdingOAuth2Exception', [], 500);
        });

        // 钉钉Login 异常
        $this->renderable(function (DingdingLoginException $e, Request $request) {

            return response()->view('Errors.DingdingLoginException', [], 500);
        });

        // 微信钉钉Login 异常
        $this->renderable(function (WeixinDingdingException $e, Request $request) {



            return response()->view('Errors.WeixinDingdingLoginException', [], 500);
        });


    }


    /**
     * 获取默认日志的上下文变量。
     *
     * @return array<string, mixed>
     */
    protected function context(): array
    {
        return array_merge(parent::context(), [
            'foo' => 'bar',
        ]);
    }


}
