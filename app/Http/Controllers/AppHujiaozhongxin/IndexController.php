<?php

namespace App\Http\Controllers\AppHujiaozhongxin;

use App\Http\Requests\StorePostRequest;
use App\Http\Resources\Yzj\YzjShenpiCollection;
use App\Lib\DemoInterface;
use App\Lib\DemoOne;
use App\Lib\DemoTwo;
use App\Lib\Kdd\CalcSign;
use App\Lib\Kdd\CommonConst;
use App\Models\Jdb\YzjDept;
use App\Models\YzjShenpi;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Facades\DemoInterfaceFacade;

class IndexController extends BaseController
{

    public function index(Request $request)
    {

//        $redirectUrl = "https://www.hik-cloud.com/safe-center/index.html#/login/retail";
        $redirectUrl = 'https://callserve.kidcastle.com.cn/';



        header("HTTP/1.1 302 Found");
        header("Location: {$redirectUrl}");
        return false;

        //return Inertia::render('AppKejizhongxin/Index', []);
    }
}
