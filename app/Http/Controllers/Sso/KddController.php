<?php

namespace App\Http\Controllers\Sso;

use App\Lib\Kdd\CalcSign;
use App\Lib\Kdd\CommonConst;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Log;

use function Psy\debug;

class KddController extends BaseController
{
    public function index(Request $request)
    {
        $yzjUser = $request->session()->get('yzjUser');
        $isToMobile = intval($request->input('isToMobile', 0));
        $isLang = $request->input('isLang', 'cn');

        $time = time();
        $client = new \GuzzleHttp\Client();

        $token = CalcSign::signData($time, 'RoleToKdd', '8888',CommonConst::SSO_KEY, CommonConst::SSO_IV);

        $form = [
            'timesteps' => $time,
            'apiuser_code' => 'RoleToKdd',
            'veytoken' => $token,
            'employeepid' => $yzjUser['jobNo'], //'**********',
            'isToMobile' => $isToMobile,
            'isLang' => $isLang,
        ];
//print_r($form);
        $response = $client->request('POST', 'https://api.kedingdang.com/ThirdParty/getThirdPartyLoginApi', [
                'form_params' => $form
            ]
        );

        $tokenContent = $response->getBody()->getContents();
        $tokenContent = json_decode($tokenContent, true);
//        if($isLang == 'tw' ){
//            print_r($tokenContent);
//            die;
//        }
        if ($tokenContent['error'] == 0) {
            if ($tokenContent['result']['list']['urlToKdd']) {
                // 跳转

//                Log::error(json_encode([
//                    'c' => $tokenContent,
//                ]));

                return redirect($tokenContent['result']['list']['urlToKdd']);
            }
        }



        return redirect('https://sclogin.kedingdang.com/login');
    }


    function general(Request $request){
print_r(11);
        // $yzjUser = $request->session()->get('yzjUser');
        // $isLang = $request->input('isLang', 'cn');

        // $time = time();
        // $client = new \GuzzleHttp\Client();

        // $token = CalcSign::signData($time, 'RoleToKdd', '8888',CommonConst::SSO_KEY, CommonConst::SSO_IV);

        // $form = [
        //     'timesteps' => $time,
        //     'apiuser_code' => 'RoleToKdd',
        //     'veytoken' => $token,
        //     'employeepid' => $yzjUser['jobNo'], //'**********',
        //     'isLang' => $isLang,
        //     'school_id' => $request->input('school_id', ''),
        //     'tourl' => $request->input('tourl', ''),
        // ];

        // $response = $client->request('POST', 'http://api.kcclassin.com/ThirdParty/getThirdPartyLoginInfoApi', [
        //         'form_params' => $form
        //     ]
        // );

        // $tokenContent = $response->getBody()->getContents();
        // $tokenContent = json_decode($tokenContent, true);

        // if ($tokenContent['error'] == 0) {
        //     if ($tokenContent['result']['list']['urlToKdd']) {

        //         return redirect($tokenContent['result']['list']['urlToKdd']);
        //     }
        // }



        // return redirect('https://sclogin.kedingdang.com/login');



    }



}
