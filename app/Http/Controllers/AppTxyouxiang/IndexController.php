<?php

namespace App\Http\Controllers\AppTxyouxiang;

use App\Http\Requests\StorePostRequest;
use App\Http\Resources\Yzj\YzjShenpiCollection;
use App\Lib\DemoInterface;
use App\Lib\DemoOne;
use App\Lib\DemoTwo;
use App\Lib\Kdd\CalcSign;
use App\Lib\Kdd\CommonConst;
use App\Models\Jdb\YzjDept;
use App\Models\YzjShenpi;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Facades\DemoInterfaceFacade;

class IndexController extends BaseController
{

    const CORPID = "wm748ed7f8ba0e927f";
    const SSO_SECRET = "oczm8_ZcKVl6WhKlrypmPZ237OuSH74Lv-4lsoZwKhYCzf6_OUU6fOX3ejArWoUO";


    public function index(Request $request)
    {

        $microtime = microtime(true);
        $client = new \GuzzleHttp\Client();

//        $yzjAppId = '*********';
//        $yzjAppSecret = 'ejLKU3ASsL1ulxpGBaWK';
//
//        $yzjResGroupSecret = 'YJpK1LHI20HGkEBrXU3xEDxrB47Dmsg0';

        try {

//            $ticket = $request->input('ticket', '');
//
//            if (empty($ticket)) throw new \Exception('ticket is empty');
//
//            $response = $client->request('POST', 'https://yunzhijia.com/gateway/oauth2/token/getAccessToken', [
//                    'form_params' => [
//                        'timestamp' => intval($microtime * 1000),
//                        'appId' => $yzjAppId,
//                        'secret' => $yzjAppSecret,
//                        'scope' => 'app',
//                        'eid' => '17916472',
//                    ]
//                ]
//            );
//            $accessToken = '';
//            $tokenContent = $response->getBody()->getContents();
//            $tokenContent = json_decode($tokenContent, true);
//            if ($tokenContent['errorCode'] == 0) {
//                $accessToken = $tokenContent['data']['accessToken'];
//            }
//
//            if (empty($accessToken)) throw new \Exception('accessToken is empty');
//
//
//
//            $ticketResponse = $client->request('POST', 'https://yunzhijia.com/gateway/ticket/user/acquirecontext?accessToken=' . $accessToken, [
//                'headers' => ['Content-Type' => 'application/json'],
//                'json' => [
//                    'eid' => '17916472',
//                    'ticket' => $ticket,
//                    'appid' => $yzjAppId,
//                ]
//            ]);
//
//            $openId = '';
//            $ticketResponse = $ticketResponse->getBody()->getContents();
//            $ticketResponse = json_decode($ticketResponse, true);
//            if ($ticketResponse['errorCode'] == 0) {
//                $openId = $ticketResponse['data']['openid'];
//            }
//            if (empty($openId)) throw new \Exception('openId is empty');
//
//            $resGroupTokenResponse = $client->request('POST', 'https://yunzhijia.com/gateway/oauth2/token/getAccessToken', [
//                    'form_params' => [
//                        'timestamp' => intval($microtime * 1000),
//                        'appId' => $yzjAppId,
//                        'secret' => $yzjResGroupSecret,
//                        'scope' => 'resGroupSecret',
//                        'eid' => '17916472',
//                    ]
//                ]
//            );
//            $resGroupToken = '';
//            $resGroupTokenResponse = $resGroupTokenResponse->getBody()->getContents();
//            $resGroupTokenResponse = json_decode($resGroupTokenResponse, true);
//            if ($resGroupTokenResponse['errorCode'] == 0) {
//                $resGroupToken = $resGroupTokenResponse['data']['accessToken'];
//            }
//
//
//            $userResponse = $client->request('POST', 'https://yunzhijia.com/gateway/openimport/open/person/get?accessToken=' . $resGroupToken, [
//                'headers' => ['Content-Type' => 'application/x-www-form-urlencoded'],
//                'form_params' => [
//                    'eid' => '17916472',
//                    'data' => json_encode(['type'=>1, 'array'=>[$openId]]),
//                ],
//            ]);
//
//            $phone = '';
//            $userResponse = json_decode($userResponse->getBody()->getContents(), true);
//            if ($userResponse['success'] == true) {
//                $phone = $userResponse['data'][0]['phone'];
//            }
//            if (empty($phone)) throw new \Exception('phone is empty');




            $phone = session()->get('user')['mobile'];


            $userDetailList = Cache::get('TXMAIL_USERDETAILLIST');
            if ($userDetailList) $userDetailList = json_decode($userDetailList, true);
            $userDetailList = $userDetailList ?? [];
            $txmailUserId = '';
            foreach ($userDetailList as $userDetail) {
                if ($userDetail['mobile'] == $phone) {
                    $txmailUserId = $userDetail['userid'];
                }
            }
            if (empty($txmailUserId)) throw new \Exception('txmailUserId is empty');


            $corpid='wm748ed7f8ba0e927f';
            $corpsecret='oczm8_ZcKVl6WhKlrypmPZ237OuSH74Lv-4lsoZwKhYCzf6_OUU6fOX3ejArWoUO';
            $mail = $txmailUserId;

            $get_access_token_url = 'https://api.exmail.qq.com/cgi-bin/gettoken?corpid='.$corpid.'&corpsecret='.$corpsecret;
            $response = $client->get($get_access_token_url);
            $access_token_json = json_decode($response->getBody()->getContents());
            $state= $access_token_json->errmsg;
            if($state == "ok"){//判断当前获取数据是否成功
                $access_token = $access_token_json->access_token;
                $login_url='https://api.exmail.qq.com/cgi-bin/service/get_login_url?access_token='.$access_token.'&userid='.$mail;
                $response = $client->get($login_url);
                $login_url_json = json_decode($response->getBody()->getContents());
                if($login_url_json->errmsg=="ok"){
                    header("location:".$login_url_json->login_url);
                    return ;
                }
            }

            throw new \Exception('end is failed');

        } catch (\Exception $e) {
            header("location:http://mail.kidcastle.cn/");
        }




//        $redirectUrl = "https://www.hik-cloud.com/safe-center/index.html#/login/retail";
//
//        header("HTTP/1.1 302 Found");
//        header("Location: {$redirectUrl}");
//        return false;

        //return Inertia::render('AppKejizhongxin/Index', []);
    }
}
