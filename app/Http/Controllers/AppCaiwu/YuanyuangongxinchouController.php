<?php

namespace App\Http\Controllers\AppCaiwu;

use App\Exports\CaiwuyuansyuangongxinchouExport;
use App\Http\Requests\AppCaiwu\YuanyuangongxinchouStoreRequest;
use App\Http\Requests\YzjCaiwuyuan\XinchouStoreRequest;
use App\Http\Resources\AppCaiwu\YuanyuangongxinchouCollection;
use App\Http\Resources\Yzj\YzjCaiwuyuan\QuanxianCollection;
use App\Http\Resources\Yzj\YzjCaiwuyuan\XinchouCollection;
use App\Http\Resources\AppCaiwu\YuanyuangongxinchousuodingCollection;
use App\Imports\CaiwuyuanyuangongxinchouImport;
use App\Imports\CaiwuyuanYusuanImport;
use App\Models\Caiwu\CaiwuYuangongxinchou;
use App\Models\Caiwu\CaiwuYuangongxinchousuoding;
use App\Models\Jdb\YzjCaiwuyuanShangchuanquanxian;
use App\Models\Jdb\YzjCaiwuyuanYuanxinchou;
use App\Services\Caiwu\CaiwuYuanyuangongxinchouService;
use App\Services\CaiwuyuanYusuanService;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Maatwebsite\Excel\Facades\Excel;


class YuanyuangongxinchouController extends BaseController
{

//    固定薪资 = 基本工资 + 职位工资 + 工龄工资 + 学历补贴 + 证书补贴 + 英语等级补贴 + 岗位津贴 + 交通津贴 + 住房津贴 + 带园津贴 + 其他津贴
//    浮动薪资 = 园内考核奖 + 集团考核奖 + 全勤奖 + 满园奖金 + 满班奖金 + 人头奖金 + 招生奖金 + 地推奖金 + 转介绍奖金 + 转制奖金 + 其他奖金或临时补贴 + 兴趣班工资 + 延时课工资
//    考勤扣款 = 考勤扣款 + 其他扣款
//    应付工资 = 固定薪资 + 浮动薪资 - 考勤扣款
//    实发工资 = 应付工资 - 社保个人 - 公积金个人 - 个税 - 税后扣款


    // 预算锁定权限
    static $yusuansuodingquanxian = [
        // 吴磊
        '**********' => ['suoding'=>1, 'fansuoding'=>1, 'queren'=>1, 'fanqueren'=>1, 'juesuansuoding'=>1, 'juesuanfansuoding'=>1, 'juesuanqueren'=>1, 'juesuanfanqueren'=>1],
        // 97 #97todo 修改吴磊 职工编号
        '0111810002' => ['suoding'=>1, 'fansuoding'=>1, 'queren'=>1, 'fanqueren'=>1, 'juesuansuoding'=>1, 'juesuanfansuoding'=>1, 'juesuanqueren'=>1, 'juesuanfanqueren'=>1],
        // 杜丹
        '0110210516' => ['suoding'=>1, 'fansuoding'=>1, 'queren'=>1, 'fanqueren'=>1, 'juesuansuoding'=>1, 'juesuanfansuoding'=>1, 'juesuanqueren'=>1, 'juesuanfanqueren'=>1],
        // 佳佳
        '0139810001' => ['suoding'=>0, 'fansuoding'=>0, 'queren'=>1, 'fanqueren'=>1, 'juesuansuoding'=>0, 'juesuanfansuoding'=>0, 'juesuanqueren'=>1, 'juesuanfanqueren'=>1],
        // Ashley
        '0110120138' => ['suoding'=>1, 'fansuoding'=>1, 'queren'=>0, 'fanqueren'=>0, 'juesuansuoding'=>1, 'juesuanfansuoding'=>1, 'juesuanqueren'=>0, 'juesuanfanqueren'=>0],
    ];

    public static function getYzjUser()
    {
        $yzjUser = session()->get('yzjUser');

//////        // TODO 测试
//        $yzjUser['jobNo'] = '**********';
//        $yzjUser['name'] = '吴磊';

        return $yzjUser;
    }

    public static function getQuanxian()
    {
        $yzjUser = self::getYzjUser();
        $quanxian = YzjCaiwuyuanShangchuanquanxian::getQuanxian($yzjUser['jobNo'], '园薪酬');
        return $quanxian;
    }



    public function index(XinchouStoreRequest $request)
    {
        $yzjUser = self::getYzjUser(); //$request->session()->get('yzjUser');
        $quanxian = self::getQuanxian(); //YzjCaiwuyuanShangchuanquanxian::getQuanxian($yzjUser['jobNo'], '园薪酬');
//        $quanxian = YzjCaiwuyuanShangchuanquanxian::getQuanxian('0110210516', '园薪酬');

        $time = strtotime("2024-09-01");
        $currentTime = time();


        $dateList = [];
        do {

            $time = mktime(0, 0, 0, date("m", $time)+1, date("d", $time), date("Y", $time));

            $nextDate = date("Y-m", $time);

            $dateList[] = ['label' => $nextDate, 'value'=>$nextDate];

        } while($time < $currentTime);



        return Inertia::render('AppCaiwu/Yuanyuangongxinchou/Index', [
            'yzjUser' => $yzjUser,
            // 吴迪,杜丹,吴磊,张佳佳,胡锦添 ,97   #97todo 修改吴磊 职工编号
            'chakanquanxian' => ['0110210322', '0110210516', '**********','0139810001', '0110210520', '0111810002'],
            'quanxian' => $quanxian,
            'dateList' => $dateList,
        ]);
    }

    // 薪酬列表
    public function list(Request $request)
    {
        $pageSize = $request->input('pageSize', 20);
        $xiaoquList = $request->input('codeList', []);
        $suoshuqiList = $request->input('suoshuqiList', []);
        $yuangongxingming = $request->input('yuangongxingming', '');
        $yuangongbianhao = $request->input('yuangongbianhao', '');

        $zaixitong = $request->input('zaixitong', []);
        $zaiyuansuo = $request->input('zaiyuansuo', []);
        $zhiweipipei = $request->input('zhiweipipei', []);
        $duojiayuanfaxin = $request->input('duojiayuanfaxin', []);
        $shifoulizhi = $request->input('shifoulizhi', []);





        $yzjUser = self::getYzjUser(); //$request->session()->get('yzjUser');
        $quanxian = self::getQuanxian(); // YzjCaiwuyuanShangchuanquanxian::getQuanxian($yzjUser['jobNo'], '园薪酬');
//         $quanxian = YzjCaiwuyuanShangchuanquanxian::getQuanxian('0110210516', '园薪酬');


        $list = CaiwuYuangongxinchou::where('id', "<>", "");

        if (empty($quanxian['list'])) {
            $list = $list->where('id','0');
        } else {
            $codeList = array_column($quanxian['list'], 'code');
            $list = $list->whereIn('系统编码', $codeList);
        }

        if (!empty($xiaoquList)) {
            $list = $list->whereIn('系统编码', $xiaoquList);
        }

        if (!empty($suoshuqiList)) {
            $list = $list->whereIn('发薪所属期', $suoshuqiList);
        }

        if (!empty($yuangongbianhao)) {
            $list = $list->where('员工编码',"LIKE", "%{$yuangongbianhao}%");

        }

        if (!empty($yuangongxingming)) {
            $list = $list->where('员工姓名','LIKE', "%{$yuangongxingming}%");
        }

        if (!empty($zaixitong)) {
            $list = $list->whereIn('是否在北森系统中', $zaixitong);
        }

        if (!empty($zaiyuansuo)) {
            $list = $list->whereIn('是否在北森系统中这家园所', $zaiyuansuo);
        }

        if (!empty($zhiweipipei)) {
            $list = $list->whereIn('是否和北森系统职位匹配', $zhiweipipei);
        }

        if (!empty($duojiayuanfaxin)) {
            $list = $list->whereIn('是否存在多家园所发薪', $duojiayuanfaxin);
        }

        if (!empty($shifoulizhi)) {
            $list = $list->whereIn('是否北森离职', $shifoulizhi);
        }

        $sumArr = $list->select(
            DB::raw("SUM(`合计固定薪资`) as '合计固定薪资'")
            ,DB::raw("SUM(`合计浮动薪资`) as '合计浮动薪资'")
            ,DB::raw("SUM(`合计扣款`) as '合计扣款'")
            ,DB::raw("SUM(`合计应付工资`) as '合计应付工资'")
            ,DB::raw("SUM(`合计实付工资`) as '合计实付工资'")
            ,DB::raw("SUM(`应付工资`) as '应付工资'")
            ,DB::raw("SUM(`实发工资`) as '实发工资'")

        )->first(); //sum('');

        $合计固定薪资 = $sumArr['合计固定薪资'];
        $合计浮动薪资 = $sumArr['合计浮动薪资'];
        $合计扣款 = $sumArr['合计扣款'];
        $合计应付工资 = $sumArr['合计应付工资'];
        $合计实付工资 = $sumArr['合计实付工资'];
        $应付工资 = $sumArr['应付工资'];
        $实发工资 = $sumArr['实发工资'];


        $list->select("*");


        $paginate = $list->paginate($pageSize);

        $xinchouCollection = new YuanyuangongxinchouCollection($paginate);

        $caiwuyuansuodingriqi = CaiwuYuangongxinchousuoding::select(['nianyue'])->where("yewutonglu", '园员工薪酬')->where('suodingrenId', "")->get();
        $caiwuyuansuodingriqiArr = $caiwuyuansuodingriqi->pluck('nianyue')->toArray();

        return $xinchouCollection->additional(['meta' => [
            '合计固定薪资' => sprintf("%.2f", $合计固定薪资),
            '合计浮动薪资' => sprintf("%.2f", $合计浮动薪资),
            '合计扣款' => sprintf("%.2f", $合计扣款),
            '合计应付工资' => sprintf("%.2f", $合计应付工资),
            '合计实付工资' => sprintf("%.2f", $合计实付工资),
            '应付工资' => sprintf("%.2f", $应付工资),
            '实发工资' => sprintf("%.2f", $实发工资),

            '锁定年月列表' => $caiwuyuansuodingriqiArr,
        ]]);
    }

    // 获取合计数
    public function hejishu(Request $request)
    {

    }



    // 保存
    public function store(YuanyuangongxinchouStoreRequest $request)
    {
        $yzjUser = self::getYzjUser(); // $request->session()->get('yzjUser');
        $quanxian = self::getQuanxian(); // YzjCaiwuyuanShangchuanquanxian::getQuanxian($yzjUser['jobNo'], '园薪酬');
//        $quanxian = YzjCaiwuyuanShangchuanquanxian::getQuanxian('0110210516', '园薪酬');

        if ($request->isMethod('post')) {
            $data = $request->validated();

            $caiwuyuansuodingriqi = CaiwuYuangongxinchousuoding::select(['nianyue'])->where("yewutonglu", '园员工薪酬')->where('suodingrenId', "")->get();
            $caiwuyuansuodingriqiArr = $caiwuyuansuodingriqi->pluck('nianyue')->toArray();
            if (!in_array($data['发薪所属期'], $caiwuyuansuodingriqiArr)) {
                return response()->json(
                    [
                        'error' => 1003,
                        'errorMsg'=>'',
                        'success' => false,
                        'data' => '',
                        'message' => $data['发薪所属期'] .'发薪所属期已锁定,如需解锁请联系佳佳或Ashley老师',
                    ]);
            }

            if (!isset($quanxian['list'][$data['系统编码']]) || empty($quanxian['list'][$data['系统编码']])) {
                return response()->json(
                    [
                        'error' => 1000,
                        'errorMsg'=>'',
                        'success' => false,
                        'data' => '',
                        'message' => $data['发薪所属期'] .':' . $data['系统编码'] . '权限不足',
                    ]);
            }

            $exists = CaiwuYuangongxinchou::where('发薪所属期', $data['发薪所属期'])->where('系统编码', $data['系统编码'])->where('员工编码', $data['员工编码'])->exists();
            if ($exists) {
                return response()->json(
                    [
                        'error' => 1001,
                        'errorMsg'=>'',
                        'success' => false,
                        'data' => '',
                        'message' => "发薪所属期:" . $data['发薪所属期'] .',系统编码:' . $data['系统编码'] . ",员工编码:" . $data['员工编码'] . '已存在'
                    ]);
            }


            try {

                // 开启事务
                DB::beginTransaction();

                // 锁
                CaiwuYuangongxinchousuoding::where("yewutonglu", '园员工薪酬')->where('nianyue', $data['发薪所属期'])->lockForUpdate()->get();


                $data['编制单位'] = $quanxian['list'][$data['系统编码']]['title'];
                $data['园所名称'] = $quanxian['list'][$data['系统编码']]['title'];

                //    固定薪资 = 基本工资 + 职位工资 + 工龄工资 + 学历补贴 + 证书补贴 + 英语等级补贴 + 岗位津贴 + 交通津贴 + 住房津贴 + 带园津贴 + 其他津贴
                //    浮动薪资 = 园内考核奖 + 集团考核奖 + 全勤奖 + 满园奖金 + 满班奖金 + 人头奖金 + 招生奖金 + 地推奖金 + 转介绍奖金 + 转制奖金 + 其他奖金或临时补贴 + 兴趣班工资 + 延时课工资
                //    考勤扣款 = 考勤扣款 + 其他扣款
                //    应付工资 = 固定薪资 + 浮动薪资 - 考勤扣款
                //    实发工资 = 应付工资 - 社保个人 - 公积金个人 - 个税 - 税后扣款

                $data['合计固定薪资'] = $data['基本工资'] + $data['职位工资'] + $data['工龄工资'] + $data['学历补贴'] + $data['证书补贴']
                    + $data['英语等级补贴'] + $data['岗位津贴'] + $data['交通津贴'] + $data['住房津贴'] + $data['带园津贴'] + $data['其他津贴'];
                $data['合计浮动薪资'] = $data['园内考核奖'] + $data['集团考核奖'] + $data['全勤奖'] + $data['满园奖金'] + $data['满班奖金']
                    + $data['人头奖金'] + $data['招生奖金'] + $data['地推奖金'] + $data['转介绍奖金'] + $data['转制奖金'] + $data['其他奖金或临时补贴'] + $data['兴趣班工资'] + $data['延时课工资'];

                $data['合计扣款'] = $data['考勤扣款'] + $data['其他扣款'];
                $data['合计应付工资'] = $data['合计固定薪资'] + $data['合计浮动薪资'] - $data['合计扣款'];
                $data['合计实付工资'] = $data['合计应付工资'] - $data['社保个人'] - $data['公积金个人'] - $data['个税'] - $data['税后扣款'];


                $data['合计固定薪资'] = round($data['合计固定薪资'], 2);
                $data['合计浮动薪资'] = round($data['合计浮动薪资'], 2);
                $data['合计扣款'] = round($data['合计扣款'], 2);
                $data['合计应付工资'] = round($data['合计应付工资'], 2);
                $data['合计实付工资'] = round($data['合计实付工资'], 2);

                $data['上传人姓名'] = $yzjUser['name']??'';
                $data['上传人员工编号'] = $yzjUser['jobNo']??'';

                $yzjJueseTongbu = CaiwuYuangongxinchou::create($data);

                // 提交事务
                DB::commit();
            } catch (\Exception $e) {

                // 发生错误，回滚事务
                DB::rollBack();

                return response()->json(
                    [
                        'error' => 1002,
                        'errorMsg'=>'',
                        'success' => false,
                        'data' => '',
                        'message' => $e->getMessage(),
                    ]);
            }

            return response()->json(
                [
                    'error' => 0,
                    'errorMsg'=>'',
                    'success' => true,
                    'data' => $yzjJueseTongbu,
                    'message' => 'ok',
                ]);
        }


    }



    public function update(YuanyuangongxinchouStoreRequest $request)
    {
        $yzjUser = self::getYzjUser(); // $request->session()->get('yzjUser');
        $quanxian = self::getQuanxian(); // YzjCaiwuyuanShangchuanquanxian::getQuanxian($yzjUser['jobNo'], '园薪酬');
//        $quanxian = YzjCaiwuyuanShangchuanquanxian::getQuanxian('0110210516', '园薪酬');


        if ($request->isMethod('put')) {
            $data = $request->validated();

            $gongyingshang = CaiwuYuangongxinchou::find($data['id']);

            if (empty($gongyingshang)) {
                return response()->json(
                    [
                        'error' => 1000,
                        'errorMsg'=>'',
                        'success' => false,
                        'data' => '',
                        'message' => $data['id'] . '不存在',
                    ]);
            }

            if ($gongyingshang['系统编码'] != $data['系统编码']
                || $gongyingshang['发薪所属期'] != $data['发薪所属期']
                ) {
                    return response()->json(
                        [
                            'error' => 1001,
                            'errorMsg'=>'',
                            'success' => false,
                            'data' => '',
                            'message' => "系统编码,发信所属期,员工编码不可更改",
                        ]);
            }

            try {

                // 开启事务
                DB::beginTransaction();

                CaiwuYuangongxinchousuoding::where("yewutonglu", '园员工薪酬')->where('nianyue', $gongyingshang['发薪所属期'])->lockForUpdate()->get();




                $data['合计固定薪资'] = $data['基本工资'] + $data['职位工资'] + $data['工龄工资'] + $data['学历补贴'] + $data['证书补贴']
                    + $data['英语等级补贴'] + $data['岗位津贴'] + $data['交通津贴'] + $data['住房津贴'] + $data['带园津贴'] + $data['其他津贴'];
                $data['合计浮动薪资'] = $data['园内考核奖'] + $data['集团考核奖'] + $data['全勤奖'] + $data['满园奖金'] + $data['满班奖金']
                    + $data['人头奖金'] + $data['招生奖金'] + $data['地推奖金'] + $data['转介绍奖金'] + $data['转制奖金'] + $data['其他奖金或临时补贴'] + $data['兴趣班工资'] + $data['延时课工资'];

                $data['合计扣款'] = $data['考勤扣款'] + $data['其他扣款'];
                $data['合计应付工资'] = $data['合计固定薪资'] + $data['合计浮动薪资'] - $data['合计扣款'];
                $data['合计实付工资'] = $data['合计应付工资'] - $data['社保个人'] - $data['公积金个人'] - $data['个税'] - $data['税后扣款'];

                $data['合计固定薪资'] = round($data['合计固定薪资'], 2);
                $data['合计浮动薪资'] = round($data['合计浮动薪资'], 2);
                $data['合计扣款'] = round($data['合计扣款'], 2);
                $data['合计应付工资'] = round($data['合计应付工资'], 2);
                $data['合计实付工资'] = round($data['合计实付工资'], 2);

                $data['zhuangtai'] = 0;

                $data['修改人员工编号'] = $yzjUser['jobNo']??'';
                $data['上传人姓名'] = $yzjUser['name']??'';

                $gongyingshang->fill($data);
                $gongyingshang->save();


                // 提交事务
                DB::commit();
            } catch (\Exception $e) {

                // 发生错误，回滚事务
                DB::rollBack();

                return response()->json(
                    [
                        'error' => 1002,
                        'errorMsg'=>'',
                        'success' => false,
                        'data' => '',
                        'message' => $e->getMessage(),
                    ]);
            }



            return response()->json(
                [
                    'error' => 0,
                    'errorMsg'=>'',
                    'success' => true,
                    'data' => $gongyingshang,
                    'message' => 'ok',
                ]);
        }


    }

    public function destroy(Request $request)
    {

        // TODO 判断权限
//        app.appcaiwu.yuanyuangongxinchou.destroy

        $yzjUser = self::getYzjUser(); // $request->session()->get('yzjUser');
        $quanxian = self::getQuanxian(); // YzjCaiwuyuanShangchuanquanxian::getQuanxian($yzjUser['jobNo'], '园薪酬');
//        $quanxian = YzjCaiwuyuanShangchuanquanxian::getQuanxian('0110210516', '园薪酬');

        if ($request->isMethod('delete')) {

            $data = $request->all();

            $caiwuyuangongxinchouModel = CaiwuYuangongxinchou::find($data['id']);
            if (empty($caiwuyuangongxinchouModel)) {
                return response()->json(
                    [
                        'error' => 1000,
                        'errorMsg'=>'',
                        'success' => false,
                        'data' => '',
                        'message' => $data['id'] . '不存在',
                    ]);
            }

            $caiwuyuansuodingriqi = CaiwuYuangongxinchousuoding::select(['nianyue'])->where("yewutonglu", '园员工薪酬')->where('suodingrenId', "")->get();
            $caiwuyuansuodingriqiArr = $caiwuyuansuodingriqi->pluck('nianyue')->toArray();
            if (!in_array($caiwuyuangongxinchouModel['发薪所属期'], $caiwuyuansuodingriqiArr)) {
                return response()->json(
                    [
                        'error' => 1003,
                        'errorMsg'=>'',
                        'success' => false,
                        'data' => '',
                        'message' => $caiwuyuangongxinchouModel['发薪所属期'] .'发薪所属期已锁定,如需解锁请联系佳佳或Ashley老师',
                    ]);
            }

            if (!isset($quanxian['list'][$caiwuyuangongxinchouModel['系统编码']]) || empty($quanxian['list'][$caiwuyuangongxinchouModel['系统编码']])) {
                return response()->json(
                    [
                        'error' => 1000,
                        'errorMsg'=>'',
                        'success' => false,
                        'data' => '',
                        'message' => $data['发薪所属期'] .':' . $data['系统编码'] . '权限不足',
                    ]);
            }

            try {

                // 开启事务
                DB::beginTransaction();

                CaiwuYuangongxinchousuoding::where("yewutonglu", '园员工薪酬')->where('nianyue', $caiwuyuangongxinchouModel['发薪所属期'])->lockForUpdate()->get();


//            CaiwuYuangongxinchou::find($data['id'])
                $caiwuyuangongxinchouModel->delete();

                // 提交事务
                DB::commit();

            } catch (\Exception $e) {

                // 发生错误，回滚事务
                DB::rollBack();

                return response()->json(
                    [
                        'error' => 1002,
                        'errorMsg'=>'',
                        'success' => false,
                        'data' => '',
                        'message' => $e->getMessage(),
                    ]);
            }




            return response()->json(
                [
                    'error' => 0,
                    'errorMsg'=>'',
                    'success' => true,
                    'data' => '',
                    'message' => 'ok',
                ]);

        }
    }



    // 锁定-列表
    public function suodingList(Request $request)
    {
        $yzjUser = self::getYzjUser(); // $request->session()->get('yzjUser');

//        $yzjUser['jobNo'] = '**********';

        $curPage = $request->input('curPage', 1);
        $pageSize = $request->input('pageSize', 20);


        // 读锁
        $yusuanService = new CaiwuYuanyuangongxinchouService();

        $lastmonth = mktime(0, 0, 0, date("m")+1, 1,   date("Y"));
        $suodingsModel = $yusuanService->getSuodingList("园员工薪酬", date("Y-m", $lastmonth));



        $suodingsModel = $suodingsModel->toArray();
        $paginate = array_slice($suodingsModel, ($curPage-1) * $pageSize, $pageSize);

        foreach ($paginate as $key=>$val) {

            $newCaozuo = [];
            $newJuesuanCaozuo = [];
            if (isset(self::$yusuansuodingquanxian[$yzjUser['jobNo']])) {

                // 锁定
                if (self::$yusuansuodingquanxian[$yzjUser['jobNo']]['suoding'] && empty($val['suodingAt'])) {
                    $newCaozuo[] = ['label' => '锁定', 'row'=>['id'=>$val['id'], 'caozuo'=>'suoding']];
                }
                // 反锁定
                if (self::$yusuansuodingquanxian[$yzjUser['jobNo']]['fansuoding'] && !empty($val['suodingAt']) && empty($val['querenrenAt'])) {
                    $newCaozuo[] = ['label' => '反锁定', 'row'=>['id'=>$val['id'], 'caozuo' =>'fansuoding']];
                }
                // 确认
                if (self::$yusuansuodingquanxian[$yzjUser['jobNo']]['queren'] && !empty($val['suodingAt']) && empty($val['querenrenAt'])) {
                    $newCaozuo[] = ['label' => '最终确认', 'row'=>['id'=>$val['id'], 'caozuo'=>'queren']];
                }
                // 反确认
                if (self::$yusuansuodingquanxian[$yzjUser['jobNo']]['fanqueren'] && !empty($val['suodingAt']) && !empty($val['querenrenAt'])) {
                    $newCaozuo[] = ['label' => '反最终确认', 'row'=>['id'=>$val['id'], 'caozuo'=>'fanqueren']];
                }

            }

            $paginate[$key]['caozuo'] = $newCaozuo;
            $paginate[$key]['juesuancaozuo'] = $newJuesuanCaozuo;

        }

        $page = new \Illuminate\Pagination\LengthAwarePaginator($paginate, count($suodingsModel), $pageSize, $curPage);

        return new YuanyuangongxinchousuodingCollection($page);
    }

    // 锁定-更新锁定
    public function suodingUpdate(Request $request)
    {

        if ($request->isMethod('put')) {
            $yzjUser = self::getYzjUser(); // $request->session()->get('yzjUser');
//            $yzjUser['jobNo'] = '**********';
//            $yzjUser['name'] = '吴磊';


            $id = $request->input("id", "");
            $caozuo = $request->input("caozuo", "");

            $success = false;
            $rs = false;
            $message = 'ok';
            if (self::$yusuansuodingquanxian[$yzjUser['jobNo']]
                && self::$yusuansuodingquanxian[$yzjUser['jobNo']][$caozuo] == 1) {

                $yusuanService = new CaiwuYuanyuangongxinchouService();

                $data = [];
                // 预算
                if ($caozuo == 'suoding') {
                    $data['suodingrenId'] = $yzjUser['jobNo'];
                    $data['suodingrenName'] = $yzjUser['name'];
                    $data['suodingAt'] = date("Y-m-d H:i:s");
                }
                if ($caozuo == 'fansuoding') {
                    $data['suodingrenId'] = '';
                    $data['suodingrenName'] = '';
                    $data['suodingAt'] = null;
                }
                if ($caozuo == 'queren') {
                    $data['querenrenId'] = $yzjUser['jobNo'];
                    $data['querenrenName'] = $yzjUser['name'];
                    $data['querenrenAt'] = date("Y-m-d H:i:s");
                }
                if ($caozuo == 'fanqueren') {
                    $data['querenrenId'] = '';
                    $data['querenrenName'] = '';
                    $data['querenrenAt'] = null;
                }

                $success = true;
                // 写锁
                $rs = $yusuanService->updateSuoding("园员工薪酬", $id, $data);
                if (!$rs) $message = "锁定失败";
            }

            return response()->json(
                [
                    'error' => 0,
                    'errorMsg'=>'',
                    'success' => $success,
                    'data' => $rs,
                    'message' => $message,
                ]
            );
        }
    }


    // 薪酬模板下载
    public function tpldownload(Request $request)
    {
        if ($request->isMethod('get')) {

            $xiaoqubianhao = $request->input('xiaoqubianhao', '');



            $yzjUser = self::getYzjUser();
            $quanxian = self::getQuanxian();
            if (!isset($quanxian['list'][$xiaoqubianhao])) {

                return response()->json(
                    [
                        'error' => 1000,
                        'errorMsg'=>'',
                        'success' => false,
                        'data' => '',
                        'message' => $xiaoqubianhao . '权限不足',
                    ]);
            }


            return Excel::download(new CaiwuyuansyuangongxinchouExport, "tpl_员工薪酬模板_{$xiaoqubianhao}.xlsx");
        }



    }

    // 薪酬模板导入
    public function tplimport(Request $request)
    {
        $yzjUser = self::getYzjUser(); // $request->session()->get('yzjUser');



//        $yzjUser = $request->session()->get('yzjUser');
        $quanxian = self::getQuanxian(); // YzjCaiwuyuanShangchuanquanxian::getQuanxian($yzjUser['jobNo'], '园薪酬');
//        $quanxian = YzjCaiwuyuanShangchuanquanxian::getQuanxian('0110210516', '园薪酬');

//        if (!isset($quanxian['list'][$caiwuyuangongxinchouModel['系统编码']]) || empty($quanxian['list'][$caiwuyuangongxinchouModel['系统编码']])) {



        $file = $request->file('file')->store('temp');
        $path = storage_path('app').'/'.$file;

        \Maatwebsite\Excel\Imports\HeadingRowFormatter::default('none');
        $headings = (new \Maatwebsite\Excel\HeadingRowImport)->toArray(($path));

        if (!(isset($headings[0][0][0]) && $headings[0][0][0] == '核算年份'
            &&  isset($headings[0][0][2]) && $headings[0][0][2] == '核算月份'
            &&  isset($headings[0][0][4]) && $headings[0][0][4] == '系统编码')) {
            return response(json_encode(['error'=>1001, 'errorMsg'=>"请上传正确模板文件，可在首页左上角找到按钮'下载薪酬模板'"]), 403);
        }


        $collection = (new CaiwuyuanyuangongxinchouImport)->toCollection($path);


        if (!isset($collection[0]) || empty($collection[0])) {
            return response(json_encode(['error'=>1000, 'errorMsg'=>"上传文件为空"]), 403);
        }
        $caiwuyuansuodingriqi = CaiwuYuangongxinchousuoding::select(['nianyue'])->where("yewutonglu", '园员工薪酬')->where('suodingrenId', "")->get();
        $caiwuyuansuodingriqiArr = $caiwuyuansuodingriqi->pluck('nianyue')->toArray();

        $suodingnianyue = [];
        $xitongbianmalist = [];
        $xitongbianma = null;
        $nianyue = null;

        try {
            foreach ($collection[0] as $row) {

                $row['核算年份'] = trim($headings[0][0][1]);
                $row['核算月份'] = trim($headings[0][0][3]);
                $row['系统编码'] = trim($headings[0][0][5]);

                $nian = intval(trim($row['核算年份']));
                $yue = intval(trim($row['核算月份']));
                $xitongbianma = trim($row['系统编码']);

                if (!(2024 <= $nian && $nian <= 2030)) {
                    throw new \Exception("年度{$nian}错误,请填写2024~2030区间年度");
                }
                if (!(1 <= $yue && $yue <= 12)) {
                    throw new \Exception("月份{$yue}错误,请填写1~12区间月份");
                }
                if (!isset($quanxian['list'][$xitongbianma])) {
                    throw new \Exception("系统编码{$xitongbianma}错误,请检查系统编码是否正确，确认没问题还是不能成功上传请联系杜丹开通相关园所权限");
                }

                $nianyue = ($nian . "-" . ($yue<10 ? ("0".$yue) : $yue));
                $suodingnianyue[$nianyue] = 1;
                $xitongbianmalist[$xitongbianma] = 1;
                if (!in_array($nianyue, $caiwuyuansuodingriqiArr)) {
                    throw new \Exception("年月{$nianyue}已锁定,如需解锁请联系张佳佳或Ashley");
                }

                if (empty($row['园所名称'])
                    && empty($row['员工编码'])
                    && empty($row['员工姓名'])
                    && empty($row['区域'])
                    && empty($row['职位名称'])) continue;


                $row['发薪所属期'] = $nianyue;
                $row['核算年份'] = $nian;
                $row['核算月份'] = $yue;
                $row['zhuangtai'] = 0;

                $data = $row->toArray();

                $data['合计固定薪资'] = doubleval($data['基本工资']) + doubleval($data['职位工资']) + doubleval($data['工龄工资']) + doubleval($data['学历补贴']) + doubleval($data['证书补贴'])
                    + doubleval($data['英语等级补贴']) + doubleval($data['岗位津贴']) + doubleval($data['交通津贴']) + doubleval($data['住房津贴']) + doubleval($data['带园津贴']) + doubleval($data['其他津贴']);
                $data['合计浮动薪资'] = doubleval($data['园内考核奖']) + doubleval($data['集团考核奖']) + doubleval($data['全勤奖']) + doubleval($data['满园奖金']) + doubleval($data['满班奖金'])
                    + doubleval($data['人头奖金'] )+ doubleval($data['招生奖金']) + doubleval($data['地推奖金']) + doubleval($data['转介绍奖金']) + doubleval($data['转制奖金']) + doubleval($data['其他奖金或临时补贴']) + doubleval($data['兴趣班工资']) + doubleval($data['延时课工资']);

                $data['合计扣款'] = doubleval($data['考勤扣款']) + doubleval($data['其他扣款']);
                $data['合计应付工资'] = ($data['合计固定薪资']) + ($data['合计浮动薪资']) - ($data['合计扣款']);
                $data['合计实付工资'] = ($data['合计应付工资']) - doubleval($data['社保个人']) - doubleval($data['公积金个人']) - doubleval($data['个税']) - doubleval($data['税后扣款']);


                $data['合计固定薪资'] = round($data['合计固定薪资'], 2);
                $data['合计浮动薪资'] = round($data['合计浮动薪资'], 2);
                $data['合计扣款'] = round($data['合计扣款'], 2);
                $data['合计应付工资'] = round($data['合计应付工资'], 2);
                $data['合计实付工资'] = round($data['合计实付工资'], 2);


                $row['合计固定薪资'] = $data['合计固定薪资'];
                $row['合计浮动薪资'] = $data['合计浮动薪资'];
                $row['合计扣款'] = $data['合计扣款'];
                $row['合计应付工资'] = $data['合计应付工资'];
                $row['合计实付工资'] = $data['合计实付工资'];

                $row['编制单位'] = $quanxian['list'][$xitongbianma]['title'];



                $row['上传人员工编号'] = $yzjUser['jobNo']??'';
                $row['上传人姓名'] = $yzjUser['name']??'';

            }

            if (empty($suodingnianyue)) {
                throw new \Exception("年月字段不能为空");
            }

        } catch (\Exception $e) {
            return response(json_encode(['error'=>$e->getCode(), 'errorMsg'=>$e->getMessage()]), 403);
        }



        try {
            DB::beginTransaction();

            // 锁
            CaiwuYuangongxinchousuoding::where("yewutonglu", '园员工薪酬')->where('nianyue', array_keys($suodingnianyue))->lockForUpdate()->get();



            // CaiwuYuangongxinchou

//            $row['发薪所属期'] = $nianyue;
//            $row['核算年份'] = $nian;
//            $row['核算月份'] = $yue;
//            $row['系统编码']

            // UNIQUE KEY `t_caiwu_yuanyuangongxinchoumingxi_pk` (`发薪所属期`,`系统编码`,`员工编码`),



            CaiwuYuangongxinchou::where('发薪所属期', $row['发薪所属期'])->where('系统编码', $row['系统编码'])->delete();


            $caiwuYuangongxinchouModel = new CaiwuYuangongxinchou();

            $collection[0]->chunk(1)->each(function($items) use ($caiwuYuangongxinchouModel) {
                $rows = $items->toArray();

                // 发薪所属期', '系统编码', '员工编码
                CaiwuYuangongxinchou::upsert(
                    $rows,
                    ['发薪所属期', '系统编码', '员工编码'],
                    $caiwuYuangongxinchouModel->fillable
                );
            });

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();

            return response(json_encode(['error'=>$e->getCode(), 'errorMsg'=>$e->getMessage()]), 403);
        }



        $totalData = CaiwuYuangongxinchou::where('id', "<>", "");
        $totalData = $totalData->whereIn('系统编码', [$xitongbianma]);
        $totalData = $totalData->whereIn('发薪所属期', [$nianyue]);
        $sumArr = $totalData->select(
            DB::raw("SUM(`合计固定薪资`) as '合计固定薪资'")
            ,DB::raw("SUM(`合计浮动薪资`) as '合计浮动薪资'")
            ,DB::raw("SUM(`合计扣款`) as '合计扣款'")
            ,DB::raw("SUM(`合计应付工资`) as '合计应付工资'")
            ,DB::raw("SUM(`合计实付工资`) as '合计实付工资'")
            ,DB::raw("SUM(`应付工资`) as '应付工资'")
            ,DB::raw("SUM(`实发工资`) as '实发工资'")
            ,DB::raw("COUNT(`id`) as '人数'")


        )->first();


        return response()->json(
            [
                'error' => 0,
                'errorMsg'=>'',
                'success' => true,
                'data' => [
                    'sumArr' => $sumArr,
                    'xitongbianma' => $xitongbianma,
                    'nianyue'=>  $nianyue,
                ],
                'message' => '',
            ]);
    }


}


