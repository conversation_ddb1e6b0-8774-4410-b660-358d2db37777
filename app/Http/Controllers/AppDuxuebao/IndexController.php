<?php

namespace App\Http\Controllers\AppDuxuebao;

use App\Http\Resources\AppDdshipei\DdshipeiCollection;
use App\Models\DdAppshipei;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Inertia\Inertia;
use App\Facades\DemoInterfaceFacade;

class IndexController extends BaseController
{

    public function index(Request $request)
    {

        $yzjUser = $request->session()->get('yzjUser');
//        $quanxian = YzjCaiwuyuanShangchuanquanxian::getQuanxian($yzjUser['jobNo'], '员工花名册');

        return Inertia::render('AppDdshipei/Index', [
//            'yzjUser' => $yzjUser,
//            'quanxian' => $quanxian,
        ]);
    }

    // 列表
    public function list(Request $request)
    {
        if ($request->isMethod('get')) {

            $yzjUser = $request->session()->get('yzjUser');

//            $quanxian = YzjCaiwuyuanShangchuanquanxian::getQuanxian($yzjUser['jobNo'], '员工花名册');
//            $codeList = array_column($quanxian['list'], "code");


            $yuangongList = DdAppshipei::orderByDesc('id')->paginate(20);

            return new DdshipeiCollection($yuangongList);
        }

    }

    // 保存创建
    public function store(Request $request)
    {
        if ($request->isMethod('post')) {

            // {"openId":"60e26896e4b01596045c9c38","phone":"18616126092","name":"\u5434\u78ca","photoUrl":"https:\/\/static.yunzhijia.com\/space\/c\/photo\/load?id=62c6788eec9ecd0001d7e347","jobNo":"0110210366","xiaoqubianhaoList":["01307","01412"]}
            $yzjUser = $request->session()->get('yzjUser');

            $yuangong = $request->get("yuangong", []);

            $ddShipeiModel = DdAppshipei::create($yuangong);

            return response()->json(
                [
                    'error' => 0,
                    'errorMsg'=>'',
                    'success' => true,
                    'data' => [],
                    'message' => 'ok',
                ]
            );
        }
    }

    public function update(Request $request)
    {
        if ($request->isMethod('put')) {
            // {"openId":"60e26896e4b01596045c9c38","phone":"18616126092","name":"\u5434\u78ca","photoUrl":"https:\/\/static.yunzhijia.com\/space\/c\/photo\/load?id=62c6788eec9ecd0001d7e347","jobNo":"0110210366","xiaoqubianhaoList":["01307","01412"]}
            $yzjUser = $request->session()->get('yzjUser');

            $yuangong = $request->get("yuangong", []);


            $appShipeiModel = DdAppshipei::find($yuangong['id']);

            if ($appShipeiModel) {

                $appShipeiModel['mingcheng'] = $yuangong['mingcheng'];
                $appShipeiModel['shipei'] = $yuangong['shipei'];
                $appShipeiModel['appid'] = $yuangong['appid'];
                $appShipeiModel->save();
            }

            return response()->json(
                [
                    'error' => 0,
                    'errorMsg'=>'',
                    'success' => true,
                    'data' => [],
                    'message' => 'ok',
                ]
            );
        }
    }

    public function destroy(Request $request)
    {
        if ($request->isMethod('delete')) {
            $id = $request->get("id");

            $deleted = DdAppshipei::where('id', $id)->delete();

            return response()->json(
                [
                    'error' => 0,
                    'errorMsg'=>'',
                    'success' => true,
                    'data' => [],
                    'message' => 'ok',
                ]
            );

        }
    }


//    // 权限列表
//    public function quanxianList(Request $request)
//    {
//        $yzjUser = $request->session()->get('yzjUser');
//        // $yzjUser['jobNo'] = '3120110055';
//        $quanxian = YzjCaiwuyuanShangchuanquanxian::getQuanxian($yzjUser['jobNo'], '员工花名册');
//        $curPage = $request->input('curPage', 1);
//        $pageSize = $request->input('pageSize', 20);
//        $xiaoquList = $request->input('codeList', []);
//
//        if (!isset($quanxian['list']) || empty($quanxian['list'])) {
//            $quanxian['list'] = [];
//        }
//        $quanxianList = array_slice($quanxian['list'], ($curPage-1) * $pageSize, $pageSize);
//
//        $list = [];
//        foreach ($quanxianList as $key=>$val) {
//            $list[] = [
//                '员工编号' => $quanxian['jobNo'],
//                '员工姓名' => $quanxian['name'],
//                '园编码' => $val['code'],
//                '园名称' => $val['title'],
//            ];
//        }
//
//        $page = new \Illuminate\Pagination\LengthAwarePaginator($list, count($quanxian['list']), $pageSize, $curPage);
//
//        return new QuanxianCollection($page);
//    }
}
