<?php

namespace App\Http\Controllers\AppDuxuebao;

use App\Http\Resources\AppDdshipei\DdshipeiCollection;
use App\Models\DdAppshipei;
use App\Services\Yxt\YxtApiService;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Inertia\Inertia;
use App\Facades\DemoInterfaceFacade;

class QrcodeController extends BaseController
{


    // 二维码扫码
    public function index(Request $request)
    {
        $returnurl = $request->input('returnurl', '');

        $redirecturl = "https://eduappv2.kidcastle.com.cn/app/duxuebao/qrcode/qrcodejump?corpid=ding5127d720688b2993ee0f45d8e4f7c288&guard=dingding&appid=6501f798-9551-47d7-9216-5a7d2205eff5&returnurl=" . urlencode($returnurl);

        return Inertia::render('AppDuxuebao/Qrcode', [
            'returnurl' => urlencode($returnurl),
            'redirecturl' => $redirecturl,
            ]);
    }


    // 二维码跳转
    public function qrcodejump(Request $request)
    {
        $returnurl = $request->input('returnurl', '');
        $yzjUser = $request->session()->get('yzjUser');
        $jobNumber = $yzjUser['jobNo'];

        $result = YxtApiService::h5sso($jobNumber);
        if (isset($result['url']) && !empty($result['url'])) {
            $ssoh5url = $result['url'];
        }

        if ($ssoh5url) $ssoh5url = $ssoh5url . "&returnurl=" . urlencode($returnurl);

        $url = $ssoh5url;
        if (!empty($url)) {
            echo "<script>window.location.href=\"{$url}\"</script>";
        }
    }

}
