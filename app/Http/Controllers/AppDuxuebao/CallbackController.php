<?php

namespace App\Http\Controllers\AppDuxuebao;


use App\Models\DdYuangong;
use App\Models\Yxt\YxtCallback;
use App\Services\Dd\DdApiService;
use App\Services\Yxt\YxtApiService;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Inertia\Inertia;


class CallbackController extends BaseController
{

    public function index(Request $request)
    {

        $content = $request->getContent();

        $msg = json_decode($content, true);

        $msgText = explode("\n",$msg['msgText']);
        array_unshift($msgText, "### 笃学堡 ###");


        $msgData['appUrl'] = $msg['appUrl'] ?? '';
        $msgData['pcUrl'] = $msg['pcUrl'] ?? '';
        $msgData['ddAppUrl'] = $msg['ddAppUrl'] ?? '';
        $msgData['ddPcUrl'] = $msg['ddPcUrl'] ?? '';
        $msgData['from'] = $msg['from'] ?? '';
        $msgData['msgHtml'] = $msg['msgHtml'] ?? '';
        $msgData['msgText'] = $msg['msgText'] ?? '';
        $msgData['sendTime'] = $msg['sendTime'] ?? '';
        $msgData['title'] = $msg['title'] ?? '';
        $msgData['to'] = $msg['to'] ?? '';
        $msgData['type'] = $msg['type'] ?? '';
        $msgData['userName'] = $msg['userName'] ?? '';
        $msgData['userNo'] = $msg['userNo'] ?? '';
        $msgCallbackModel = YxtCallback::create($msgData);


        $ddYuangongModel = DdYuangong::where('job_number', $msg['userNo'])->first();




        $ddApiService = new DdApiService('6501f798-9551-47d7-9216-5a7d2205eff5');

//        $yxtUrl = 'https://study.kidcastle.com.cn/sty/mystudytask.htm';
//        $yxtUrl = urlencode($yxtUrl);

        $callbackUrl = "https://eduappv2.kidcastle.com.cn/app/duxuebao/callbackmsgjump?corpid=ding5127d720688b2993ee0f45d8e4f7c288&guard=dingding&appid=6501f798-9551-47d7-9216-5a7d2205eff5&fromtype=pc&callbackid=" . $msgCallbackModel->id;

        //. "&appUrl=" . urlencode($msg['appUrl']);

        $callbackUrl = urlencode($callbackUrl);

        $url = "dingtalk://dingtalkclient/action/openapp?corpid=ding5127d720688b2993ee0f45d8e4f7c288&container_type=work_platform&app_id=3158384419&redirect_type=jump&redirect_url=" . $callbackUrl;


        // $rs = $ddApiService->msgSendForYunxuetang('0618223132699158', $msg['title'], implode("\n\n", $msgText), $url);


        $rs = $ddApiService->msgSendForYunxuetang($ddYuangongModel['userid'], $msg['title'], implode("\n\n", $msgText), $url);



        $msgCallbackModel['rep'] = $rs;
        $msgCallbackModel->save();



    }

    // dd消息免登跳转
    public function msgjump(Request $request)
    {
        $guard = $request->input('guard');
        $corpid = $request->input('corpid');
        $appid = $request->input('appid');
        $callbackId = $request->input('callbackid', '');


        $yxtCallbackModel = YxtCallback::find($callbackId);


        $yzjUser = $request->session()->get('yzjUser');
        $jobNumber = $yzjUser['jobNo'];

        $ssopcurl = "";
        $ssoh5url = "";


        $result = YxtApiService::pcsso($jobNumber);
        if ($result['code'] == 0) {
            $ssopcurl = $result['data'];
        }

        $result = YxtApiService::h5sso($jobNumber);
        if (isset($result['url']) && !empty($result['url'])) {
            $ssoh5url = $result['url'];
        }

        if ($ssopcurl) $ssopcurl = $ssopcurl . "&fromurl=" . urlencode($yxtCallbackModel['pcUrl']);
        if ($ssoh5url) $ssoh5url = $ssoh5url . "&returnurl=" . urlencode($yxtCallbackModel['appUrl']);



        return Inertia::render('AppDuxuebao/MsgJump', ['guard'=>$guard, 'corpid' => $corpid, 'appid'=>$appid, 'ssopcurl'=>$ssopcurl, 'ssoh5url'=>$ssoh5url]);

    }



}
