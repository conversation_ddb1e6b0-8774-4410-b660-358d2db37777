<?php

namespace App\Http\Controllers\AppK3;

use App\Http\Requests\StorePostRequest;
use App\Http\Resources\Yzj\YzjShenpiCollection;
use App\Lib\DemoInterface;
use App\Lib\DemoOne;
use App\Lib\DemoTwo;
use App\Lib\Kdd\CalcSign;
use App\Lib\Kdd\CommonConst;
use App\Models\Jdb\YzjDept;
use App\Models\K3Dengluzhanghao;
use App\Models\K3Log;
use App\Models\YzjShenpi;
use App\Services\AescbcService;
use App\Services\K3\K3Service;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Facades\DemoInterfaceFacade;

class IndexController extends BaseController
{

    public function index(Request $request)
    {
        $user = $request->session()->get('user');
        $yzjUser = $request->session()->get('yzjUser');
        $formid = $request->get('formid', '');
        $formtype = $request->get('formtype', '');


        $k3Service = new K3Service();

        $k3DengluzhanghaoModel = K3Dengluzhanghao::where("FUSERACCOUNT", "<>", "")->where("FUSERACCOUNT", $yzjUser['jobNo'])->first();

        $account = "无此账号";
        if (!empty($k3DengluzhanghaoModel)) {
            $account = $k3DengluzhanghaoModel->FNAME;
        }

        $udData = $k3Service->calcUd(['username'=>$account, 'formid' => $formid, 'formtype' => $formtype]);

        $ud = base64_encode(json_encode($udData));

        K3Log::create([
            'jobNo'=>$yzjUser['jobNo'],
            'name' => $yzjUser['name'],
            'k3account' => $account,
            'ud' => ($udData),
        ]);

        // 測試服
        //$redirectUrl = 'http://47.96.36.32:55520/k3cloud/html5/index.aspx?ud=' . $ud;
        // 正式服
//        if ($formtype == 'mobile') {
//
//            $redirectUrl = 'https://k3.kidcastle.com.cn/k3cloud/xmobile/cloud.html?ud=' . $ud . "&entryRole=xt&formId=ER_MBReimb_HomePage&formType=mobile&acctid=5ab0ff35434cf7";
//
////            $redirectUrl = 'https://k3.kidcastle.com.cn/k3cloud/html5/dform.aspx?ud=' . $ud;
//
//        } else {
            $redirectUrl = 'https://k3.kidcastle.com.cn/K3Cloud/html5/index.aspx?ud=' . $ud;
//        }

        header("HTTP/1.1 302 Found");
        header("Location: {$redirectUrl}");
        return false;
    }
}
