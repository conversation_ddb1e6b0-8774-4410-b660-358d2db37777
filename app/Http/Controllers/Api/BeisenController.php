<?php


namespace App\Http\Controllers\Api;

use App\Http\Requests\Api\Dtb\PersonRequest;
use App\Lib\Kdd\Aescbc;
use App\Lib\Kdd\CalcSign;
use App\Lib\Kdd\CommonConst;
use App\Models\Dtb\DtbJuese;
use App\Models\Dtb\DtbJueseEntry;
use App\Models\Fanwei\Hrmresource;
use App\Models\Jdb\YzjDept;
use App\Models\Jdb\YzjPerson;
use App\Models\Jdb\YzjPersonJob;
use App\Services\BeisenApi\BeisenService;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Log;


class BeisenController extends BaseController
{

    // 获取北森主职信息-97调用
    public function zhuzhixinxi(Request $request)
    {
        $mobile = $request->input('mobile', '');
        $jobNumber = $request->input('jobNumber', '');

        if (empty($mobile) && empty($jobNumber)) {
            return response()->json(
                [
                    'error' => 10001,
                    'errorMsg'=>'',
                    'success' => false,
                    'data' => [],
                    'message' => "mobile和jobNumber不能同时为空",
                ]
            );
        }

        $beisenService = new BeisenService();

        $beisenYuangongsModel = $beisenService->getBeisenYuangongModel();
        if (!empty($mobile)) {
            $yuangongModel = $beisenYuangongsModel->where('t_beisen_yuangong.mobilePhone', $mobile)->where('serviceType', 0)->first();
        } else {
            $yuangongModel = $beisenYuangongsModel->where('t_beisen_yuangong_renzhi.jobNumber', $jobNumber)->where('serviceType', 0)->first();
        }

        if (empty($yuangongModel)) {
            return response()->json(
                [
                    'error' => 10002,
                    'errorMsg'=>'',
                    'success' => false,
                    'data' => [],
                    'message' => "未找到北森用户mobile:{$mobile}",
                ]
            );
        }


        return response()->json(
            [
                'error' => 0,
                'errorMsg'=>'',
                'success' => true,
                'data' => [
                    'user' => [
                        'mobile' => $yuangongModel->mobilePhone,
                        'zhiwuname' => $yuangongModel->zhiwuname,
                        'name' => $yuangongModel->name,
                        'jobNumber' => $yuangongModel->jobNumber,
                    ]
                ],
                'message' => "ok",
            ]
        );

    }



//    // 获取泛微userid
//    public function huoqufanweiuserid(Request $request)
//    {
//        $jobNumber = $request->input('jobnumber', '');
//
//        if (empty($jobNumber)) {
//            return response()->json(
//                [
//                    'error' => 10001,
//                    'errorMsg'=>'',
//                    'success' => false,
//                    'data' => [],
//                    'message' => "jobnumber不能为空",
//                ]
//            );
//        }
//
//
//
//        $hrmresourceModel = Hrmresource::where('WORKCODE', $jobNumber)->where('STATUS', "!=", "5")->first();
//
////        echo json_encode($hrmresourceModel);
//
//
//
//        if (empty($hrmresourceModel)) {
//            return response()->json(
//                [
//                    'error' => 10002,
//                    'errorMsg'=>'',
//                    'success' => false,
//                    'data' => [],
//                    'message' => "未找到泛微用户jobNumber:{$jobNumber}",
//                ]
//            );
//        }
//
//        return response()->json(
//            [
//                'error' => 0,
//                'errorMsg'=>'',
//                'success' => true,
//                'data' => ['fanweiUserId' => $hrmresourceModel['ID'], 'jobtitle'=>$hrmresourceModel['JOBTITLE'], 'department' => $hrmresourceModel['DEPARTMENTID']],
//                'message' => "ok",
//            ]
//        );
//
//    }

}
