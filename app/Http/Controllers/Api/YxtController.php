<?php

namespace App\Http\Controllers\Api;

use AlibabaCloud\OpenApiUtil\Tests\ParseModel;
use App\Lib\Kdd\Aescbc;
use App\Lib\Kdd\CommonConst;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;

class YxtController extends BaseController
{


    // 单点登录-获取单点登录信息
    public function huoqudandiandengluxinxi(Request $request)
    {
        $token = $request->input('token', '');
        $token = Aescbc::decryptWithOpenssl($token, CommonConst::KDD_KEY, CommonConst::KDD_IV);

        if (!$token) {
            return response()->json(
                [
                    'error' => 10001,
                    'errorMsg'=>'',
                    'success' => false,
                    'data' => [],
                    'message' => "token 不能为空",
                ]
            );

//            // return "token 错误，请重新传入正确token。";
//            //header("HTTP/1.1 404 Not Found");
//            header("location:https://study.kidcastle.com.cn");
//            return;
        }
        $token = json_decode($token, true);
        if (!$token) {
            return response()->json(
                [
                    'error' => 10002,
                    'errorMsg'=>'',
                    'success' => false,
                    'data' => [],
                    'message' => "token 解析错误",
                ]
            );
            //  return "token 错误，请重新传入正确token2。";
            //header("HTTP/1.1 404 Not Found");
//            header("location:https://study.kidcastle.com.cn");
//            return;
        }

        // 登录账号
        $account = $token['account'] ?? '';
        // 路径
        $path = $token['path'] ?? '';
        // 时间
        $timestamp = $token['timestamp'] ?? 0; //$request->input('params', []);
        $time = time();
        if (!(($time - 300) <= $timestamp && $timestamp <= ($time + 300))) {
            return response()->json(
                [
                    'error' => 10003,
                    'errorMsg'=>'',
                    'success' => false,
                    'data' => [],
                    'message' => "时间戳记超时",
                ]
            );

            // 时间戳记超过5分钟
//            header("HTTP/1.1 404 Not Found");
            // header("status: 404 not found");
//            header("location:https://study.kidcastle.com.cn");
//
//            return;
        }


        $client = new \GuzzleHttp\Client();

        try {

            $postData = \App\Http\Controllers\Sso\YxtController::calcSignature();
            $postData['uname'] = $account;
            $pcSsoUrl = "http://apic1.yunxuetang.cn/el/sso";
            $response = $client->request('POST', $pcSsoUrl, [
                'headers' => ['Content-Type' => 'application/json'],
                'json' => $postData
            ]);

            $response = json_decode($response->getBody()->getContents(), true);


            $url = 'https://study.kidcastle.com.cn';
            if ($response['code'] == 0) {
                $url = $response['data'];

                if (!empty($path)) {
                    if (strpos($url, '?') === false) {
                        $url .= "?fromurl=" . urlencode($path);
                    } else {
                        $url .= "&fromurl=" . urlencode($path);
                    }
                }
            }

            if (!empty($url)) {

                return response()->json(
                    [
                        'error' => 0,
                        'errorMsg'=>'',
                        'success' => true,
                        'data' => [
                            'yxturl' => $url,
                        ],
                        'message' => "ok",
                    ]
                );

//                echo "<script>window.location.href=\"{$url}\"</script>";
//                return;
            }

        } catch(Exception $e) {

            return response()->json(
                [
                    'error' => 10004,
                    'errorMsg'=>'',
                    'success' => false,
                    'data' => [],
                    'message' => "登录失败请联系管理员",
                ]
            );

//            header("location:https://study.kidcastle.com.cn");
//            return;
        }

    }



    // 单点登录-自跳转
    public function sso(Request $request)
    {
        $token = $request->input('token', '');
        $token = Aescbc::decryptWithOpenssl($token, CommonConst::KDD_KEY, CommonConst::KDD_IV);

        if (!$token) {
            // return "token 错误，请重新传入正确token。";
            //header("HTTP/1.1 404 Not Found");
            header("location:https://study.kidcastle.com.cn");
            return;
        }
        $token = json_decode($token, true);
        if (!$token) {
            //  return "token 错误，请重新传入正确token2。";
            //header("HTTP/1.1 404 Not Found");
            header("location:https://study.kidcastle.com.cn");
            return;
        }

        // 登录账号
        $account = $token['account'] ?? '';
        // 路径
        $path = $token['path'] ?? '';
        // 时间
        $timestamp = $token['timestamp'] ?? 0; //$request->input('params', []);
        $time = time();
        if (!(($time - 300) <= $timestamp && $timestamp <= ($time + 300))) {
            // 时间戳记超过5分钟
//            header("HTTP/1.1 404 Not Found");
            // header("status: 404 not found");
            header("location:https://study.kidcastle.com.cn");

            return;
        }


        $client = new \GuzzleHttp\Client();

        try {

            $postData = \App\Http\Controllers\Sso\YxtController::calcSignature();
            $postData['uname'] = $account;
            $pcSsoUrl = "http://apic1.yunxuetang.cn/el/sso";
            $response = $client->request('POST', $pcSsoUrl, [
                'headers' => ['Content-Type' => 'application/json'],
                'json' => $postData
            ]);

            $response = json_decode($response->getBody()->getContents(), true);


            $url = 'https://study.kidcastle.com.cn';
            if ($response['code'] == 0) {
                $url = $response['data'];

                if (!empty($path)) {
                    if (strpos($url, '?') === false) {
                        $url .= "?fromurl=" . urlencode($path);
                    } else {
                        $url .= "&fromurl=" . urlencode($path);
                    }
                }
            }

            if (!empty($url)) {
                echo "<script>window.location.href=\"{$url}\"</script>";
                return;
            }

        } catch(Exception $e) {
            header("location:https://study.kidcastle.com.cn");
            return;
        }

    }


    // 获取云学堂老师知识学习进度-97调用-角色平台使用
    public function zhishijindu(Request $request)
    {
//        $token = $request->input('token', '');
//        $token = Aescbc::decryptWithOpenssl($token, CommonConst::KDD_KEY, CommonConst::KDD_IV);
//
//        if (!$token) {
//            // return "token 错误，请重新传入正确token。";
//            return response()->json(
//                [
//                    'error' => 10000,
//                    'errorMsg'=>'',
//                    'success' => false,
//                    'data' => [],
//                    'message' => "token 错误，请重新传入正确token。",
//                ]
//            );
//        }
//        $token = json_decode($token, true);
//        if (!$token) {
//            return response()->json(
//                [
//                    'error' => 10001,
//                    'errorMsg'=>'',
//                    'success' => false,
//                    'data' => [],
//                    'message' => "token 错误，请重新传入正确token。",
//                ]
//            );
//        }

        $token = $request->all();

        // 老师账号
        $account = $token['account'] ?? '';

        // 知识列表
        $knowledgeIds = $token['knowledgeIds'] ?? [];


        // 时间
        $timestamp = $token['timestamp'] ?? 0;
        $time = time();
//        if (!(($time - 300) <= $timestamp && $timestamp <= ($time + 300))) {
//            // 时间戳记超过5分钟
//            return response()->json(
//                [
//                    'error' => 10002,
//                    'errorMsg'=>'',
//                    'success' => false,
//                    'data' => [],
//                    'message' => "token 错误，时间戳记超过5分钟。",
//                ]
//            );
//        }

        try {

            $data = [];

            $yonghuzhishijinduModel = \App\Models\Yxt\YxtYonghuZhishi::select(['userName','knowledgeid','studyschedule'])
                ->where('userName', $account)
                ->whereIn('knowledgeid', $knowledgeIds)
                ->get();

            $yonghuzhishijinduModelByGroupKnowledgeId = $yonghuzhishijinduModel->groupBy('knowledgeid');

            foreach ($yonghuzhishijinduModelByGroupKnowledgeId as $knowId => $rows) {
                $maxJindu = 0;
                foreach ($rows as $row) {
                    if (doubleval($row['studyschedule']) > $maxJindu) {
                        $maxJindu = doubleval($row['studyschedule']);
                    }
                }
                $data[$knowId] = ['jindu'=> round($maxJindu, 2)];
            }


            return response()->json(
                [
                    'error' => 0,
                    'errorMsg'=>'',
                    'success' => true,
                    'data' => ['zhishiliebiao' => $data],
                    'message' => "ok",
                ]
            );

        } catch(Exception $e) {
            return response()->json(
                [
                    'error' => 10003,
                    'errorMsg'=>'',
                    'success' => false,
                    'data' => [],
                    'message' => "请联系管理员" . $e->getMessage(),
                ]
            );
        }
    }
}
