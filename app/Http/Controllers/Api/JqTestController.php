<?php

namespace App\Http\Controllers\Api;

use Illuminate\Routing\Controller as BaseController;
use App\Models\DdBumen;
use App\Models\DdDakajilu;
use App\Models\DdKaoqinzu;
use App\Models\DdKaoqinzuYuangong;
use App\Models\DdYuangong;
use App\Services\Dd\DdApiService;
use Illuminate\Console\Command;
use Library\EduConst;
class JqTestController extends BaseController
{

    public function ddkaoqin()
    {
        $ddDakajiluModel = new DdDakajilu();
        $dakajiluFillable = $ddDakajiluModel->fillable;
        $ddApiService = new  DdApiService();

//        $beginTime  = mktime(0, 0, 0, date("m")  , date("d")-7, date("Y"));
//        $endTime  = mktime(0, 0, 0, date("m")  , date("d"), date("Y"));

        $beginTime = '**********';
        $endTime = '**********';



        // 拉取打卡记录
//        $kaoqinzuYuangongsModel = DdKaoqinzuYuangong::all();
        $kaoqinzuYuangongsModel = DdKaoqinzuYuangong::where("member_id", '*********')->get();
        $kaoqinzuYuangongsModel->chunk(50)->each(function($items, $key) use ($ddApiService, $beginTime, $endTime, $dakajiluFillable) {
            $memberIds = $items->pluck("member_id");
            $dakajiluList = $ddApiService->getDakaJieguo(date("Y-m-d H:i:s", $beginTime), date("Y-m-d H:i:s", $endTime), $memberIds);

            $dakajiluCollect = collect($dakajiluList);
            echo '<pre>';
            print_r($dakajiluList);
            echo '-------------------------------------';

//            $dakajiluCollect->chunk(1)->each(function($items2, $key2) use ($dakajiluFillable) {
//
//                $rows = $items2->toArray();
//                echo json_encode($rows);
//                echo "\n\n";
//
//                DdDakajilu::upsert(
//                    $rows,
//                    ['id'],
//                    $dakajiluFillable
//                );
//            });
        });


        echo "ok-" . time();

        die;
        return Command::SUCCESS;
    }



}
