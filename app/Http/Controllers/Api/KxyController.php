<?php

namespace App\Http\Controllers\Api;

use AlibabaCloud\OpenApiUtil\Tests\ParseModel;
use App\Lib\Kdd\Aescbc;
use App\Lib\Kdd\CommonConst;
use App\Models\Kxy\KxyUsers;
use App\Models\Kxy\KxyXuexixiangmu;
use App\Models\Kxy\KxyXuexixiangmuuser;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Log;
use function Termwind\ValueObjects\pr;

class KxyController extends BaseController
{


    // 单点登录-获取单点登录信息
    public function huoqudandiandengluxinxi(Request $request)
    {
        $token = $request->input('token', '');
        $token = Aescbc::decryptWithOpenssl($token, CommonConst::KDD_KEY, CommonConst::KDD_IV);

        if (!$token) {
            return response()->json(
                [
                    'error' => 10001,
                    'errorMsg'=>'',
                    'success' => false,
                    'data' => [],
                    'message' => "token 不能为空",
                ]
            );
        }
        $token = json_decode($token, true);
        if (!$token) {
            return response()->json(
                [
                    'error' => 10002,
                    'errorMsg'=>'',
                    'success' => false,
                    'data' => [],
                    'message' => "token 解析错误",
                ]
            );
        }
        // 登录账号
        $account = $token['account'] ?? '';
        // 路径
        $path = $token['path'] ?? '';


        // 判断验证时间的有效性
        $timestamp = $token['timestamp'] ?? 0; //$request->input('params', []);
        $time = time();
        if (!(($time - 300) <= $timestamp && $timestamp <= ($time + 300))) {
            return response()->json(
                [
                    'error' => 10003,
                    'errorMsg'=>'',
                    'success' => false,
                    'data' => [],
                    'message' => "时间戳记超时",
                ]
            );
        }

        $client = new \GuzzleHttp\Client();

        try {

            $kuxueyuanApiService = new \App\Services\Kuxueyuan\KuxueyuanApiService();
            $pcSsoUrl = $kuxueyuanApiService->calcSsoUrl($account, false,$path);

//            $response = $client->request('GET', $pcSsoUrl);
//            $response = $response->getBody()->getContents();
//
//            if ($response) {
//                $url = "https://learning.coolcollege.cn/#/course/management?classifyType=all";
//            }

            $url = $pcSsoUrl;

            if (!empty($url)) {
                return response()->json(
                    [
                        'error' => 0,
                        'errorMsg'=>'',
                        'success' => true,
                        'data' => [
                            'yxturl' => $url,
                        ],
                        'message' => "ok",
                    ]
                );
            }
        } catch(Exception $e) {
            return response()->json(
                [
                    'error' => 10004,
                    'errorMsg'=>'',
                    'success' => false,
                    'data' => [],
                    'message' => "登录失败请联系管理员",
                ]
            );
        }
    }


    //酷学院 用户列表
    public function userslist($p=1,$num=100)
    {

        $kuxueyuanApiService = new \App\Services\Kuxueyuan\KuxueyuanApiService();
        $UsersList = $kuxueyuanApiService->getUsersList($p,$num);

//        print_r($UsersList);
//        die;

        //学习项目数据表
        $KxyUsersModel = new KxyUsers();
        $KxyUsersFillable = $KxyUsersModel->fillable;

        if($UsersList['list'] && $UsersList['total'] >= '1'){
            if(isset($UsersList['list']) && $UsersList['total']>='1'){
                foreach ($UsersList['list'] as $UsersListVal){
                    $rows = array();
                    $rows['id'] = $UsersListVal['id'];
                    $rows['job_number'] = $UsersListVal['job_number']??'';
                    $rows['user_name'] = $UsersListVal['user_name']??'';
                    $rows['all_name'] = $UsersListVal['all_name']??'';
                    $rows['source_type'] = $UsersListVal['source_type'];
                    $rows['user_id'] = $UsersListVal['user_id'];
                    $rows['avator'] = $UsersListVal['avator']??'';
                    $rows['create_time'] = $UsersListVal['create_time']?$UsersListVal['create_time']/1000:0;
                    KxyUsers::upsert(
                        $rows,
                        ['id'],
                        $KxyUsersFillable
                    );
                }

                //循环存储数据
                $allnum = $p*$num;
                if($UsersList['total'] > $allnum){
                    $p++;
                    $this->userslist($p,$num);
                }
            }
            echo '数据已处理++';
        }else{
            echo '接口访问出错';
        }

    }

    //酷学院 学习项目列表
    public function xuexixiangmuliebiao($start_time='2024-01-01',$end_time='2050-01-01',$p=1,$num=100)
    {
        $kuxueyuanApiService = new \App\Services\Kuxueyuan\KuxueyuanApiService();
        $XiangMuLieBiao = $kuxueyuanApiService->getXuexixiangmuLiebiao($start_time,$end_time,$p,$num);
//print_r($XiangMuLieBiao);die;
        //学习项目数据表
        $KxyXuexixiangmuModel = new KxyXuexixiangmu();
        $KxyXuexixiangmuFillable = $KxyXuexixiangmuModel->fillable;

        if($XiangMuLieBiao['success'] == true && $XiangMuLieBiao['code'] == '0'){
            if(isset($XiangMuLieBiao['data']) && $XiangMuLieBiao['data']['total']>='1' && $XiangMuLieBiao['data']['list']){
                foreach ($XiangMuLieBiao['data']['list'] as $XiangMuLieBiaoVal){
                    $rows = array();
                    $rows['id'] = $XiangMuLieBiaoVal['id'];
                    $rows['cover'] = $XiangMuLieBiaoVal['cover'];
                    $rows['course_id'] = $XiangMuLieBiaoVal['course_id'];
                    $rows['create_time'] = $XiangMuLieBiaoVal['create_time'];
                    $rows['classify_name'] = $XiangMuLieBiaoVal['classify_name'];
                    $rows['is_free'] = $XiangMuLieBiaoVal['is_free'];
                    $rows['lecturer_name'] = $XiangMuLieBiaoVal['lecturer_name'];
                    $rows['title'] = $XiangMuLieBiaoVal['title'];
                    $rows['official_price'] = $XiangMuLieBiaoVal['official_price'];
                    $rows['preferential_price'] = $XiangMuLieBiaoVal['preferential_price'];
                    $rows['need_enroll'] = $XiangMuLieBiaoVal['need_enroll'];
                    $rows['tags'] = $XiangMuLieBiaoVal['tags']?json_encode($XiangMuLieBiaoVal['tags'],JSON_UNESCAPED_UNICODE ):'';
                    KxyXuexixiangmu::upsert(
                        $rows,
                        ['id'],
                        $KxyXuexixiangmuFillable
                    );
                }

                //循环存储数据
                $allnum = $p*$num;
                if($XiangMuLieBiao['data']['total'] > $allnum){
                    $p++;
                    $this->xuexixiangmuliebiao($start_time,$end_time,$p,$num);
                }
            }
            echo '数据已处理++';
        }else{
            echo '接口访问出错';
        }

    }

    //酷学院 学习项目监控中 -- 目前主要获取用户列表
    public function xuexixiangmujiankongOne($courseid='',$p='1',$num='50') //One
    {
//        $courseid = '2168931698551689216';

        $kuxueyuanApiService = new \App\Services\Kuxueyuan\KuxueyuanApiService();
        $XiangMuJiankong = $kuxueyuanApiService->xuexixiangmujiankong($courseid,$p,$num);

//        print_r($XiangMuJiankong['monitor_list']['list']);die;

        //学习项目-用户 数据表
        $KxyXuexixiangmuuserModel = new KxyXuexixiangmuuser();
        $KxyXuexixiangmuuserFillable = $KxyXuexixiangmuuserModel->fillable;

        if(isset($XiangMuJiankong['monitor_list']['list']) && $XiangMuJiankong['monitor_list']['total']>='1' && $XiangMuJiankong['monitor_list']['list']){
            foreach ($XiangMuJiankong['monitor_list']['list'] as $XiangMuJiankongVal){
                $rows = array();
                $rows['user_id'] = $XiangMuJiankongVal['user_id'];
                $rows['course_id'] = $courseid;
                $rows['user_name'] = $XiangMuJiankongVal['user_name'];
                $rows['jobnumber'] = $XiangMuJiankongVal['jobnumber'];
                $rows['study_count'] = $XiangMuJiankongVal['study_count'];
                $rows['study_progress'] = $XiangMuJiankongVal['study_progress'];
                $rows['process'] = $XiangMuJiankongVal['process'];
                $rows['project_status'] = $XiangMuJiankongVal['project_status'];
                $rows['project_status_name'] = $XiangMuJiankongVal['project_status_name'];
                $rows['department'] = $XiangMuJiankongVal['department'];
                $rows['qualified_status'] = $XiangMuJiankongVal['qualified_status'];
                $rows['qualified_status_name'] = $XiangMuJiankongVal['qualified_status_name'];
                $rows['study_duration_str'] = $XiangMuJiankongVal['study_duration_str'];
                $rows['exam_count'] = $XiangMuJiankongVal['exam_count'];
                $rows['exam_join_count'] = $XiangMuJiankongVal['exam_join_count'];
                $rows['update_time'] = $XiangMuJiankongVal['update_time'];
                $rows['exam_pass_count'] = $XiangMuJiankongVal['exam_pass_count'];
                $rows['credits'] = $XiangMuJiankongVal['credits'];
                $rows['exam_duration_str'] = $XiangMuJiankongVal['exam_duration_str'];
                $rows['departments'] = $XiangMuJiankongVal['departments'];
                $rows['certificate_count'] = $XiangMuJiankongVal['certificate_count'];
                $rows['exam_cast_duration'] = $XiangMuJiankongVal['exam_cast_duration'];
                $rows['exam_cast_duration_str'] = $XiangMuJiankongVal['exam_cast_duration_str'];
                $rows['join_type'] = $XiangMuJiankongVal['join_type'];
                $rows['create_time'] = $XiangMuJiankongVal['create_time'];
                $rows['department_name'] = $XiangMuJiankongVal['department_name'];
                $rows['task_begin_time'] = $XiangMuJiankongVal['task_begin_time'];
                $rows['certificate_name'] = $XiangMuJiankongVal['certificate_name'];
                $rows['study_duration'] = $XiangMuJiankongVal['study_duration'];
                $rows['exam_get_score_count'] = $XiangMuJiankongVal['exam_get_score_count'];
                $rows['enrollment'] = $XiangMuJiankongVal['enrollment'];
                $rows['total_operation_count'] = $XiangMuJiankongVal['total_operation_count'];
                $rows['study_type'] = $XiangMuJiankongVal['study_type'];
                $rows['task_end_time'] = $XiangMuJiankongVal['task_end_time'];
                $rows['exam_duration'] = $XiangMuJiankongVal['exam_duration'];
                $rows['department_names'] = $XiangMuJiankongVal['department_names'];
                // //下边四个字段数据量大，先屏蔽了
//                $rows['stages_json'] = $XiangMuJiankongVal['stages']?json_encode($XiangMuJiankongVal['stages'],JSON_UNESCAPED_UNICODE ):'';
//                $rows['post_name_list_json'] = $XiangMuJiankongVal['post_name_list']?json_encode($XiangMuJiankongVal['post_name_list'],JSON_UNESCAPED_UNICODE ):'';
//                $rows['eventIds_json'] = $XiangMuJiankongVal['eventIds']?json_encode($XiangMuJiankongVal['eventIds'],JSON_UNESCAPED_UNICODE ):'';
//                $rows['user_info_json'] = $XiangMuJiankongVal['user_info']?json_encode($XiangMuJiankongVal['user_info'],JSON_UNESCAPED_UNICODE ):'';
//print_r($rows);
                // //接口没有返回这两个数据所以注释了20250407
                // //$rows['offline_train_duration'] = $XiangMuJiankongVal['offline_train_duration'];
                // //$rows['offline_train_duration_str'] = $XiangMuJiankongVal['offline_train_duration_str'];
                KxyXuexixiangmuuser::upsert(
                    $rows,
                    ['course_id','user_id'],
                    $KxyXuexixiangmuuserFillable
                );
            }

            // //循环存储数据
            $allnum = $p*$num;
            if($XiangMuJiankong['monitor_list']['total'] > $allnum){
                $p++;
                $this->xuexixiangmujiankongOne($courseid,$p,$num);
            }else{
                $courseid = '';
                $p = '1';
                $num = '50';
            }
            echo '数据已处理++'.$courseid.'++'.$num.'++'.$p.'++';
        }else{
            echo '接口访问出错';
        }
    }

    //酷学院 学习项目监控 -- List
    public function xuexixiangmujiankong(Request $request)
    {
        $xuexixiangmuModel = KxyXuexixiangmu::all();
        $xuexixiangmuModel->chunk(20)->each(function($xuexixiangmuItems){
            $xuexixiangmuList = $xuexixiangmuItems->toArray();
            foreach ($xuexixiangmuList as $xuexixiangmuVar){
                //拉取数据
                $this->xuexixiangmujiankongOne($xuexixiangmuVar['course_id'],1,50);
            }
        });
        echo '项目下用户监控数据拉取完成';
    }

}
