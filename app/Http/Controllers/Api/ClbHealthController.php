<?php

namespace App\Http\Controllers\Api;

use App\Lib\Kdd\Aescbc;
use App\Lib\Kdd\CommonConst;
use App\Models\Dd\TbDduserid;
use App\Models\Dd\TbXiangmurenwu;
use App\Models\DdYuangong;
use App\Models\Guanyuan\GuanyuanZhanghao;
use App\Models\Jdb\YzjPerson;
use App\Models\Qiwei\QiweiLianxiwo;
use App\Models\Qiwei\QiweiXiaoqi;
use App\Services\BeisenApi\BeisenService;
use App\Services\Qiwei\QiweiApiService;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Log;

class ClbHealthController extends BaseController
{
    public function index()
    {
        return response()->json([
            'success' => true,
        ]);
    }
}
