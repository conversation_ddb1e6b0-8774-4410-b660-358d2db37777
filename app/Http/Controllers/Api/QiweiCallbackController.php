<?php

namespace App\Http\Controllers\Api;

use AlibabaCloud\SDK\Dingtalk\Vfinance_1_0\Models\CreateSubInstitutionRequest\contactInfo;
use App\Models\Qiwei\QiweiEventcallbackmsg;
use App\Models\Qiwei\QiweiEventhuanyingyu;
use App\Models\Qiwei\QiweiEventhuanyingyuguize;
use EasyWeChat\Work\Application;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Log;
use App\Services\Qiwei\QiweiApiService;

class QiweiCallbackController extends BaseController
{

    // 联系我二维码
    // {"errcode":0,"errmsg":"ok","config_id":"e3a0b505a7b07f887ad5e60e1a5a7040","qr_code":"https:\/\/wework.qpic.cn\/wwpic3az\/696146_kCkLT64ySFSTacj_1732949003\/0"}

    public function index(Request $request)
    {

        Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>QiweiCallbackController::class . "_index" ,'data'=>$request->all()]));
        Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>QiweiCallbackController::class . "_index" ,'content'=>$request->getContent()]));


        try {
            $config = [
                'corp_id' => 'ww7833d742bfec6684',
                'agent_id' => '1000002',
                'secret' =>  'GHPJS3MpOSGLvztfP-1qlaF4GpaHtwvERhAhz4EkhvI',
                'token' => 'IQ4Tbjl6TtUN7CvXALnmoKtUnQdtjf',
                'aes_key' => 'QOuNmJTaWmL6HOgDqL5EApxV9k9OwWm1StMWMeIILXP',
                'http' => [
                    'throw'  => true, // 状态码非 200、300 时是否抛出异常，默认为开启
                    'timeout' => 10.0,
                    'retry' => true, // 使用默认重试配置
                ]
            ];

            $app = new Application($config);

            $server  = $app->getServer();

            $decodeContent = $server->getDecryptedMessage();
            $decodeContent = empty($decodeContent) ? [] : $decodeContent;
            $data = [];

            $data['ToUserName'] = $decodeContent['ToUserName'] ?? '';
            $data['AgentID'] = $decodeContent['AgentID'] ?? '';
            $data['FromUserName'] = $decodeContent['FromUserName'] ?? '';
            $data['MsgType'] = $decodeContent['MsgType'] ?? '';
            $data['Event'] = $decodeContent['Event'] ?? '';
            $data['ChangeType'] = $decodeContent['ChangeType'] ?? '';
            $data['MsgId'] = $decodeContent['MsgId'] ?? '';

            $data['UserID'] = $decodeContent['UserID'] ?? '';
            $data['ExternalUserID'] = $decodeContent['ExternalUserID'] ?? '';
            $data['State'] = $decodeContent['State'] ?? '';
            $data['WelcomeCode'] = $decodeContent['WelcomeCode'] ?? '';
            $data['Source'] = $decodeContent['Source'] ?? '';
            $data['ChatId'] = $decodeContent['ChatId'] ?? '';
            $data['CreateTime'] = $decodeContent['CreateTime'] ?? '';
            $data['Encrypt'] = $decodeContent['Encrypt'] ?? '';
            $data['content'] = empty($decodeContent) ? '' : $decodeContent;

            QiweiEventcallbackmsg::create($data);


            $server->with(function($message, \Closure $next) use ($decodeContent) {
                // $message->event_type 事件类型


                Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>9,'data'=>$message]));
                Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>91,'data'=>$message->event_type]));
                Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>92,'data'=>$message->ChangeType]));
                Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>93,'data'=>$message->change_type]));
//                Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>94,'data'=>$message['change_type']]));
//                Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>95,'data'=>$message['ChangeType']]));
//                Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>96,'data'=>$message['event_type']]));
//                Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>97,'data'=>$message['EventType']]));



                if ($message->ChangeType == 'add_external_contact') {


                    $data = [];

                    $data['ToUserName'] = $decodeContent['ToUserName'] ?? '';
                    $data['AgentID'] = $decodeContent['AgentID'] ?? '';
                    $data['FromUserName'] = $decodeContent['FromUserName'] ?? '';
                    $data['MsgType'] = $decodeContent['MsgType'] ?? '';
                    $data['Event'] = $decodeContent['Event'] ?? '';
                    $data['ChangeType'] = $decodeContent['ChangeType'] ?? '';
//                    $data['MsgId'] = $decodeContent['MsgId'] ?? '';

                    $data['UserID'] = $decodeContent['UserID'] ?? '';
                    $data['ExternalUserID'] = $decodeContent['ExternalUserID'] ?? '';
                    $data['State'] = $decodeContent['State'] ?? '';
                    $data['WelcomeCode'] = $decodeContent['WelcomeCode'] ?? '';

//                    $data['Source'] = $decodeContent['Source'] ?? '';
//                    $data['ChatId'] = $decodeContent['ChatId'] ?? '';
                    $data['CreateTime'] = $decodeContent['CreateTime'] ?? '';
                    $data['Encrypt'] = $decodeContent['Encrypt'] ?? '';
                    $data['content'] = empty($decodeContent) ? '' : $decodeContent;


                    $qiweiEventhuanyingyuModel = QiweiEventhuanyingyu::create($data);


//                    Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>10]));


                    if (!empty($data['WelcomeCode']) && !empty($data['State'])) {





                        $qiweiApiService = new QiweiApiService('ww7833d742bfec6684', '1000002');



                        $body = [];
                        $body['welcome_code'] = $message['WelcomeCode'];
                        $body['text'] = [
                            'content' => "测试文本",
                        ];



                        Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>11, 'data'=> $body]));

                        $rep = $qiweiApiService->fasonghuanyingyu($body);

                        $qiweiEventhuanyingyuModel['req'] = $body;
                        $qiweiEventhuanyingyuModel['rep'] = $rep;
                        $qiweiEventhuanyingyuModel['repCode'] = $rep['errcode'] ?? -1;

                        $qiweiEventhuanyingyuModel->save();

                        Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>12, 'data'=> $rep]));

                    }

                }

                return $next($message);
            });


            //$serve->getDecryptedMessage();

            Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>2,'data'=>$server->getRequestMessage()]));


            // {"ToUserName":"ww7833d742bfec6684","Encrypt":"z\/tFtR81vcI01FmI56oi2UYEH4uJFHvQ7TUGgEN\/f6Rw55+Wwo3RnfE+ssXawg0z\/LwTDKeeGja+8zAdwERwP+prp5uHIKVZpXF0OKqWRrfAbjQujr\/F5RcdIPzabAnZo8DPfDsSR32sW\/zZpqnlrVErDYlupAkzpYM2b0J+bpBsf5gdwBUNEtydHYGNA3IS0vUWdiETnptz9pAZJW8HtnMBtCIYsUIwqTTQogOMUAX35Nf3AcC\/wyKa\/gPjeixA5mAVllG7mh2mE3iJ6S964eSeSWGqwm4FMxC31xNjFp84vYFyRe9qoJW1vU6QgaGggHNNgZhmWSTW8oBfCteJ3pWXjVH1\/xQwsCEq5YqsWpkMCUph7onKtisxr6gjdewgtHMYoOtwIfja0QAaDnOTbkAfprA4\/V2R6pY7oadnCGKS+AzXZQ9P4OgBmzwRgjqmTwk0e3xeQm5tyWmpkpsiSLVFbTsqf9cXIouSv2zZf1A0I+8Id5b\/gqoL5xX1085oJNO9kQGLziWdqG2Ed8O6d3NPsmRoRw4BgoybTjF8kw6JcLbGYHGjkrxMxurv8feVTIqMa9Dj9tjXJ2BQ8CFxG6spl+EPrULHxK9tXqBq1ystrXLVfYg8pmCRUlp3\/UAy2lQAW4jwLPsacxFNvVbnqkPMp\/xKgU+G4xMXzmwrTbAun4gifAVsz6NyH2gTwe37I8Z6oY9AeeNQmRXtIHsBqHkWI1wTcIiqEwJGuPyPEkSLegCRt6DSWN\/NnpyqdVt7","AgentID":"1000002","FromUserName":"sys","CreateTime":"1732949210","MsgType":"event","Event":"change_external_contact","ChangeType":"add_external_contact","UserID":"WuLei","ExternalUserID":"wmO_HIDgAAyS2UssTJAF5wIkM0f0_lcg","State":"lianxiwohello","WelcomeCode":"dGyruI3m2V6dKvrQSYFqhu4dIMLntMpxcPTCh0FD5-U"}


            Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>3,'data'=>$server->getDecryptedMessage()]));

            Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>4,'data'=>$server]));
        }catch (\Exception $e) {

            Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>5,'data'=>$e->getCode()]));
            Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>6,'data'=>$e->getMessage()]));
            Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>7,'data'=>$e->getTraceAsString()]));

        }

        $response = $server->serve();


        Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>8,'data'=>$response]));


        return $response; //$server->serve(); //$server; //$serve->serve();

    }


    // 联系我加人二维码
    // {"errcode":0,"errmsg":"ok","config_id":"fa5639a474a6d661b4e7a3101de929b5","qr_code":"https:\/\/wework.qpic.cn\/wwpic3az\/184002_iIiluNoaShSE00H_1733119498\/0"}
    // 吉德堡kidcastle 企微回调
    public function indexforkidcastle(Request $request)
    {

        Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>QiweiCallbackController::class . "_index" ,'data'=>$request->all()]));
        Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>QiweiCallbackController::class . "_index" ,'content'=>$request->getContent()]));


        try {
            $config = [
                'corp_id' => 'ww8977cb1b47249e00',
                'agent_id' => '1000009',
                'secret' =>  'RpxeEXqoBspUnyxLS-nat6hsWgLU-Dqq8OqqAOPfJ9w',
                'token' => 'IQ4Tbjl6TtUN7CvXALnmoKtUnQdtjf',
                'aes_key' => 'QOuNmJTaWmL6HOgDqL5EApxV9k9OwWm1StMWMeIILXP',
                'http' => [
                    'throw'  => true, // 状态码非 200、300 时是否抛出异常，默认为开启
                    'timeout' => 10.0,
                    'retry' => true, // 使用默认重试配置
                ]
            ];

            $app = new Application($config);

            $server  = $app->getServer();

            $decodeContent = $server->getDecryptedMessage();
            $decodeContent = empty($decodeContent) ? [] : $decodeContent;


            Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>111, 'data'=> $decodeContent]));


            $data = [];

            $data['ToUserName'] = $decodeContent['ToUserName'] ?? '';
            $data['AgentID'] = $decodeContent['AgentID'] ?? '';
            $data['FromUserName'] = $decodeContent['FromUserName'] ?? '';
            $data['MsgType'] = $decodeContent['MsgType'] ?? '';
            $data['Event'] = $decodeContent['Event'] ?? '';
            $data['ChangeType'] = $decodeContent['ChangeType'] ?? '';
            $data['MsgId'] = $decodeContent['MsgId'] ?? '';

            $data['UserID'] = $decodeContent['UserID'] ?? '';
            $data['ExternalUserID'] = $decodeContent['ExternalUserID'] ?? '';
            $data['State'] = $decodeContent['State'] ?? '';
            $data['WelcomeCode'] = $decodeContent['WelcomeCode'] ?? '';
            $data['Source'] = $decodeContent['Source'] ?? '';
            $data['ChatId'] = $decodeContent['ChatId'] ?? '';
            $data['CreateTime'] = $decodeContent['CreateTime'] ?? '';
            $data['Encrypt'] = $decodeContent['Encrypt'] ?? '';
            $data['content'] = empty($decodeContent) ? '' : $decodeContent;

            QiweiEventcallbackmsg::create($data);


            $server->with(function($message, \Closure $next) use ($decodeContent) {
                // $message->event_type 事件类型


                Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>9,'data'=>$message]));
                Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>91,'data'=>$message->event_type]));
                Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>92,'data'=>$message->ChangeType]));
                Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>93,'data'=>$message->change_type]));
//                Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>94,'data'=>$message['change_type']]));
//                Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>95,'data'=>$message['ChangeType']]));
//                Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>96,'data'=>$message['event_type']]));
//                Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>97,'data'=>$message['EventType']]));



                // 添加好友事件
                if ($message->ChangeType == 'add_external_contact') {


                    $data = [];

                    $data['ToUserName'] = $decodeContent['ToUserName'] ?? '';
                    $data['AgentID'] = $decodeContent['AgentID'] ?? '';
                    $data['FromUserName'] = $decodeContent['FromUserName'] ?? '';
                    $data['MsgType'] = $decodeContent['MsgType'] ?? '';
                    $data['Event'] = $decodeContent['Event'] ?? '';
                    $data['ChangeType'] = $decodeContent['ChangeType'] ?? '';
//                    $data['MsgId'] = $decodeContent['MsgId'] ?? '';

                    $data['UserID'] = $decodeContent['UserID'] ?? '';
                    $data['ExternalUserID'] = $decodeContent['ExternalUserID'] ?? '';
                    $data['State'] = $decodeContent['State'] ?? '';
                    $data['WelcomeCode'] = $decodeContent['WelcomeCode'] ?? '';

//                    $data['Source'] = $decodeContent['Source'] ?? '';
//                    $data['ChatId'] = $decodeContent['ChatId'] ?? '';
                    $data['CreateTime'] = $decodeContent['CreateTime'] ?? '';
                    $data['Encrypt'] = $decodeContent['Encrypt'] ?? '';
                    $data['content'] = empty($decodeContent) ? '' : $decodeContent;


                    $qiweiEventhuanyingyuModel = QiweiEventhuanyingyu::create($data);


//                    Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>10]));




                    // TODO 测试
                    // if ($data['UserID'] == "0110210366" || $data['UserID'] == '0111810005') {




                        $qudao = $data['State'] ?? '';

                        $huanyingyuModel = QiweiEventhuanyingyuguize::orderByDesc('id')->where("state", "")->first(); //null;


                        Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>94,'data'=>$huanyingyuModel]));


                        $huanyingyuguizesModel = QiweiEventhuanyingyuguize::orderByDesc('id')->get();
                        foreach ($huanyingyuguizesModel as $model) {
                            $state = $model['state'] ?? '';
                            if (empty($state)) continue;
                            if (stripos($qudao, $state) !== false) {
                                $huanyingyuModel = $model;
                            }
                        }

                        Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>95,'data'=>$huanyingyuModel]));




                        if (!empty($data['WelcomeCode']) && !empty($huanyingyuModel)) {




                            $qiweiApiService = new QiweiApiService('ww8977cb1b47249e00', '1000009');



                            $body = [];
                            $body['welcome_code'] = $message['WelcomeCode'];
                            $body['text'] = ['content' => $huanyingyuModel['content']];
//                            [
//                                'content' => "亲爱的家长，感谢您对吉的堡和我的信任！
//希望通过我们的交流能帮助您更多地了解吉的堡，为您和宝贝提供优质的教育资源！",
//                            ];

                            // $mediaId = '29RNRgJEA15ov-TMaK86k9vkTt5NL_3aj3alU-j7OQatlbyMtKhwA9iXnOzLXVCFlqrnS0FqxAhMfw8YULBxWTA';


                            $qudao = str_replace("kdd_", "", $qudao);

                            if (isset($huanyingyuModel['xiaochengxuAppId']) && !empty($huanyingyuModel['xiaochengxuAppId'])) {

                                $mediaId = $huanyingyuModel['xiaochengxuMediaId'] ?? '27VAiMU-7ItlIDO5Uy6jQgk6t0buDbbsF5Me7Nio_6GcmhOdhvk7ImAgAqbogs7uBPtGV6_8iXEuvLE5JvMPhig';

                                $xiaochengxuPage = $huanyingyuModel['xiaochengxuPage'];
                                $xiaochengxuArgs = $huanyingyuModel['xiaochengxuArgs'] ?? 'state';

                                if (stripos($xiaochengxuPage, '?') !== false) {
                                    $xiaochengxuPage = $xiaochengxuPage . "&" . $xiaochengxuArgs . "=" . $qudao;
                                } else {
                                    $xiaochengxuPage = $xiaochengxuPage . "?" . $xiaochengxuArgs . "=" . $qudao;
                                }

                                $body['miniprogram'] = [
                                    'title' => $huanyingyuModel['xiaochengxuTitle'], //"小程序",
                                    'pic_media_id' => $mediaId,
                                    'appid' => $huanyingyuModel['xiaochengxuAppId'], //'wx5c1bbd43da334d85',
                                    'page' => $xiaochengxuPage, //'/homePages/score/parent-reply-detailov?logId=12921143',
                                ];
                            }



                            Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>11, 'data'=> $body]));

                            $rep = $qiweiApiService->fasonghuanyingyu($body);

                            $qiweiEventhuanyingyuModel['req'] = $body;
                            $qiweiEventhuanyingyuModel['rep'] = $rep;
                            $qiweiEventhuanyingyuModel['repCode'] = $rep['errcode'] ?? -1;

                            $qiweiEventhuanyingyuModel->save();

                            Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>12, 'data'=> $rep]));

                        }



//                        // 没有渠道信息推送默认消息
//                        if (!empty($data['WelcomeCode']) && empty($data['State'])) {
//
//                        }
//
//                        // 有渠道信息推送规则信息
//                        if (!empty($data['WelcomeCode']) && !empty($data['State'])) {
//
//                        }

                    // }


//
//                    if ($data['UserID'] == "0110210366" && !empty($data['WelcomeCode']) && !empty($data['State'])) {
//
//
//
//
//
//                        $qiweiApiService = new QiweiApiService('ww8977cb1b47249e00', '1000009');
//
//
//
//                        $body = [];
//                        $body['welcome_code'] = $message['WelcomeCode'];
//                        $body['text'] = [
//                            'content' => "亲爱的家长，感谢您对吉的堡和我的信任！
//希望通过我们的交流能帮助您更多地了解吉的堡，为您和宝贝提供优质的教育资源！",
//                        ];
//
//
//                        // $mediaId = '29RNRgJEA15ov-TMaK86k9vkTt5NL_3aj3alU-j7OQatlbyMtKhwA9iXnOzLXVCFlqrnS0FqxAhMfw8YULBxWTA';
//
//                        $mediaId = '27VAiMU-7ItlIDO5Uy6jQgk6t0buDbbsF5Me7Nio_6GcmhOdhvk7ImAgAqbogs7uBPtGV6_8iXEuvLE5JvMPhig';
//
//
//                        $body['miniprogram'] = [
//                                'title' => "小程序",
//                                'pic_media_id' => $mediaId,
//                                'appid' => 'wx5c1bbd43da334d85',
//                                'page' => '/homePages/score/parent-reply-detailov?logId=12921143',
//                            ];
//
//
//
//
//
//                        Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>11, 'data'=> $body]));
//
//                        $rep = $qiweiApiService->fasonghuanyingyu($body);
//
//                        $qiweiEventhuanyingyuModel['req'] = $body;
//                        $qiweiEventhuanyingyuModel['rep'] = $rep;
//                        $qiweiEventhuanyingyuModel['repCode'] = $rep['errcode'] ?? -1;
//
//                        $qiweiEventhuanyingyuModel->save();
//
//                        Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>12, 'data'=> $rep]));
//
//                    }
//



                }

                return $next($message);
            });


            //$serve->getDecryptedMessage();

            Log::debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>2,'data'=>$server->getRequestMessage()]));


            // {"ToUserName":"ww7833d742bfec6684","Encrypt":"z\/tFtR81vcI01FmI56oi2UYEH4uJFHvQ7TUGgEN\/f6Rw55+Wwo3RnfE+ssXawg0z\/LwTDKeeGja+8zAdwERwP+prp5uHIKVZpXF0OKqWRrfAbjQujr\/F5RcdIPzabAnZo8DPfDsSR32sW\/zZpqnlrVErDYlupAkzpYM2b0J+bpBsf5gdwBUNEtydHYGNA3IS0vUWdiETnptz9pAZJW8HtnMBtCIYsUIwqTTQogOMUAX35Nf3AcC\/wyKa\/gPjeixA5mAVllG7mh2mE3iJ6S964eSeSWGqwm4FMxC31xNjFp84vYFyRe9qoJW1vU6QgaGggHNNgZhmWSTW8oBfCteJ3pWXjVH1\/xQwsCEq5YqsWpkMCUph7onKtisxr6gjdewgtHMYoOtwIfja0QAaDnOTbkAfprA4\/V2R6pY7oadnCGKS+AzXZQ9P4OgBmzwRgjqmTwk0e3xeQm5tyWmpkpsiSLVFbTsqf9cXIouSv2zZf1A0I+8Id5b\/gqoL5xX1085oJNO9kQGLziWdqG2Ed8O6d3NPsmRoRw4BgoybTjF8kw6JcLbGYHGjkrxMxurv8feVTIqMa9Dj9tjXJ2BQ8CFxG6spl+EPrULHxK9tXqBq1ystrXLVfYg8pmCRUlp3\/UAy2lQAW4jwLPsacxFNvVbnqkPMp\/xKgU+G4xMXzmwrTbAun4gifAVsz6NyH2gTwe37I8Z6oY9AeeNQmRXtIHsBqHkWI1wTcIiqEwJGuPyPEkSLegCRt6DSWN\/NnpyqdVt7","AgentID":"1000002","FromUserName":"sys","CreateTime":"1732949210","MsgType":"event","Event":"change_external_contact","ChangeType":"add_external_contact","UserID":"WuLei","ExternalUserID":"wmO_HIDgAAyS2UssTJAF5wIkM0f0_lcg","State":"lianxiwohello","WelcomeCode":"dGyruI3m2V6dKvrQSYFqhu4dIMLntMpxcPTCh0FD5-U"}


            Log::debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>3,'data'=>$server->getDecryptedMessage()]));

            Log::debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>4,'data'=>$server]));
        }catch (\Exception $e) {

            Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>5,'data'=>$e->getCode()]));
            Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>6,'data'=>$e->getMessage()]));
            Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>7,'data'=>$e->getTraceAsString()]));

        }

        $response = $server->serve();


        Log::channel('qiweicallback')->debug(json_encode(['act'=>QiweiCallbackController::class . "_index","sub"=>8,'data'=>$response]));


        return $response; //$server->serve(); //$server; //$serve->serve();
    }
}
