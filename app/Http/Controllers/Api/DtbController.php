<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\Api\Dtb\PersonRequest;
use App\Lib\Kdd\Aescbc;
use App\Lib\Kdd\CalcSign;
use App\Lib\Kdd\CommonConst;
use App\Models\Dtb\DtbJuese;
use App\Models\Dtb\DtbJueseEntry;
use App\Models\Jdb\YzjDept;
use App\Models\Jdb\YzjPerson;
use App\Models\Jdb\YzjPersonJob;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;

class DtbController extends BaseController
{
    public function person(PersonRequest $request)
    {
        $params = $request->validated();

        if (empty($params['token'])) {
            return response()->json([
                'success' => false,
                'error' => '请传入token参数',
                'errorCode' => 10000,
                'data' => ''
            ]);
        }


        $time = time();

        $token =  Aescbc::decryptWithOpenssl($params['token'], CommonConst::DTB_KEY, CommonConst::DTB_IV);

        $token = json_decode($token, true);
        if (empty($token) || $token['apiuser_code'] != 'dtb' || $token['company_id'] != '10000') {
            return response()->json([
                'success' => false,
                'error' => '请传入正确token参数',
                'errorCode' => 10001,
                'data' => ''
            ]);
        }







        if (empty($params['openId'])) {
            return response()->json([
                'success' => false,
                'error' => '请传入openId参数',
                'errorCode' => 10002,
                'data' => ''
            ]);
        }

        $person = YzjPerson::where("openId", $params['openId'])->first();
        if (empty($person)) {
            return response()->json([
                'success' => false,
                'error' => "openId:{$params['openId']}不存在",
                'errorCode' => 10003,
                'data' => ''
            ]);
        }

        $yzjPersonJobList = YzjPersonJob::where("openId", $params['openId'])->where('is_disable', 0)->get();
        $yzjDeptList = YzjDept::whereIn('orgId', $yzjPersonJobList->pluck('orgId'))->get();
        $yzjDeptListByOrgId = $yzjDeptList->keyBy('orgId');

        $personJobList = collect([]);
        $personJobDeptList = collect([]);
        foreach ($yzjPersonJobList as $personJob) {
            if (isset($yzjDeptListByOrgId[$personJob['orgId']])) {
                if ($yzjDeptListByOrgId[$personJob['orgId']]['is_disable'] == 0) {

                    $personJobList[] = $personJob;
                    $personJobDeptList[] = $yzjDeptListByOrgId[$personJob['orgId']];
                }
            }
        }

        $jueseList = DtbJuese::whereIn('id', DtbJueseEntry::whereIn('jobId', $personJobList->pluck('jobId'))->get()->pluck('jueseId'))->get();
        $access_rule = '';
        $access_rule_source = 0;
        foreach ($jueseList as $row) {
            if ($row['weight'] >= $access_rule_source) {
                $access_rule = $row['title'];
            }
        }

        $xiaoquList = YzjDept::where('is_disable', 0)->where('isSchool', 4)->get();
        $xiaoList = YzjDept::where('is_disable', 0)->where('isSchool', 1)->get();

        $school_list = [];
        foreach ($personJobDeptList as $row) {

            list($isXiaoqu, $xiaoquDeptLongName) = YzjDept::arrayInString($xiaoquList, $row['deptLongName']);
            list($isXiao, $xiaoDeptLongName) = YzjDept::arrayInString($xiaoList, $row['deptLongName']);

            // 分配小区下所有校权限
            if ($isXiaoqu && !$isXiao) {
                foreach ($xiaoList as $xiaoRow) {
                    if (mb_strstr($xiaoRow['deptLongName'], $xiaoquDeptLongName) !== false) {
                        $school_list[] = $xiaoRow['schoolCode'];
                    }
                }
            }

            // 分配单校权限
            if ($isXiaoqu && $isXiao) {
                foreach ($xiaoList as $xiaoRow) {
                    if ($xiaoRow['deptLongName'] == $xiaoDeptLongName) {
                        if (empty($xiaoRow['schoolCode'])) continue;
                        $school_list[] = $xiaoRow['schoolCode'];
                    }
                }
            }
        }

        $data = [];
        $data['openid'] = $person['openId'];
        $data['username'] = $person['name'];
        $data['access_rule'] = $access_rule;
        $data['school_list'] = $school_list;
        $data['is_disable'] = $person['is_disable'];

        return response()->json([
            'success' => true,
            'error' => "",
            'errorCode' => 0,
            'data' => $data,
        ]);
    }


}
