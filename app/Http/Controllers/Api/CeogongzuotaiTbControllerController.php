<?php

namespace App\Http\Controllers\Api;

use App\Lib\Kdd\Aescbc;
use App\Lib\Kdd\CommonConst;
use App\Models\Dd\TbDduserid;
use App\Models\Dd\TbXiangmurenwu;
use App\Models\DdYuangong;
use App\Models\Guanyuan\GuanyuanZhanghao;
use App\Models\Jdb\YzjPerson;
use App\Models\Qiwei\QiweiLianxiwo;
use App\Models\Qiwei\QiweiXiaoqi;
use App\Services\BeisenApi\BeisenService;
use App\Services\Qiwei\QiweiApiService;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Log;

class CeogongzuotaiTbControllerController extends BaseController
{

    public function tblistzhuananguanli(Request $request)
    {
//        Log::info((array_merge(['act'=>'tblist', $request->all()])));
//        Log::info(json_encode(array_merge(['act'=>'tblist', $request->getContent()])));



        $maxGroupLength = $request->input('maxGroupLength');
        $maxLength = $request->input('maxLength');
        $tabs = $request->input('tabs', '');
        $yidong = $request->input('yidong', '0');


        if (empty($tabs)) {
            $bumenList = TbXiangmurenwu::select("extbumen")->distinct()->where("extbumen", "<>", "")->get();
            $tabs = $bumenList->pluck("extbumen")->toArray();
        } else {
            $tabs = explode(",", $tabs);
        }


        $renwusModel = TbXiangmurenwu::where('projectId', '6707db6a7130d6b60d6363b0')->where('isDone', "0")->where('parentTaskId',null)->orderByDesc("created");
        if (!empty($tabs)) {
            $renwusModel = $renwusModel->whereIn("extbumen", $tabs);
        }

        $renwusModel = $renwusModel->get();
        $renwusModelByBumen = $renwusModel->groupBy("extbumen");

        $creatorIdList = $renwusModel->pluck('creatorId')->toArray();

        $priorityMap = [
            '-10' => '3星',
            '0' => '4星',
            '1' => '5星',
            '2' => '6星',
        ];

        $result = ['list' => []];
        foreach ($tabs as $tab) {

            $newGroup = ['title' => $tab, 'items'=>[]];

            if (!isset($renwusModelByBumen[$tab])) continue;

            $count=0;

            foreach ($renwusModelByBumen[$tab] as $row) {

                if ($count > $maxLength) break;

                $count++;

                $tbUserId = TbDduserid::where('tbUserId', $row['executorId'])->first();
                if($tbUserId) {
                    $ddYuangong = DdYuangong::where('userid', $tbUserId['dingtalkUserId'])->first();
                }else{
                    $ddYuangong['name'] = '';
                }
                $jumpUrl = "https://www.teambition.com/project/{$row['projectId']}/tasks/view/6707db6ad4bf472e8ed2a282/task/{$row['id']}";
                if ($yidong == '1') {
                    $jumpUrl = "https://www.teambition.com/h5/project/{$row['projectId']}/tasks/view/6707db6ad4bf472e8ed2a282/tasks/{$row['id']}";
                }

                $newRow = [];
                $newRow['id'] = intval($row['uniqueId']);
                $newRow['title'] = $row['content']; // . " " . ($priorityMap[$row['priority']] ?? "");
                $newRow['description'] = $row['note'];
                $newRow['author'] = $ddYuangong['name'] . " ";
                $newRow['date'] = date("Y-m-d", strtotime($row['created']));
                $newRow['imageUrl'] = "";
                $newRow['jumpUrl'] = $jumpUrl; //"https://www.teambition.com/project/{$row['projectId']}/tasks/view/6707db6ad4bf472e8ed2a282/task/{$row['id']}";
                $newGroup['items'][] = $newRow;
            }

            $result['list'][] = $newGroup;
        }



        return response()->json($result);
    }





    public function tblisttest(Request $request)
    {

//        Log::info((array_merge(['act'=>'tblist', $request->all()])));
//        Log::info(json_encode(array_merge(['act'=>'tblist', $request->getContent()])));


        $maxGroupLength = $request->input('maxGroupLength');
        $maxLength = $request->input('maxLength');

//        Log::info((array_merge(['act'=>'tblist', $maxGroupLength, $maxLength ])));





        $result = [
            'list' => [
                [
                    'title' => "通知",
                    'items'=>[
                        [
                            'id' => 123,
                            'title' => '标题1',
                            'description' => '描述1',
                            'author' => '',
                            'date' => '2024-01-31 14:00:42',
                            'imageUrl' => '',
                            'jumpUrl' => "https://www.baidu.com",
                            //'jumpUrl' => "https://www.teambition.com/project/6707db6a7130d6b60d6363b0/tasks/view/674ec4bee499bb48bebf1316/task/674fefcfeb950bb43a4a627c",
                        ]
                    ],
                ],
                [
                    'title' => "公司制度1",
                    'items'=>[],
                ],
                [
                    'title' => "变更公告2",
                    'items'=>[],
                ],
                [
                    'title' => "公司制度3",
                    'items'=>[],
                ],
                [
                    'title' => "变更公告4",
                    'items'=>[],
                ],
                [
                    'title' => "公司制度5",
                    'items'=>[],
                ],
                [
                    'title' => "变更公告6",
                    'items'=>[],
                ]


            ]
        ];

//        $str = json_decode($str, true);

        return response()->json($result);
    }

//    // 获取企微园联系我二维码-97调用
//    public function lianxiwoerweima(Request $request)
//    {
//        $xiaoqubianhao = $request->input('xiaoqubianhao', '');
//        $qudao = $request->input('qudao', '');
//
//
//        if (empty($xiaoqubianhao)) {
//            return response()->json(
//                [
//                    'error' => 10000,
//                    'errorMsg'=>'',
//                    'success' => false,
//                    'data' => [],
//                    'message' => "校区编号不能为空",
//                ]
//            );
//        }
//
//        if (mb_strlen($qudao) > 20) {
//            return response()->json(
//                [
//                    'error' => 10004,
//                    'errorMsg'=>'',
//                    'success' => false,
//                    'data' => [],
//                    'message' => "渠道长度不能超过20个字符",
//                ]
//            );
//        }
//
//
//        $beisenService = new BeisenService();
//
//        $beisenBumenListModel = $beisenService->getBeisenBumenModel();
//        $beisenBumenListModel = $beisenBumenListModel->whereIn('extzuzhileixing', [8, 14, 20]);
//
//        $beisenBumenListModel = $beisenBumenListModel->where('extyewuxitongbianma', $xiaoqubianhao);
//
//        $beisenBumenModel = $beisenBumenListModel->first();
//
//        if (empty($beisenBumenModel)) {
//            return response()->json(
//                [
//                    'error' => 10001,
//                    'errorMsg'=>'',
//                    'success' => false,
//                    'data' => [],
//                    'message' => "未找到{$xiaoqubianhao}校区/园所",
//                ]
//            );
//        }
//
//        $xiaoqiModel = QiweiXiaoqi::where('changmingcheng', $beisenBumenModel->changmingcheng)->where('zhuangtai', 1)->first();
//        if (empty($xiaoqiModel)) {
//            return response()->json(
//                [
//                    'error' => 10002,
//                    'errorMsg'=>'',
//                    'success' => false,
//                    'data' => [],
//                    'message' => "未找到{$xiaoqubianhao} 小奇账号",
//                ]
//            );
//        }
//
//
//
//
//
//        $qiweiLianxiwo = QiweiLianxiwo::where('qiweiyuangongbianhao', $xiaoqiModel->qiweiyuangongbianhao)->where('qudao', $qudao)->first();
//        if (!empty($qiweiLianxiwo)) {
//
//            return response()->json(
//                [
//                    'error' => 0,
//                    'errorMsg'=>'',
//                    'success' => true,
//                    'data' => [
//                        'xiaoqu' => [
//                            $xiaoqubianhao => [
//                                'qiweiconfigid' => $qiweiLianxiwo->qiweiconfigid,
//                                'erweimadizhi' => $qiweiLianxiwo->qiweierweima,
//                                'qudao' => $qudao,
//                            ]
//                        ]
//                    ],
//                    'message' => "",
//                ]
//            );
//
//
//        } else {
//
//            $qiweiApiService = new QiweiApiService('ww8977cb1b47249e00', '1000009');
//
//            $lianxiwoBody = ['type'=>1, 'scene'=>2, 'user'=>$xiaoqiModel->qiweiyuangongbianhao];
//            if (!empty($qudao)) {
//                $lianxiwoBody['state'] = "kdd_{$qudao}";
//            }
//
//            $rep = $qiweiApiService->addLianxiren(
//                $lianxiwoBody
//            );
////                ['type'=>1, 'scene'=>2, 'user'=>$xiaoqiModel->qiweiyuangongbianhao]
//
//
//            if ($rep['errcode'] != 0) {
//                return response()->json(
//                    [
//                        'error' => 10003,
//                        'errorMsg'=>'',
//                        'success' => false,
//                        'data' => [],
//                        'message' => "校区{$xiaoqubianhao}, userid:" . $xiaoqiModel->qiweiyuangongbianhao . "生成错误，请联系管理员.",
//                    ]
//                );
//            } else {
//
//                $qiweiLianxiwo = QiweiLianxiwo::create([
//                    'qiweiyuangongbianhao' => $xiaoqiModel->qiweiyuangongbianhao,
//                    'qudao' => "kdd_{$qudao}",
//                    'qiweiconfigid' => $rep['config_id'],
//                    'qiweierweima' => $rep['qr_code'],
//                    'zhuangtai'=>1,
//                ]);
//
//            }
//
//            return response()->json(
//                [
//                    'error' => 0,
//                    'errorMsg'=>'',
//                    'success' => true,
//                    'data' => [
//                        'xiaoqu' => [
//                            $xiaoqubianhao => [
//                                'qiweiconfigid' => $qiweiLianxiwo->qiweiconfigid,
//                                'erweimadizhi' => $qiweiLianxiwo->qiweierweima,
//                                'qudao' => $qudao,
//                            ]
//                        ]
//                    ],
//                    'message' => "",
//                ]
//            );
//
//        }
//
//
//    }

}
