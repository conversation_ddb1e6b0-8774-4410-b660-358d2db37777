<?php


namespace App\Http\Controllers\Api;

use App\Http\Requests\Api\Dtb\PersonRequest;
use App\Lib\Kdd\Aescbc;
use App\Lib\Kdd\CalcSign;
use App\Lib\Kdd\CommonConst;
use App\Models\DdYuangong;
use App\Models\Dtb\DtbJuese;
use App\Models\Dtb\DtbJueseEntry;
use App\Models\Fanwei\Hrmresource;
use App\Models\Guanyuan\GuanyuanZhanghao;
use App\Models\Jdb\YzjDept;
use App\Models\Jdb\YzjPerson;
use App\Models\Jdb\YzjPersonJob;
use App\Services\BeisenApi\BeisenService;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Log;


class DdController extends BaseController
{
    public function yuangongxinxibyjobnumber(Request $request)
    {
        $jobnumber = $request->input('jobnumber', '');

        if (empty($jobnumber)) {
            return response()->json(
                [
                    'error' => 10001,
                    'errorMsg'=>'',
                    'success' => false,
                    'data' => [],
                    'message' => "jobnumber不能为空",
                ]
            );
        }



        $ddYuangongModel = DdYuangong::where('job_number', $jobnumber)->whereNotIn('title', [
            '案经理',
'财务主任',
'督导',
'区园长',
'项目副总',
'集团副总',
'专案协理',
'市场主管',
'财务督导',
'财务副主任',
'区域市场经理',
'财务助理督导',
'董事长',
'特助',
        ])->first();
        if (empty($ddYuangongModel)) {

            return response()->json(
                [
                    'error' => 10002,
                    'errorMsg'=>'',
                    'success' => false,
                    'data' => [],
                    'message' => "未搜索到员工编号：" . $jobnumber,
                ]
            );
        }

        return response()->json(
            [
                'error' => 0,
                'errorMsg'=>'',
                'success' => true,
                'data' => [
                    'job_number' => $ddYuangongModel['job_number'],
                    'dduserid' => $ddYuangongModel['userid'],
                ],
                'message' => "ok",
            ]
        );





    }

    // 钉钉员工信息-97使用
    public function yuangongxinxi(Request $request)
    {
        $userid = $request->input('userid', '');
        if (empty($userid)) {
            return response()->json(
                [
                    'error' => 10001,
                    'errorMsg'=>'',
                    'success' => false,
                    'data' => [],
                    'message' => "dd userid 不能为空",
                ]
            );
        }


        $ddYuangongModel = DdYuangong::where('userid', $userid)->where('shanchu', 0)->first();
        if (empty($ddYuangongModel)) {
            return response()->json(
                [
                    'error' => 10002,
                    'errorMsg'=>'',
                    'success' => false,
                    'data' => [],
                    'message' => "dd userid {$userid}, 未找到在职员工信息",
                ]
            );
        }

        $zhanghaoMode = GuanyuanZhanghao::where("钉钉账号", $userid)->first();
        if (empty($zhanghaoMode)) {
            $gyzhanghao = '';
        }else{
            $gyzhanghao = $zhanghaoMode->账号;
        }
        return response()->json(
            [
                'error' => 0,
                'errorMsg'=>'',
                'success' => true,
                'data' => [
                    'user' => [
                        'mobile' => $ddYuangongModel->mobile,
                        'name' => $ddYuangongModel->name,
                        'jobNumber' => $ddYuangongModel->job_number,
                        'gyZhanghao' => $gyzhanghao,
                    ]
                ],
                'message' => "ok",
            ]
        );

    }

//    // 获取北森主职信息-97调用
//    public function zhuzhixinxi(Request $request)
//    {
//        $mobile = $request->input('mobile', '');
//
//        if (empty($mobile)) {
//            return response()->json(
//                [
//                    'error' => 10001,
//                    'errorMsg'=>'',
//                    'success' => false,
//                    'data' => [],
//                    'message' => "mobile不能为空",
//                ]
//            );
//        }
//
//        $beisenService = new BeisenService();
//
//        $beisenYuangongsModel = $beisenService->getBeisenYuangongModel();
//
//        $yuangongModel = $beisenYuangongsModel->where('t_beisen_yuangong.mobilePhone', $mobile)->where('serviceType', 0)->first();
//
//        if (empty($yuangongModel)) {
//            return response()->json(
//                [
//                    'error' => 10002,
//                    'errorMsg'=>'',
//                    'success' => false,
//                    'data' => [],
//                    'message' => "未找到北森用户mobile:{$mobile}",
//                ]
//            );
//        }
//
//
//        return response()->json(
//            [
//                'error' => 0,
//                'errorMsg'=>'',
//                'success' => true,
//                'data' => [
//                    'user' => [
//                        'mobile' => $yuangongModel->mobilePhone,
//                        'zhiwuname' => $yuangongModel->zhiwuname,
//                        'name' => $yuangongModel->name,
//                        'jobNumber' => $yuangongModel->jobNumber,
//                    ]
//                ],
//                'message' => "ok",
//            ]
//        );
//
//    }
//
//
//
////    // 获取泛微userid
////    public function huoqufanweiuserid(Request $request)
////    {
////        $jobNumber = $request->input('jobnumber', '');
////
////        if (empty($jobNumber)) {
////            return response()->json(
////                [
////                    'error' => 10001,
////                    'errorMsg'=>'',
////                    'success' => false,
////                    'data' => [],
////                    'message' => "jobnumber不能为空",
////                ]
////            );
////        }
////
////
////
////        $hrmresourceModel = Hrmresource::where('WORKCODE', $jobNumber)->where('STATUS', "!=", "5")->first();
////
//////        echo json_encode($hrmresourceModel);
////
////
////
////        if (empty($hrmresourceModel)) {
////            return response()->json(
////                [
////                    'error' => 10002,
////                    'errorMsg'=>'',
////                    'success' => false,
////                    'data' => [],
////                    'message' => "未找到泛微用户jobNumber:{$jobNumber}",
////                ]
////            );
////        }
////
////        return response()->json(
////            [
////                'error' => 0,
////                'errorMsg'=>'',
////                'success' => true,
////                'data' => ['fanweiUserId' => $hrmresourceModel['ID'], 'jobtitle'=>$hrmresourceModel['JOBTITLE'], 'department' => $hrmresourceModel['DEPARTMENTID']],
////                'message' => "ok",
////            ]
////        );
////
////    }

}
