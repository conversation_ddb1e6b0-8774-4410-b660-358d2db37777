<?php

namespace App\Http\Controllers\Api;

use App\Lib\Kdd\Aescbc;
use App\Lib\Kdd\CommonConst;
use App\Models\DdYuangong;
use App\Models\Guanyuan\GuanyuanZhanghao;
use App\Models\Jdb\YzjPerson;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Log;

class GyController extends BaseController
{

    // 单点登录for课叮当
    public function sso(Request $request)
    {
        $token = $request->input('token', '');
        $token = Aescbc::decryptWithOpenssl($token, CommonConst::KDD_KEY, CommonConst::KDD_IV);
//echo $token;die;
        if (!$token) {
            // return "token 错误，请重新传入正确token。";
            //header("HTTP/1.1 404 Not Found");
            header("location:https://jidebao.guandatacloud.com");

            //header("status: 404 not found");
            return;
        }
        $token = json_decode($token, true);
        if (!$token) {
            //  return "token 错误，请重新传入正确token2。";
            //header("HTTP/1.1 404 Not Found");
            header("location:https://jidebao.guandatacloud.com");
            //header("status: 404 not found");
            return;
        }

        // 表单填报
        $formId = $token['formid'] ?? ''; //$request->input('formid', "");
        // 登录账号
        $account = $token['account'] ?? ''; //$request->input('account', "");
        // 路径
        $path = $token['path'] ?? ''; //$request->input('path', "");
        // 其他参数
        $params = $token['params'] ?? []; //$request->input('params', []);
        // 时间
        $timestamp = $token['timestamp'] ?? 0; //$request->input('params', []);
        // 是否是移动端
        $yidongduan = $token['yidongduan'] ?? 0;
        // 页切参数
        $anchor = $token['anchor']??'';

        $time = time();

        if (!(($time - 300) <= $timestamp && $timestamp <= ($time + 300))) {
            // 时间戳记超过5分钟
            //header("HTTP/1.1 404 Not Found");
            //header("status: 404 not found");
            header("location:https://jidebao.guandatacloud.com");
            return;
        }

//        // 表单填报
//        $formId = $request->input('formid', "");
//        // 登录账号
//        $account = $request->input('account', "");
//        // 路径
//        $path = $request->input('path', "");
//        // 其他参数
//        $params = $request->input('params', []);

        try {
            $jobNo = $account; //$yzjUser['jobNo'];

            $domainId = 'guanbi';
            $fp=fopen("/data/etc/guandata_private_key.pem","r");
            $privateKey=fread($fp,8192);
            fclose($fp);

            $fp=fopen("/data/etc/guandata_public_key.pem","r");
            $publicKey=fread($fp,8192);
            fclose($fp);

            // 加密后的数据
            $encrypted = "";
            $piKey=openssl_pkey_get_private($privateKey);//这个函数可用来判断私钥是否是可用的，可用返回资源id Resource id
            $puKey=openssl_pkey_get_public($publicKey);//这个函数可用来判断公钥是否是可用的
            $data = json_encode([
                'domainId' => $domainId,
                'externalUserId' => $jobNo,
                'timestamp' => time(),
            ]);

            openssl_private_encrypt($data,$encrypted,$piKey);//私钥加密
            $encrypted = base64_encode($encrypted);
            $encrypted = bin2hex($encrypted);

            $queryparams = [
                'provider'=>'guanbi',
                'ssoToken' => $encrypted,
                'providerName' => 'guanbi',
                'domainId' => 'guanbi',
                'externalUserId' => $jobNo,
                'ps'=>'iframe',
                'anchor'=>$anchor,
            ];
//            if ($yidongduan == 1) {
//                $queryparams['pref.HostNavOnly'] = true;
//            }

            $queryparams = array_merge($queryparams,$params );

            $queryparams = http_build_query($queryparams);

            if (!empty($formId)) {
                // 表单填报
                $redirectUrl = "https://jidebao.guandatacloud.com/survey-engine/form-data/{$formId}?{$queryparams}";
            } else if (!empty($path)) {
                if (substr($path, -1) != '/') {
                    $path = $path . "/";
                }
                if (substr($path, 0,1) != '/') {
                    $path = "/" . $path;
                }
                if ($yidongduan == 1) {
                    $path = "/m" . $path;
                }
                // 自定义url地址
                $redirectUrl = "https://jidebao.guandatacloud.com{$path}{$formId}?{$queryparams}";
            } else {
                $redirectUrl = "https://jidebao.guandatacloud.com?{$queryparams}";
            }

            header("location:{$redirectUrl}");
            return;
        } catch (\Exception $e) {

            header("location:https://jidebao.guandatacloud.com");
            return;
        }
    }




    // 单点登录for工作台
    public function ceogongzuotaisso(Request $request)
    {

//        Log::debug(json_encode(['act'=>'ceogongzuotaisso', 'data'=>$request->all()]));

//        $token = $request->input('token', '');
//        $token = Aescbc::decryptWithOpenssl($token, CommonConst::KDD_KEY, CommonConst::KDD_IV);
//
//        if (!$token) {
//            // return "token 错误，请重新传入正确token。";
//            //header("HTTP/1.1 404 Not Found");
//            header("location:https://jidebao.guandatacloud.com");
//
//            //header("status: 404 not found");
//            return;
//        }
//        $token = json_decode($token, true);
//        if (!$token) {
//            //  return "token 错误，请重新传入正确token2。";
//            //header("HTTP/1.1 404 Not Found");
//            header("location:https://jidebao.guandatacloud.com");
//            //header("status: 404 not found");
//            return;
//        }

        // iframe key
        // aD1PIBKWByvr3bVQdOSDazbHCZws1zXT

        // 钉钉corpid
        $corpid = $request->input('corpid', "");
        // 钉钉userid
        $userid = $request->input('userid', "");
        $timestamp = $request->input('timestamp', '');
        $sign = $request->input('sign', '');
        $shaixuanqiJobNo = $request->input('shaixuanqijobno', '');

        // 根据timestamp, secret计算签名值
        $s = hash_hmac('sha256', "$userid\n$timestamp", 'aD1PIBKWByvr3bVQdOSDazbHCZws1zXT', true);
        $signature = bin2hex($s);

        Log::debug(json_encode(['act'=>'ceogongzuotaisso', 'data'=>$request->all(), 'timestamp' => $timestamp, 'sign'=>$sign, 'signature'=>$signature]));


//        if ($signature != $sign) {
//            echo "sign failed";
//            exit;
//        }



        // 表单填报
        $formId = $request->input('formid', "");
        // 登录账号
        $account = $request->input('account', "");

        // 路径
        $path = $request->input('path', "");
        // 其他参数
        $params = $request->input('params', []);
        // 是否是移动端
        $yidongduan = $request->input('yidongduan', 0);

        $newAccount = $request->input('userid', "");
        $ddYuan = DdYuangong::where('userid', $newAccount)->first();
        if (empty($ddYuan)) {
            return response()->json(
                [
                    'error' => 10000,
                    'errorMsg'=>'',
                    'success' => false,
                    'data' => [],
                    'message' => "请传入正确钉钉userid",
                ]
            );
        }





        $ddYuangongModel = DdYuangong::where('userid', $userid)->first();
        if (empty($ddYuangongModel)) {
            echo "userid:{$userid} failed";
            exit;
        }


//        $yzjUser = $request->session()->get('yzjUser');

//        // 表单填报
////        $formId = $request->input('formid', "");

        $jobNo = $ddYuangongModel['job_number']; //$yzjUser['jobNo'];
        $userId = $ddYuangongModel['userid']; //$userid; //$yzjUser['userid'];


        if (!empty($shaixuanqiJobNo)) {
            $params[$shaixuanqiJobNo] =  $jobNo;
        }


        $zhanghaoMode = GuanyuanZhanghao::where("账号", $jobNo)->first();
        if (empty($zhanghaoMode)) {
            $zhanghaoMode = GuanyuanZhanghao::where("钉钉账号", $userId)->first();
        }

        if (empty($zhanghaoMode)) {
            $yzjOpenId = YzjPerson::select(['openId'])->where("jobNo", $jobNo)->first();
            if (!empty($yzjOpenId)) {
                $zhanghaoMode = GuanyuanZhanghao::where("云之家账号", $yzjOpenId['openId'])->first();
            }
        }

        if (in_array($userid, ['619268838', '619275697', '622672946'])) {
            $jobNo = 'yangmingtan';
        } else if (in_array($userid, ['619276838'])) {
            $jobNo = 'Karen';
        } else if (in_array($userid, ['619269002'])) {
            $jobNo = 'liaohuili';
        } else if (in_array($userid, ['619267536'])) {
            $jobNo = 'wangyuehong';
        } else if (!empty($zhanghaoMode) && !empty($zhanghaoMode['账号'])) {
            // $jobNo = 'juesepingtai'; // $zhanghaoMode['账号'] ?? '';
            $jobNo = $zhanghaoMode['账号'] ?? '';
        }


//        // 时间
//        $timestamp = $token['timestamp'] ?? 0; //$request->input('params', []);
//        $time = time();
//
//        if (!(($time - 300) <= $timestamp && $timestamp <= ($time + 300))) {
//            // 时间戳记超过5分钟
//            //header("HTTP/1.1 404 Not Found");
//            //header("status: 404 not found");
//            header("location:https://jidebao.guandatacloud.com");
//            return;
//        }

//        // 表单填报
//        $formId = $request->input('formid', "");
//        // 登录账号
//        $account = $request->input('account', "");
//        // 路径
//        $path = $request->input('path', "");
//        // 其他参数
//        $params = $request->input('params', []);

        try {
//            $jobNo = $account; //$yzjUser['jobNo'];

            $domainId = 'guanbi';
            $fp=fopen("/data/etc/guandata_private_key.pem","r");
            $privateKey=fread($fp,8192);
            fclose($fp);

            $fp=fopen("/data/etc/guandata_public_key.pem","r");
            $publicKey=fread($fp,8192);
            fclose($fp);

            // 加密后的数据
            $encrypted = "";
            $piKey=openssl_pkey_get_private($privateKey);//这个函数可用来判断私钥是否是可用的，可用返回资源id Resource id
            $puKey=openssl_pkey_get_public($publicKey);//这个函数可用来判断公钥是否是可用的
            $data = json_encode([
                'domainId' => $domainId,
                'externalUserId' => $jobNo,
                'timestamp' => time(),
            ]);

            openssl_private_encrypt($data,$encrypted,$piKey);//私钥加密
            $encrypted = base64_encode($encrypted);
            $encrypted = bin2hex($encrypted);

            $queryparams = [
                'provider'=>'guanbi',
                'ssoToken' => $encrypted,
                'providerName' => 'guanbi',
                'domainId' => 'guanbi',
                'externalUserId' => $jobNo,
                'ps'=>'iframe',
            ];
            $queryparams = array_merge($queryparams,$params );

            $queryparams = http_build_query($queryparams);

            if (!empty($formId)) {
                // 表单填报
                $redirectUrl = "https://jidebao.guandatacloud.com/survey-engine/form-data/{$formId}?{$queryparams}";
            } else if (!empty($path)) {
                if (substr($path, -1) != '/') {
                    $path = $path . "/";
                }
                if (substr($path, 0,1) != '/') {
                    $path = "/" . $path;
                }
                if ($yidongduan == 1) {
                    $path = "/m" . $path;
                }
                // 自定义url地址
                $redirectUrl = "https://jidebao.guandatacloud.com{$path}{$formId}?{$queryparams}";
            } else {
                $redirectUrl = "https://jidebao.guandatacloud.com?{$queryparams}";
            }

            header("location:{$redirectUrl}");
            return;
        } catch (\Exception $e) {

            header("location:https://jidebao.guandatacloud.com");
            return;
        }
    }

}
