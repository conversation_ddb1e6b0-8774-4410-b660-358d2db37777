<?php


namespace App\Http\Controllers\Api;

use App\Http\Requests\Api\Dtb\PersonRequest;
use App\Lib\Kdd\Aescbc;
use App\Lib\Kdd\CalcSign;
use App\Lib\Kdd\CommonConst;
use App\Models\Dtb\DtbJuese;
use App\Models\Dtb\DtbJueseEntry;
use App\Models\Fanwei\Hrmresource;
use App\Models\Fanwei\UfQuaManagement;
use App\Models\Fanwei\UfQuaName;
use App\Models\Fanwei\WorkflowSelectitem;
use App\Models\Jdb\YzjDept;
use App\Models\Jdb\YzjPerson;
use App\Models\Jdb\YzjPersonJob;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Log;


class FanweiZizhibaoguanController extends BaseController
{
    // 获取泛微自制保管人列表
    // uf_qua_management
    public function zizhibaoguanren(Request $request)
    {
        // 公司名称
        // gsmc

        // 资质分类
        // zzfl 9691

        // 保管人
        // bgr

        // 状态
        // zt 9698

        $zzflSelectId = 9691;




        $ufQuanNameModelById = UfQuaName::all()->keyBy('id');

        $selectItemsModel = WorkflowSelectitem::select(['fieldid', 'selectvalue', 'selectname'])->whereIn('fieldid', [9691, 9698])->get();
        $selectItemsModelByFieldId = $selectItemsModel->groupBy('fieldid');
        foreach ($selectItemsModelByFieldId as $key=>$model) {
            $selectItemsModelByFieldId[$key] = $model->keyBy('selectvalue');
        }

        $hrmresourcesModel = Hrmresource::select(['id', 'lastname', 'workcode'])->where('status', "!=", "5")->where('accounttype',  0)->get();
        $hrmresourcesModelByUserId = $hrmresourcesModel->keyBy('id');

        $list = [];
        $baoguanrensModel = UfQuaManagement::whereIn('zt', [0, 1])->get();

        try {

            foreach ($baoguanrensModel as $model) {
                foreach ($model->bgr as $fanweiuserid) {

                    if (empty($fanweiuserid) || !isset($hrmresourcesModelByUserId[$fanweiuserid])) continue;

                    $fanweiUserRow = $hrmresourcesModelByUserId[$fanweiuserid];

                    if (!isset($list[$fanweiUserRow['workcode']])) $list[$fanweiUserRow['workcode']] = ['jobnumber'=>'' ,'text'=>'', 'list'=>[]];

                    $list[$fanweiUserRow['workcode']]['jobnumber'] = $hrmresourcesModelByUserId[$fanweiuserid]['workcode'];
                    $list[$fanweiUserRow['workcode']]['list'][] = [
                        'fanweiuserid' => $hrmresourcesModelByUserId[$fanweiuserid]['id'],
                        'fanweiname' => $hrmresourcesModelByUserId[$fanweiuserid]['lastname'],
                        'jobnumber' => $hrmresourcesModelByUserId[$fanweiuserid]['workcode'],

                        'gsmc' => $ufQuanNameModelById[$model['gsmc']]['zzmc'] ?? '',
                        'zzfl' => $selectItemsModelByFieldId[$zzflSelectId][$model['zzfl']]['selectname'] ?? '',

                        'text' => ($ufQuanNameModelById[$model['gsmc']]['zzmc'] ?? '') . ","
                            . ($selectItemsModelByFieldId[$zzflSelectId][$model['zzfl']]['selectname'] ?? ''),
                    ];
                }
            }

            foreach ($list as $key=>$row) {
                $list[$key]['text'] = implode("\n", array_column($row['list'],'text'));
            }

        } catch (\Exception $e) {
            return response()->json(
                [
                    'error' => 10001,
                    'errorMsg'=>'',
                    'success' => false,
                    'data' => [],
                    'message' => "服务器异常请联系管理员，e:" . $e->getMessage(),
                ]
            );
        }

        return response()->json(
            [
                'error' => 0,
                'errorMsg'=>'',
                'success' => true,
                'data' => [
                    'list' => array_values($list),
                ],
                'message' => "ok",
            ]
        );
    }

}
