<?php

namespace App\Http\Controllers\Api;

use App\Lib\Kdd\Aescbc;
use App\Lib\Kdd\CommonConst;
use App\Models\DdYuangong;
use App\Models\Guanyuan\GuanyuanZhanghao;
use App\Models\Jdb\YzjPerson;
use App\Models\Qiwei\QiweiLianxiwo;
use App\Models\Qiwei\QiweiXiaoqi;
use App\Services\BeisenApi\BeisenService;
use App\Services\Qiwei\QiweiApiService;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;

class KddQiweiController extends BaseController
{

    // 获取企微园联系我二维码-97调用
    public function lianxiwoerweima(Request $request)
    {
        $xiaoqubianhao = $request->input('xiaoqubianhao', '');
        $qudao = $request->input('qudao', '');


        if (empty($xiaoqubianhao)) {
            return response()->json(
                [
                    'error' => 10000,
                    'errorMsg'=>'',
                    'success' => false,
                    'data' => [],
                    'message' => "校区编号不能为空",
                ]
            );
        }
        if (mb_strlen($qudao) > 20) {
            return response()->json(
                [
                    'error' => 10004,
                    'errorMsg'=>'',
                    'success' => false,
                    'data' => [],
                    'message' => "渠道长度不能超过20个字符",
                ]
            );
        }

        $beisenService = new BeisenService();
        $beisenBumenListModel = $beisenService->getBeisenBumenModel();
        $beisenBumenListModel = $beisenBumenListModel->whereIn('extzuzhileixing', [8, 14, 20]);
        $beisenBumenListModel = $beisenBumenListModel->where('extyewuxitongbianma', $xiaoqubianhao);
        $beisenBumenModel = $beisenBumenListModel->first();

        if (empty($beisenBumenModel)) {
            return response()->json(
                [
                    'error' => 10001,
                    'errorMsg'=>'',
                    'success' => false,
                    'data' => [],
                    'message' => "未找到{$xiaoqubianhao}校区/园所",
                ]
            );
        }

        $xiaoqiModel = QiweiXiaoqi::where('changmingcheng', $beisenBumenModel->changmingcheng)->where('zhuangtai', 1)->first();
        if (empty($xiaoqiModel)) {
            $xiaoqiModel = QiweiXiaoqi::where('extyewuxitongbianma', $xiaoqubianhao)->where('zhuangtai', 1)->first();
            if (empty($xiaoqiModel)) {
                return response()->json(
                    [
                        'error' => 10002,
                        'errorMsg'=>'',
                        'success' => false,
                        'data' => [],
                        'message' => "未找到{$xiaoqubianhao} 小奇账号",
                    ]
                );
            }
        }
        if($xiaoqubianhao == '01463'){
            return response()->json(
                [
                    'error' => 0,
                    'errorMsg'=>'',
                    'success' => true,
                    'data' => [
                        'xiaoqu' => [
                            $xiaoqubianhao => [
                                'qiweiconfigid' => 'LinShiChuLi',
                                'erweimadizhi' => 'https://oss.kidcastle.cn/manage/202503071158x688578254.png',
                                'qudao' => 1,
                            ]
                        ]
                    ],
                    'message' => "",
                ]
            );
        }

        $qiweiLianxiwo = QiweiLianxiwo::where('qiweiyuangongbianhao', $xiaoqiModel->qiweiyuangongbianhao)->where('qudao', "kdd_{$qudao}")->first();
        if (!empty($qiweiLianxiwo)) {
            return response()->json(
                [
                    'error' => 0,
                    'errorMsg'=>'',
                    'success' => true,
                    'data' => [
                        'xiaoqu' => [
                            $xiaoqubianhao => [
                                'qiweiconfigid' => $qiweiLianxiwo->qiweiconfigid,
                                'erweimadizhi' => $qiweiLianxiwo->qiweierweima,
                                'qudao' => $qudao,
                            ]
                        ]
                    ],
                    'message' => "",
                ]
            );
        } else {
            $qiweiApiService = new QiweiApiService('ww8977cb1b47249e00', '1000009');

            $lianxiwoBody = ['type'=>1, 'scene'=>2, 'user'=>$xiaoqiModel->qiweiyuangongbianhao];
            if (!empty($qudao)) {
                $lianxiwoBody['state'] = "kdd_{$qudao}";
            }

            $rep = $qiweiApiService->addLianxiren(
                $lianxiwoBody
            );
//                ['type'=>1, 'scene'=>2, 'user'=>$xiaoqiModel->qiweiyuangongbianhao]

            if ($rep['errcode'] != 0) {
                return response()->json(
                    [
                        'error' => 10003,
                        'errorMsg'=>'',
                        'success' => false,
                        'data' => [],
                        'message' => "校区{$xiaoqubianhao}, userid:" . $xiaoqiModel->qiweiyuangongbianhao . "生成错误，请联系管理员.",
                    ]
                );
            } else {

                $qiweiLianxiwo = QiweiLianxiwo::create([
                    'qiweiyuangongbianhao' => $xiaoqiModel->qiweiyuangongbianhao,
                    'qudao' => "kdd_{$qudao}",
                    'qiweiconfigid' => $rep['config_id'],
                    'qiweierweima' => $rep['qr_code'],
                    'zhuangtai'=>1,
                ]);

            }
            return response()->json(
                [
                    'error' => 0,
                    'errorMsg'=>'',
                    'success' => true,
                    'data' => [
                        'xiaoqu' => [
                            $xiaoqubianhao => [
                                'qiweiconfigid' => $qiweiLianxiwo->qiweiconfigid,
                                'erweimadizhi' => $qiweiLianxiwo->qiweierweima,
                                'qudao' => $qudao,
                            ]
                        ]
                    ],
                    'message' => "",
                ]
            );
        }
    }

    //生成新新的二维码 20250707
    public function lianxiwoerweimaadd(Request $request){
        $xiaoqubianhao = $request->input('xiaoqubianhao', '');
        $qudao = $request->input('qudao', '');
        if (empty($xiaoqubianhao)) {
            return response()->json(
                [
                    'error' => 10000,
                    'errorMsg'=>'',
                    'success' => false,
                    'data' => [],
                    'message' => "校区编号不能为空",
                ]
            );
        }
        $beisenService = new BeisenService();
        $beisenBumenListModel = $beisenService->getBeisenBumenModel();
        $beisenBumenListModel = $beisenBumenListModel->whereIn('extzuzhileixing', [8, 14, 20]);
        $beisenBumenListModel = $beisenBumenListModel->where('extyewuxitongbianma', $xiaoqubianhao);
        $beisenBumenModel = $beisenBumenListModel->first();
        if (empty($beisenBumenModel)) {
            return response()->json(
                [
                    'error' => 10001,
                    'errorMsg'=>'',
                    'success' => false,
                    'data' => [],
                    'message' => "未找到{$xiaoqubianhao}校区/园所",
                ]
            );
        }
//        $xiaoqiModel = QiweiXiaoqi::where('extyewuxitongbianma', $xiaoqubianhao)->where('zhuangtai', 1)->first();
//        if (empty($xiaoqiModel)) {
//        }

        $qiweiyuangongbianhao = '1100002551';

        $qiweiApiService = new QiweiApiService('ww8977cb1b47249e00', '1000009');
        $lianxiwoBody = ['type'=>1, 'scene'=>2, 'user'=>$qiweiyuangongbianhao];
        if (!empty($qudao)) {
            $lianxiwoBody['state'] = "kdd_{$qudao}";
        }
print_r($lianxiwoBody);die;
        $rep = $qiweiApiService->addLianxiren(
            $lianxiwoBody
        );
//                ['type'=>1, 'scene'=>2, 'user'=>$xiaoqiModel->qiweiyuangongbianhao]

        if ($rep['errcode'] != 0) {
            return response()->json(
                [
                    'error' => 10003,
                    'errorMsg'=>'',
                    'success' => false,
                    'data' => [],
                    'message' => "校区{$xiaoqubianhao}, userid:" . $xiaoqiModel->qiweiyuangongbianhao . "生成错误，请联系管理员.",
                ]
            );
        } else {

            $qiweiLianxiwo = QiweiLianxiwo::create([
                'qiweiyuangongbianhao' => $xiaoqiModel->qiweiyuangongbianhao,
                'qudao' => "kdd_{$qudao}",
                'qiweiconfigid' => $rep['config_id'],
                'qiweierweima' => $rep['qr_code'],
                'zhuangtai'=>1,
            ]);

            print_r($rep);die;
        }
        return response()->json(
            [
                'error' => 0,
                'errorMsg'=>'',
                'success' => true,
                'data' => [
                    'xiaoqu' => [
                        $xiaoqubianhao => [
                            'qiweiconfigid' => $qiweiLianxiwo->qiweiconfigid,
                            'erweimadizhi' => $qiweiLianxiwo->qiweierweima,
                            'qudao' => $qudao,
                        ]
                    ]
                ],
                'message' => "",
            ]
        );

    }

}
