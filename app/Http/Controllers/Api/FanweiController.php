<?php


namespace App\Http\Controllers\Api;

use App\Http\Requests\Api\Dtb\PersonRequest;
use App\Lib\Kdd\Aescbc;
use App\Lib\Kdd\CalcSign;
use App\Lib\Kdd\CommonConst;
use App\Models\Dtb\DtbJuese;
use App\Models\Dtb\DtbJueseEntry;
use App\Models\Fanwei\Hrmresource;
use App\Models\Fanwei\UfBanzu;
use App\Models\Fanwei\ViewXingzhengzuzhi;
use App\Models\Fanwei\WorkflowSelectitem;
use App\Models\Jdb\YzjDept;
use App\Models\Jdb\YzjPerson;
use App\Models\Jdb\YzjPersonJob;
use App\Services\Fanwei\FanweiApiService;
use Illuminate\Database\Query\Builder;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Log;


class FanweiController extends BaseController
{
    // 请假单据测试
    public function qjd(Request $request)
    {

        Log::warning(json_encode([
            $request->all(),
        ]));


        return response()->json([
            'result' => 1,
        ]);

        // var_dump($request->all());
    }

    // 获取泛微userid-建波调用
    public function huoqufanweiuserid(Request $request)
    {
        $jobNumber = $request->input('jobnumber', '');

        if (empty($jobNumber)) {
            return response()->json(
                [
                    'error' => 10001,
                    'errorMsg'=>'',
                    'success' => false,
                    'data' => [],
                    'message' => "jobnumber不能为空",
                ]
            );
        }



        $hrmresourceModel = Hrmresource::where('WORKCODE', $jobNumber)->where('STATUS', "!=", "5")->first();

//        echo json_encode($hrmresourceModel);



        if (empty($hrmresourceModel)) {
            return response()->json(
                [
                    'error' => 10002,
                    'errorMsg'=>'',
                    'success' => false,
                    'data' => [],
                    'message' => "未找到泛微用户jobNumber:{$jobNumber}",
                ]
            );
        }

        $fanweiApiService = new FanweiApiService();


        return response()->json(
            [
                'error' => 0,
                'errorMsg'=>'',
                'success' => true,
                'data' => [
                    'fanweiUserId' => $hrmresourceModel['ID'],
                    'jobtitle'=>$hrmresourceModel['JOBTITLE'],
                    'department' => $hrmresourceModel['DEPARTMENTID'],

                    'fanweiUserIdJiami' => $fanweiApiService->encrypt($hrmresourceModel['ID']),

                ],
                'message' => "ok",
            ]
        );

    }

    // 获取申请园校-浏览框数据-建波调用-排课调整申请流程使用
    public function liulankuangshenqingyuanxiao(Request $request)
    {
        $xiaoqubianhao = $request->input('xiaoqubianhao', '');

        $shenqingyuanxiaosModel = ViewXingzhengzuzhi::select(['id', 'departmentname', 'ywxtbm']);
        if (!empty($xiaoqubianhao)) {
            $xiaoqubianhao = explode(',', $xiaoqubianhao);
            $shenqingyuanxiaosModel = $shenqingyuanxiaosModel->whereIn('ywxtbm', $xiaoqubianhao);
        }

        $shenqingyuanxiaosModel->where(function( $query) {
            $query->whereIn('zzlx', ['园_园','校_校','早教_校'])
                ->orWhere('departmentname', 'OMO中心');
        });

        $shenqingyuanxiaosModel = $shenqingyuanxiaosModel->get();

        return response()->json(
            [
                'error' => 0,
                'errorMsg'=>'',
                'success' => true,
                'data' => [
                    'shenqingyuanxiao' => $shenqingyuanxiaosModel
                ],
                'message' => "ok",
            ]
        );




    }

    // 获取班组
    public function shenqingtiaokebanzu(Request $request)
    {

        $banzusModel = UfBanzu::select(['id','bz','bh', 'form_biz_id'])->where('sfkq',1)->get();

        $banzusArr = $banzusModel->toArray();

        $banzusselectmodeModel = WorkflowSelectitem::where('FIELDID', '16135')->get();
        $banzusselectmodeModelByName = $banzusselectmodeModel->keyBy('SELECTNAME');


        foreach ($banzusArr as $key=>$row) {
            $banzusArr[$key]['selectvalue'] = '';
            if(isset($banzusselectmodeModelByName[$row['bz']])) {
                $banzusArr[$key]['selectvalue'] = $banzusselectmodeModelByName[$row['bz']]['SELECTVALUE'];
            }
        }


        return response()->json(
            [
                'error' => 0,
                'errorMsg'=>'',
                'success' => true,
                'data' => [
                    'banzu' => $banzusArr
                ],
                'message' => "ok",
            ]
        );

    }

    public function shenqingtiaokebanzuv2(Request $request)
    {
        $banzusModel = WorkflowSelectitem::where('FIELDID', '16135')->get();

        return response()->json(
            [
                'error' => 0,
                'errorMsg'=>'',
                'success' => true,
                'data' => [
                    'banzu' => $banzusModel
                ],
                'message' => "ok",
            ]
        );

    }
}
