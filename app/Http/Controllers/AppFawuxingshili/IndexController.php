<?php

namespace App\Http\Controllers\AppFawuxingshili;

use App\Http\Requests\StorePostRequest;
use App\Http\Resources\Yzj\YzjShenpiCollection;
use App\Lib\DemoInterface;
use App\Lib\DemoOne;
use App\Lib\DemoTwo;
use App\Lib\Kdd\CalcSign;
use App\Lib\Kdd\CommonConst;
use App\Models\Jdb\YzjDept;
use App\Models\YzjShenpi;
use App\Services\AescbcService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Facades\DemoInterfaceFacade;

class IndexController extends BaseController
{

    public function index(Request $request)
    {
        $user = $request->session()->get('user');
        $yzjUser = $request->session()->get('yzjUser');

        $token = \App\Services\AescbcService::encryptWithOpenssl(
            json_encode(['user' => $user, 'yzjUser' => $yzjUser, 'timestamp' => time()]),
            \App\Services\CommonConst::ZIXITONG_KEY,
            \App\Services\CommonConst::ZIXITONG_IV);

        $redirectUrl = 'https://eduapp.kidcastle.com.cn/guandata/deadlinenotice?acToken=' . urlencode($token);

        header("HTTP/1.1 302 Found");
        header("Location: {$redirectUrl}");
        return false;

    }
}
