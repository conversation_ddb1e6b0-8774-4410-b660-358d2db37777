<?php

namespace App\Http\Controllers\AppDuanxinfasongjihua;

use App\Exports\CaiwuyuanYusuanExport;
use App\Exports\MingdanmobanExport;
use App\Http\Requests\StorePostRequest;
use App\Http\Resources\AppDdshipei\DdshipeiCollection;
use App\Http\Resources\AppDuanxinfasongjihua\DuanxinfasongjihuaCollection;
use App\Http\Resources\AppDuanxinfasongjihua\DuanxinfasongjihuamingdanCollection;
use App\Http\Resources\AppYuangonghuamingce\YuangongCollection;
use App\Http\Resources\Yzj\YzjCaiwuyuan\QuanxianCollection;
use App\Http\Resources\Yzj\YzjShenpiCollection;
use App\Imports\CaiwuyuanYusuanImport;
use App\Imports\JianheImport;
use App\Imports\MingdanmobanImport;
use App\Lib\DemoInterface;
use App\Lib\DemoOne;
use App\Lib\DemoTwo;
use App\Lib\Kdd\CalcSign;
use App\Lib\Kdd\CommonConst;
use App\Models\AppDuanxinfasongjihua;
use App\Models\AppDuanxinfasongjihuaFenlu;
use App\Models\AppYuangonghuamingceYuangong;
use App\Models\DdAppshipei;
use App\Models\Jdb\YzjCaiwuyuanShangchuanquanxian;
use App\Models\Jdb\YzjDept;
use App\Models\YzjShenpi;
use App\Services\CaiwuyuanYusuanService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Facades\DemoInterfaceFacade;
use Maatwebsite\Excel\Facades\Excel;

class MingdanController extends BaseController
{

    public function index(Request $request, $id)
    {

        $yzjUser = $request->session()->get('yzjUser');
//        $quanxian = YzjCaiwuyuanShangchuanquanxian::getQuanxian($yzjUser['jobNo'], '员工花名册');
        $duanxinfasongjihuaModel = AppDuanxinfasongjihua::find($id);

        return Inertia::render('AppDuanxinfasongjihua/Mingdan', [
            'id' => $id,
            'duanxinfasongjihuaModel' => $duanxinfasongjihuaModel,

//            'yzjUser' => $yzjUser,
//            'quanxian' => $quanxian,
        ]);
    }

    // 下载名单模板
    public function mingdanmobanExport(Request $request)
    {
        return Excel::download(new MingdanmobanExport, "tpl_短信发送名单模板-". date("Y-m-d") .".xlsx");
    }

    // 名单模板导入
    public function mingdanmobanImport(Request $request)
    {


        if ($request->isMethod('post')) {

            $file = $request->file('file')->store('temp');
            $path = storage_path('app').'/'.$file;

            Excel::import(new MingdanmobanImport, $path);

            return response()->json([
                'success' => true,
                'error' => '',
                'errorCode' => 0,
                'data' => [
                ]
            ]);
        }
//        \Maatwebsite\Excel\Imports\HeadingRowFormatter::default('none');
//        $headings = (new \Maatwebsite\Excel\HeadingRowImport)->toArray(($path));
//
//        if (!(isset($headings[0][0][0]) && $headings[0][0][0] == '预算年度'
//            &&  isset($headings[0][0][2]) && $headings[0][0][2] == '月份' )) {
//            return response(json_encode(['error'=>0, 'errorMsg'=>"请上传正确预算模板文件，可在预算首页左上角找到按钮'下载预算模板'"]), 403);
//        }
//
//        try {
//            // 检查表头中年月
//            if (isset($headings[0][0])) {
//
//                $nian = $headings[0][0][1] ?? 0;
//                $yue = $headings[0][0][3] ?? 0;
//
//                if (!is_numeric($nian) || !is_numeric($yue)) {
//                    throw new \Exception("检查文件中年度或月份是否正确");
//                }
//
//                $nian = intval($nian);
//                $yue = intval($yue);
//
//                if (!(2024 <= $nian && $nian <= 2030)) {
//                    throw new \Exception("年度{$nian}错误,请填写此2024~2030区间年度");
//                }
//                if (!(1 <= $yue && $yue <= 12)) {
//                    throw new \Exception("月份{$yue}错误,请填写此1~12区间月份");
//                }
//
//            } else {
//                throw new \Exception("检查文件中年度或月份是否正确");
//            }
//        } catch (\Exception $e) {
//            return response(json_encode(['error'=>$e->getCode(), 'errorMsg'=>$e->getMessage()]), 403);
//        }
//
//        $collection = (new CaiwuyuanYusuanImport)->toCollection($path);
//
//        $collection = $collection[0];
//
//        $timestamp = date("Y-m-d H:i:s");
//        $collection = $collection->map(function($item, $key) use($nian, $yue, $timestamp, $yzjUser) {
//            $item['年'] = $nian;
//            $item['月'] = $yue;
//            $item['created_at'] = $timestamp;
//            $item['chuangjianrenId'] = $yzjUser['jobNo'];   // 提交人信息
//            $item['chuangjianrenName'] = $yzjUser['name'];
//            $item['bianmaxiangmu'] = $item['cloud编码'] . "-" . $item['预算项目编码'];
//
//            if (isset($item['决算数'])) unset($item['决算数']);
//
//            return $item;
//        });
//
//        $yusuanService = new CaiwuyuanYusuanService();
//        $quanxian = $yusuanService->getQuanxian($yzjUser['jobNo'], "预算");
//
//        try {
//            DB::beginTransaction();
//
//            // 检查是否权限不足
//            if (!isset($quanxian['jobNo']) || empty($quanxian['jobNo']) || empty($quanxian['xiaoquList'])) {
//                throw new \Exception("老师您权限不足请联系管理员杜丹");
//            }
//
//            $xiaoquCollect = collect($quanxian['xiaoquList']);
//            $xiaoquCollect = $xiaoquCollect->map(function($item, $key) {
//                $item['yusuankemuList'] = collect($item['yusuankemuList'])->keyBy('预算项目编码');
//                return $item;
//            });
//            $xiaoquCollect = $xiaoquCollect->keyBy('code');
//            $collection->each(function($item, $key) use ($xiaoquCollect) {
//                if (!(isset($xiaoquCollect[$item['cloud编码']]) && isset($xiaoquCollect[$item['cloud编码']]['yusuankemuList'][$item['预算项目编码']]))) {
//                    throw new \Exception("老师您权限不足请联系管理员杜丹");
//                }
//            });
//
//            // 锁定
//            $suodingModel = $yusuanService->getYusuansuoding("预算", sprintf("%4d-%02d", $nian, $yue), true);
//            // 检查是否已锁定
//            if (!empty($suodingModel['suodingAt']) || !empty($suodingModel['querenrenAt'])) {
//                throw new \Exception("{$nian}-{$yue} 年度月份已锁定请重新填写正确年月或联系管理员余家蓉,张佳佳取消锁定状态");
//            }
//
//            $collection->chunk(50)->each(function($item, $key) {
//                $rs = DB::connection("jdbmysql")->table("tpl_财务园_预算_v1")->insert($item->toArray());
//
//                if (!$rs) {
//                    throw new \Exception("预算插入失败，请检查文件内容是否填写正确");
//                }
//            });
//
//            DB::commit();
//        } catch (\Exception $e) {
//            DB::rollBack();
//
//            return response(json_encode(['error'=>$e->getCode(), 'errorMsg'=>$e->getMessage()]), 403);
//        }
//
//        return response()->json(
//            [
//                'error' => 0,
//                'errorMsg'=>'',
//                'success' => true,
//                'data' => '数据上传成功',
//                'message' => '',
//            ]);
    }


// 列表
    public function list(Request $request, $id)
    {
        if ($request->isMethod('get')) {

            $yzjUser = $request->session()->get('yzjUser');

//            $quanxian = YzjCaiwuyuanShangchuanquanxian::getQuanxian($yzjUser['jobNo'], '员工花名册');
//            $codeList = array_column($quanxian['list'], "code");


            $yuangongList = AppDuanxinfasongjihuaFenlu::where("duanxinfasongjihuaId", $id)->orderByDesc('id')->paginate(20);

//            $yuangongList = AppDuanxinfasongjihua::


            return new DuanxinfasongjihuamingdanCollection($yuangongList);
        }

    }

    // 保存创建
    public function store(Request $request)
    {
        if ($request->isMethod('post')) {

            // {"openId":"60e26896e4b01596045c9c38","phone":"18616126092","name":"\u5434\u78ca","photoUrl":"https:\/\/static.yunzhijia.com\/space\/c\/photo\/load?id=62c6788eec9ecd0001d7e347","jobNo":"0110210366","xiaoqubianhaoList":["01307","01412"]}
            $yzjUser = $request->session()->get('yzjUser');

            $yuangong = $request->get("yuangong", []);

            $ddShipeiModel = AppDuanxinfasongjihua::create($yuangong);

            return response()->json(
                [
                    'error' => 0,
                    'errorMsg'=>'',
                    'success' => true,
                    'data' => [],
                    'message' => 'ok',
                ]
            );
        }
    }

    public function update(Request $request, $id)
    {
        if ($request->isMethod('put')) {
            // {"openId":"60e26896e4b01596045c9c38","phone":"18616126092","name":"\u5434\u78ca","photoUrl":"https:\/\/static.yunzhijia.com\/space\/c\/photo\/load?id=62c6788eec9ecd0001d7e347","jobNo":"0110210366","xiaoqubianhaoList":["01307","01412"]}
            $yzjUser = $request->session()->get('yzjUser');

            $yuangong = $request->get("yuangong", []);



            $appShipeiModel = AppDuanxinfasongjihuaFenlu::find($yuangong['id']);

            if ($appShipeiModel) {
                $appShipeiModel['mingcheng'] = $yuangong['mingcheng'];
                $appShipeiModel['shouji'] = $yuangong['shouji'];
//                $appShipeiModel['zhuangtai'] = $yuangong['zhuangtai'];
                $appShipeiModel->save();
            }

            return response()->json(
                [
                    'error' => 0,
                    'errorMsg'=>'',
                    'success' => true,
                    'data' => [],
                    'message' => 'ok',
                ]
            );
        }
    }

    public function destroy(Request $request)
    {
        if ($request->isMethod('delete')) {
            $id = $request->get("id");

            $deleted = AppDuanxinfasongjihuaFenlu::where('id', $id)->delete();

            return response()->json(
                [
                    'error' => 0,
                    'errorMsg'=>'',
                    'success' => true,
                    'data' => [],
                    'message' => 'ok',
                ]
            );

        }
    }


//    // 权限列表
//    public function quanxianList(Request $request)
//    {
//        $yzjUser = $request->session()->get('yzjUser');
//        // $yzjUser['jobNo'] = '3120110055';
//        $quanxian = YzjCaiwuyuanShangchuanquanxian::getQuanxian($yzjUser['jobNo'], '员工花名册');
//        $curPage = $request->input('curPage', 1);
//        $pageSize = $request->input('pageSize', 20);
//        $xiaoquList = $request->input('codeList', []);
//
//        if (!isset($quanxian['list']) || empty($quanxian['list'])) {
//            $quanxian['list'] = [];
//        }
//        $quanxianList = array_slice($quanxian['list'], ($curPage-1) * $pageSize, $pageSize);
//
//        $list = [];
//        foreach ($quanxianList as $key=>$val) {
//            $list[] = [
//                '员工编号' => $quanxian['jobNo'],
//                '员工姓名' => $quanxian['name'],
//                '园编码' => $val['code'],
//                '园名称' => $val['title'],
//            ];
//        }
//
//        $page = new \Illuminate\Pagination\LengthAwarePaginator($list, count($quanxian['list']), $pageSize, $curPage);
//
//        return new QuanxianCollection($page);
//    }


}
