<?php

namespace App\Http\Controllers\AppDushujianhexunjian;

use App\Http\Requests\StorePostRequest;
use App\Http\Resources\Yzj\YzjShenpiCollection;
use App\Lib\DemoInterface;
use App\Lib\DemoOne;
use App\Lib\DemoTwo;
use App\Lib\Kdd\CalcSign;
use App\Lib\Kdd\CommonConst;
use App\Models\Jdb\YzjDept;
use App\Models\K3Dengluzhanghao;
use App\Models\K3Log;
use App\Models\YzjShenpi;
use App\Services\AescbcService;
use App\Services\K3\K3Service;
use Illuminate\Database\Query\Builder;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Facades\DemoInterfaceFacade;

class IndexController extends BaseController
{

    public function index(Request $request)
    {
        $user = $request->session()->get('user');
        $yzjUser = $request->session()->get('yzjUser');
//        $formid = $request->get('formid', '');
//        $formtype = $request->get('formtype', '');

//        if(($request->input('appid')??"") == "2612ba1a-2d52-4872-89b4-b79c322fdf1f") {
//            print_r($user);die;
//            echo 111;die;
//        }

//        mobile=&name=&pid=&department

        $params = [];

        $params['pid'] = $user['job_number'];
        $params['name'] = $user['name'];
        $params['mobile'] = $user['mobile'];


        $now = date("Y-m-d H:i:s");


        $user = DB::table('t_beisen_yuangong')
            ->leftJoin('t_beisen_yuangong_renzhi', 't_beisen_yuangong.userId', '=', 't_beisen_yuangong_renzhi.userId')
            ->leftJoin('t_beisen_bumen', 't_beisen_yuangong_renzhi.oIdDepartment', '=', 't_beisen_bumen.oId')
            ->select([
                't_beisen_yuangong.name','t_beisen_yuangong.gender','t_beisen_yuangong.mobilePhone',
                't_beisen_yuangong_renzhi.userId', 't_beisen_yuangong_renzhi.jobNumber', 't_beisen_yuangong_renzhi.oIdDepartment','t_beisen_yuangong_renzhi.serviceType',
                't_beisen_bumen.personInCharge','t_beisen_bumen.changmingcheng'
            ])
            ->whereIn("t_beisen_yuangong_renzhi.employType", [0, 2])
            ->whereIn("t_beisen_yuangong_renzhi.employeeStatus", [2, 3, 4, 5])
            ->where(function(Builder $query) {
                $query->where(function(Builder $query2) {
                    $query2->where('t_beisen_yuangong_renzhi.isCurrentRecord', 1)
                        ->where('t_beisen_yuangong_renzhi.serviceType', 0);
                });
                   // ->orWhere('t_beisen_yuangong_renzhi.serviceType', 1);
            })
            ->where("t_beisen_yuangong_renzhi.startDate", "<=", $now)
            ->where('t_beisen_yuangong_renzhi.stopDate', ">=", $now)
            ->where('t_beisen_yuangong_renzhi.stdIsDeleted', 0)
            ->where('t_beisen_yuangong.stdIsDeleted', 0)
            ->where('t_beisen_bumen.status', 1)
            ->where('t_beisen_bumen.shanchu', 0)
            ->where('t_beisen_bumen.stdIsDeleted', 0)
            ->where('t_beisen_bumen.startDate', '<=', $now)
            ->where('t_beisen_bumen.stopDate', '>=', $now)
            ->whereNotIn('t_beisen_bumen.code', ['RootOrg', 'DefaultDept'])
            ->where('t_beisen_yuangong_renzhi.jobNumber', $user['job_number'])
            ->first();


        $params['department'] = str_replace("_", "/", $user->changmingcheng);



//        ;


//
//        echo json_encode($user);
//
//        echo "\n <br />";
//
//
//        echo json_encode($user);
//        echo "\n<br />";
//        var_dump($user);
//
//
//
//
//        echo "asdf";
//
//        exit();
//
//        $k3Service = new K3Service();
//
//        $k3DengluzhanghaoModel = K3Dengluzhanghao::where("FUSERACCOUNT", "<>", "")->where("FUSERACCOUNT", $yzjUser['jobNo'])->first();
//
//        $account = "无此账号";
//        if (!empty($k3DengluzhanghaoModel)) {
//            $account = $k3DengluzhanghaoModel->FNAME;
//        }
//
//        $udData = $k3Service->calcUd(['username'=>$account, 'formid' => $formid, 'formtype' => $formtype]);
//
//        $ud = base64_encode(json_encode($udData));
//
//        K3Log::create([
//            'jobNo'=>$yzjUser['jobNo'],
//            'name' => $yzjUser['name'],
//            'k3account' => $account,
//            'ud' => ($udData),
//        ]);

        // 測試服
        //$redirectUrl = 'http://47.96.36.32:55520/k3cloud/html5/index.aspx?ud=' . $ud;
        // 正式服
//        if ($formtype == 'mobile') {
//
//            $redirectUrl = 'https://k3.kidcastle.com.cn/k3cloud/xmobile/cloud.html?ud=' . $ud . "&entryRole=xt&formId=ER_MBReimb_HomePage&formType=mobile&acctid=5ab0ff35434cf7";
//
////            $redirectUrl = 'https://k3.kidcastle.com.cn/k3cloud/html5/dform.aspx?ud=' . $ud;
//
//        } else {
//            $redirectUrl = 'https://k3.kidcastle.com.cn/K3Cloud/html5/index.aspx?ud=' . $ud;
//        }

        //$redirectUrl= "https://minbookpatrol.chevady.cn/jumpPage?" . http_build_query($params);

        $redirectUrl= "https://minbookpatrol.kidcastle.cn/jumpPage?" . http_build_query($params);



        header("HTTP/1.1 302 Found");
        header("Location: {$redirectUrl}");
        return false;
    }


    public function xunjiantw(Request $request)
    {
        $user = $request->session()->get('user');
        $yzjUser = $request->session()->get('yzjUser');
//        echo '<pre>';
//        print_r($user);
        //处理手机号
        if(preg_match('/^[9]\d{8}$/', trim($user['mobile']))){//$pattern = "/^(9(\d{8}))$/";
            $user['mobile'] = '0'.$user['mobile'];
        }

        $params = [];
        $params['pid'] = $user['job_number'];
        $params['name'] = $user['name'];
        $params['mobile'] = $user['mobile'];

        $now = date("Y-m-d H:i:s");

        $user = DB::table('temp_tw_teacher')
            ->select([
                'temp_tw_teacher.name','temp_tw_teacher.mobile','temp_tw_teacher.number','temp_tw_teacher.department'
            ])
            ->where('temp_tw_teacher.number', $user['job_number'])
            ->first();

        $params['department'] = $user->department??'';
//        print_r($params);die;
        $redirectUrl= "https://minbookpatrol.kidcastleapp.tw/jumpPage?" . http_build_query($params);
//        print_r($redirectUrl);die;
        header("HTTP/1.1 302 Found");
        header("Location: {$redirectUrl}");
        return false;
    }

    //考试测评系统
    public function kaoshiceping(Request $request)
    {
//echo '122';die;
        $user = $request->session()->get('user');
        $yzjUser = $request->session()->get('yzjUser');
        $fromArr = $request->query();
        if($user['appid'] == '44119117-3923-4a6a-b0bc-5bad206996ce'){
//            echo '<pre>';
//            echo '111';
//            print_r($user);
//            echo 2222;
//            print_r($yzjUser);
//            echo 3333;
//            print_r($fromArr);
//            echo 44444;
//            die;
        }
        if($user['job_number'] == '0111810002' || $user['job_number'] == '0110210420'){
//            echo '<pre>';
//            echo '111';
//            print_r($user);
//            echo '222';
//            print_r($yzjUser);
//            echo '333';
//            print_r($request->query());
//            die;
        }

        $params = [];
        $params['pid'] = $user['job_number'];
        $params['name'] = $user['name'];
        $params['mobile'] = $user['mobile'];

        $now = date("Y-m-d H:i:s");

        if($user['job_number']) {
            $user = DB::table('t_beisen_yuangong')
                ->leftJoin('t_beisen_yuangong_renzhi', 't_beisen_yuangong.userId', '=', 't_beisen_yuangong_renzhi.userId')
                ->leftJoin('t_beisen_bumen', 't_beisen_yuangong_renzhi.oIdDepartment', '=', 't_beisen_bumen.oId')
                ->leftJoin('t_beisen_zhiwu', 't_beisen_yuangong_renzhi.oIdJobPost', '=', 't_beisen_zhiwu.oId')
                ->select([
                    't_beisen_yuangong.name', 't_beisen_yuangong.gender', 't_beisen_yuangong.mobilePhone',
                    't_beisen_yuangong_renzhi.userId', 't_beisen_yuangong_renzhi.jobNumber', 't_beisen_yuangong_renzhi.oIdDepartment', 't_beisen_yuangong_renzhi.serviceType',
                    't_beisen_bumen.personInCharge', 't_beisen_bumen.changmingcheng', 't_beisen_zhiwu.code'
                ])
                ->whereIn("t_beisen_yuangong_renzhi.employType", [0, 2])
                ->whereIn("t_beisen_yuangong_renzhi.employeeStatus", [2, 3, 4, 5])
                ->where(function (Builder $query) {
                    $query->where(function (Builder $query2) {
                        $query2->where('t_beisen_yuangong_renzhi.isCurrentRecord', 1)
                            ->where('t_beisen_yuangong_renzhi.serviceType', 0);
                    });
                })
                ->where("t_beisen_yuangong_renzhi.startDate", "<=", $now)
                ->where('t_beisen_yuangong_renzhi.stopDate', ">=", $now)
                ->where('t_beisen_yuangong_renzhi.stdIsDeleted', 0)
                ->where('t_beisen_yuangong.stdIsDeleted', 0)
                ->where('t_beisen_bumen.status', 1)
                ->where('t_beisen_bumen.shanchu', 0)
                ->where('t_beisen_bumen.stdIsDeleted', 0)
                ->where('t_beisen_bumen.startDate', '<=', $now)
                ->where('t_beisen_bumen.stopDate', '>=', $now)
                ->whereNotIn('t_beisen_bumen.code', ['RootOrg', 'DefaultDept'])
                ->where('t_beisen_yuangong_renzhi.jobNumber', $user['job_number'])
                ->first();
        }

        $params['department'] = $user->changmingcheng?str_replace("_", "/", $user->changmingcheng):'';
        $params['code'] = $user->code?$user->code:'';
        if(isset($fromArr['type']) && $fromArr['type'] == '2'){
            //2  幼儿园-期末评鉴
            $params['type'] = 2;
        }else{
            //1  默认为  考试测评系统（成长中心-季度检定）
            $params['type'] = 1;
        }

        if(isset($fromArr['isformal']) && $fromArr['isformal'] == '1'){
            $redirectUrl= "https://staespad.kidcastle.cn/jumpPage?" . http_build_query($params);
        }else{
            $redirectUrl= "https://staespad.chevady.cn/jumpPage?" . http_build_query($params);
        }

        //正确的跳转地址： 测试服
        //https://staespad.chevady.cn/jumpPage?type=1&pid=0111810025&name=吴光辉&mobile=15958558256&department=吉的堡教育集团/墨智/IT产品研发部/后端开发部&code=CJGCS

        header("HTTP/1.1 302 Found");
        header("Location: {$redirectUrl}");
        return false;
    }


}
