<?php

namespace App\Http\Controllers\AppShujubao;

use App\Lib\Kdd\CalcSign;
use App\Lib\Kdd\CommonConst;
use App\Models\Guanyuan\GuanyuanZhanghao;
use App\Models\Jdb\YzjPerson;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Log;

class IndexController extends BaseController
{
    public function index(Request $request)
    {
        $yzjUser = $request->session()->get('yzjUser');

        // 表单填报
        $formId = $request->input('formid', "");

        $jobNo = $yzjUser['jobNo'];
        $userId = $yzjUser['userid'];

//        $jobNo='**********';
//        $userId='*********';

//        Log::info('99996666aaaaaaaa'.$request->url().'bbbbbbbbbb'.json_encode($yzjUser).'ccccccccc'.json_encode($formId));

        $zhanghaoMode = GuanyuanZhanghao::where("账号", $jobNo)->first();
//        Log::info('99996666zhanghao111'.json_encode($zhanghaoMode));
        if (empty($zhanghaoMode)) {
            $zhanghaoMode = GuanyuanZhanghao::where("钉钉账号", $userId)->first();
//            Log::info('99996666zhanghao222'.json_encode($zhanghaoMode));
        }

        if (empty($zhanghaoMode)) {
            $yzjOpenId = YzjPerson::select(['openId'])->where("jobNo", $jobNo)->first();
            if (!empty($yzjOpenId)) {
                $zhanghaoMode = GuanyuanZhanghao::where("云之家账号", $yzjOpenId['openId'])->first();
//                Log::info('99996666zhanghao333'.json_encode($zhanghaoMode));
            }
        }
        // 路径
        $path = $request->input('path', "");
        // 其他参数
        $params = $request->input('params', []);
        // 是否是移动端
        $yidongduan = $request->input('yidongduan', 0);

        try {

            //$jobNo = $yzjUser['jobNo'];
            $jobNo = $zhanghaoMode['账号'] ?? $yzjUser['jobNo'];

            // $jobNo = '**********';
            if($jobNo == '0111810002'){//97的账号 模拟登录
                $jobNo = 'lihui';
            }


            GuanyuanZhanghao::where("账号", $jobNo)->first();




            $domainId = 'guanbi';
            $fp=fopen("/data/etc/guandata_private_key.pem","r");
            $privateKey=fread($fp,8192);
            fclose($fp);

            $fp=fopen("/data/etc/guandata_public_key.pem","r");
            $publicKey=fread($fp,8192);
            fclose($fp);

            // 加密后的数据
            $encrypted = "";
            $piKey=openssl_pkey_get_private($privateKey);//这个函数可用来判断私钥是否是可用的，可用返回资源id Resource id
            $puKey=openssl_pkey_get_public($publicKey);//这个函数可用来判断公钥是否是可用的
            $data = json_encode([
                'domainId' => $domainId,
                'externalUserId' => $jobNo,
                'timestamp' => time(),
            ]);

            openssl_private_encrypt($data,$encrypted,$piKey);//私钥加密
            $encrypted = base64_encode($encrypted);
            $encrypted = bin2hex($encrypted);

            $queryparams = [
                'provider'=>'guanbi',
                'ssoToken' => $encrypted,
                'providerName' => 'guanbi',
                'domainId' => 'guanbi',
                'externalUserId' => $jobNo,
//                'ps'=>'iframe',
            ];

            $queryparams = array_merge($queryparams,$params);

            $queryparams = http_build_query($queryparams);





            if (!empty($formId)) {
                // 表单填报
                $redirectUrl = "https://jidebao.guandatacloud.com/survey-engine/form-data/{$formId}?{$queryparams}";
//                Log::info('99996666tiaozhuan111'."++".$formId."++".json_encode($queryparams));
            } else if (!empty($path)) {
                if (substr($path, -1) != '/') {
                    $path = $path . "/";
                }
                if (substr($path, 0,1) != '/') {
                    $path = "/" . $path;
                }
                if ($yidongduan == 1) {
                    $path = "/m" . $path;
                }
                // 自定义url地址
                $redirectUrl = "https://jidebao.guandatacloud.com{$path}{$formId}?{$queryparams}";
//                Log::info('99996666tiaozhuan222'."++".$formId."++".json_encode($queryparams));
            } else {
                $redirectUrl = "https://jidebao.guandatacloud.com?{$queryparams}";
//                Log::info('99996666tiaozhuan333'."++".$formId."++".json_encode($queryparams));
            }


//            if (empty($formId)) {
//                $redirectUrl = "https://jidebao.guandatacloud.com?{$params}";
//            } else {
//                $redirectUrl = "https://jidebao.guandatacloud.com/survey-engine/form-data/{$formId}?{$params}";
//            }

            header("location:{$redirectUrl}");

        } catch (\Exception $e) {

            header("location:https://jidebao.guandatacloud.com");
        }

    }
}


