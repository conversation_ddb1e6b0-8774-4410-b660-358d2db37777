<?php

namespace App\Http\Controllers\AppYuangonghuamingce;

use App\Http\Requests\StorePostRequest;
use App\Http\Resources\AppYuangonghuamingce\YuangongCollection;
use App\Http\Resources\Yzj\YzjCaiwuyuan\QuanxianCollection;
use App\Http\Resources\Yzj\YzjShenpiCollection;
use App\Lib\DemoInterface;
use App\Lib\DemoOne;
use App\Lib\DemoTwo;
use App\Lib\Kdd\CalcSign;
use App\Lib\Kdd\CommonConst;
use App\Models\AppYuangonghuamingceYuangong;
use App\Models\Jdb\YzjCaiwuyuanShangchuanquanxian;
use App\Models\Jdb\YzjDept;
use App\Models\YzjShenpi;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Facades\DemoInterfaceFacade;

class IndexController extends BaseController
{

    public function index(Request $request)
    {
        $yzjUser = $request->session()->get('yzjUser');
        $quanxian = YzjCaiwuyuanShangchuanquanxian::getQuanxian($yzjUser['jobNo'], '员工花名册');

//        echo json_encode($quanxian['list']);

//        var_dump();


        return Inertia::render('AppYuangonghuamingce/Index', [
            'yzjUser' => $yzjUser,
            'quanxian' => $quanxian,
        ]);
    }

    // 列表
    public function list(Request $request)
    {
        if ($request->isMethod('get')) {
            $yzjUser = $request->session()->get('yzjUser');
            $quanxian = YzjCaiwuyuanShangchuanquanxian::getQuanxian($yzjUser['jobNo'], '员工花名册');
            $codeList = array_column($quanxian['list'], "code");

            
            $yuangongList = AppYuangonghuamingceYuangong::whereIn("yuansuobianhao", $codeList)->orderByDesc('id')->paginate(20);

            return new YuangongCollection($yuangongList);
        }

    }

    // 保存创建
    public function store(Request $request)
    {
        if ($request->isMethod('post')) {

            // {"openId":"60e26896e4b01596045c9c38","phone":"18616126092","name":"\u5434\u78ca","photoUrl":"https:\/\/static.yunzhijia.com\/space\/c\/photo\/load?id=62c6788eec9ecd0001d7e347","jobNo":"0110210366","xiaoqubianhaoList":["01307","01412"]}
            $yzjUser = $request->session()->get('yzjUser');

            $yuangong = $request->get("yuangong", []);

            foreach ($yuangong as $key=>$val) {

                $yuangongModel = AppYuangonghuamingceYuangong::where('yuefen', $val['yuefen'])
                    ->where('yuansuobianhao', $val['yuansuobianhao'])
                    ->where('yuangongbianhao', $val['yuangongbianhao'])
                    ->first();

                if (!$yuangongModel) {
                    $yuangongModel = new AppYuangonghuamingceYuangong();
                    $yuangongModel->chuangjianrenId = $yzjUser['jobNo'];
                    $yuangongModel->chuangjianren = $yzjUser['name'];
                }

                $yuangongModel->yuansuobianhao = $val['yuansuobianhao'];
                $yuangongModel->yuefen = $val['yuefen'];
                $yuangongModel->yuangongbianhao = $val['yuangongbianhao'];
                $yuangongModel->xingming = $val['xingming'];
                $yuangongModel->yuangongshuxing = $val['yuangongshuxing'];
                $yuangongModel->shebao = $val['shebao'];

                $yuangongModel->xiugairenId = $yzjUser['jobNo'];
                $yuangongModel->xiugairen = $yzjUser['name'];

                $yuangongModel->save();
            }

            return response()->json(
                [
                    'error' => 0,
                    'errorMsg'=>'',
                    'success' => true,
                    'data' => [],
                    'message' => 'ok',
                ]
            );
        }
    }

    public function update(Request $request)
    {
        if ($request->isMethod('put')) {
            // {"openId":"60e26896e4b01596045c9c38","phone":"18616126092","name":"\u5434\u78ca","photoUrl":"https:\/\/static.yunzhijia.com\/space\/c\/photo\/load?id=62c6788eec9ecd0001d7e347","jobNo":"0110210366","xiaoqubianhaoList":["01307","01412"]}
            $yzjUser = $request->session()->get('yzjUser');

            $yuangong = $request->get("yuangong", []);

            foreach ($yuangong as $key=>$val) {

                $yuangongModel = AppYuangonghuamingceYuangong::where('yuefen', $val['yuefen'])
                    ->where('yuansuobianhao', $val['yuansuobianhao'])
                    ->where('yuangongbianhao', $val['yuangongbianhao'])
                    ->first();

//                if (!$yuangongModel) {
//                    $yuangongModel = new AppYuangonghuamingceYuangong();
//                    $yuangongModel->chuangjianrenId = $yzjUser['jobNo'];
//                    $yuangongModel->chuangjianren = $yzjUser['name'];
//                }

                $yuangongModel->yuansuobianhao = $val['yuansuobianhao'];
                $yuangongModel->yuefen = $val['yuefen'];
                $yuangongModel->yuangongbianhao = $val['yuangongbianhao'];
                $yuangongModel->xingming = $val['xingming'];
                $yuangongModel->yuangongshuxing = $val['yuangongshuxing'];
                $yuangongModel->shebao = $val['shebao'];

                $yuangongModel->xiugairenId = $yzjUser['jobNo'];
                $yuangongModel->xiugairen = $yzjUser['name'];

                $yuangongModel->save();
            }

            return response()->json(
                [
                    'error' => 0,
                    'errorMsg'=>'',
                    'success' => true,
                    'data' => [],
                    'message' => 'ok',
                ]
            );
        }
    }

    public function destroy(Request $request)
    {
        if ($request->isMethod('delete')) {
            $id = $request->get("id");


            $deleted = AppYuangonghuamingceYuangong::where('id', $id)->delete();

            return response()->json(
                [
                    'error' => 0,
                    'errorMsg'=>'',
                    'success' => true,
                    'data' => [],
                    'message' => 'ok',
                ]
            );

        }
    }

    // 权限列表
    public function quanxianList(Request $request)
    {
        $yzjUser = $request->session()->get('yzjUser');
        // $yzjUser['jobNo'] = '3120110055';
        $quanxian = YzjCaiwuyuanShangchuanquanxian::getQuanxian($yzjUser['jobNo'], '员工花名册');
        $curPage = $request->input('curPage', 1);
        $pageSize = $request->input('pageSize', 20);
        $xiaoquList = $request->input('codeList', []);

        if (!isset($quanxian['list']) || empty($quanxian['list'])) {
            $quanxian['list'] = [];
        }
        $quanxianList = array_slice($quanxian['list'], ($curPage-1) * $pageSize, $pageSize);

        $list = [];
        foreach ($quanxianList as $key=>$val) {
            $list[] = [
                '员工编号' => $quanxian['jobNo'],
                '员工姓名' => $quanxian['name'],
                '园编码' => $val['code'],
                '园名称' => $val['title'],
            ];
        }

        $page = new \Illuminate\Pagination\LengthAwarePaginator($list, count($quanxian['list']), $pageSize, $curPage);

        return new QuanxianCollection($page);
    }
}
