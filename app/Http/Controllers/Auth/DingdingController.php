<?php

namespace App\Http\Controllers\Auth;


use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Inertia\Inertia;

class DingdingController extends BaseController
{
    public function index(Request $request)
    {
        $guard = $request->input('guard');
        $corpid = $request->input('corpid');
        $appid = $request->input('appid');
        $callbackUrl = $request->input('callbackUrl', '');

        return Inertia::render('Auth/Dingding/Index', ['callbackUrl' => $callbackUrl, 'guard'=>$guard, 'corpid' => $corpid, 'appid'=>$appid]);
    }
}
