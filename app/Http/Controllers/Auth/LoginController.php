<?php

namespace App\Http\Controllers\Auth;

use Illuminate\Routing\Controller as BaseController;
use Illuminate\Http\Request;
use Inertia\Inertia;

class LoginController extends BaseController
{
    public function index(Request $request)
    {
        // var_dump($request->all());
    }

    // auth.shipei 默认登录页面
    public function shipei(Request $request)
    {
        return Inertia::render('Auth/Login/Shipei', []);

        //return view("YzjItShebeiLingyong.index", []);
    }

}
