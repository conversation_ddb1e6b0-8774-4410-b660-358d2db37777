<?php

namespace App\Http\Controllers\Auth;

use Illuminate\Routing\Controller as BaseController;
use Illuminate\Http\Request;
use Inertia\Inertia;

class LoginWeixinDingdingController extends BaseController
{
    public function index(Request $request)
    {
        // var_dump($request->all());
    }

    // 钉钉免登录跳转页
    public function dingding(Request $request)
    {
        $guard = $request->input('guard');
        $corpid = $request->input('corpid');
        $appid = $request->input('appid');
        $callbackUrl = $request->input('callbackUrl', '');

        return Inertia::render('Auth/LoginWeixinDingding/Dingding', ['callbackUrl' => $callbackUrl, 'guard'=>$guard, 'corpid' => $corpid, 'appid'=>$appid]);
    }

    // 钉钉免登录跳转页dev
    public function dingdingdev(Request $request)
    {


        $guard = $request->input('guard');
        $corpid = $request->input('corpid');
        $appid = $request->input('appid');
        $callbackUrl = $request->input('callbackUrl', '');

        return Inertia::render('Auth/LoginWeixinDingding/DingdingDev', ['callbackUrl' => $callbackUrl, 'guard'=>$guard, 'corpid' => $corpid, 'appid'=>$appid]);
    }

    // 微信钉钉登录失败
    public function failedlogin(Request $request)
    {
        return Inertia::render('Auth/LoginWeixinDingding/Failedlogin', []);
    }

    public function failedshipei(Request $request)
    {
        $msg = $request->input('msg', "适配");
        return Inertia::render('Auth/LoginWeixinDingding/Failedshipei', ['msg'=>$msg]);

    }

}
