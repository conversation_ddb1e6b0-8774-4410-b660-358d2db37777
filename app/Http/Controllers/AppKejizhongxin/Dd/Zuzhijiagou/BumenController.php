<?php

namespace App\Http\Controllers\AppKejizhongxin\Dd\Zuzhijiagou;


use App\Http\Requests\AppKejizhongxin\Dd\Juese\JueseStoreRequest;
use App\Http\Resources\AppKejizhongxin\Dd\Juese\JueseCollection;
use App\Http\Resources\AppKejizhongxin\Dd\Zuzhijiagou\BumenCollection;
use App\Models\Dd\DdJuese;
use App\Models\DdBumen;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Inertia\Inertia;
use App\Facades\DemoInterfaceFacade;

class BumenController extends BaseController
{

    // 列表
    public function list(Request $request)
    {
        $search = $request->input('search', []);

        $paginate = DdBumen::where("shanchu","<>", "1");
        if (isset($search['mingcheng']) && !empty($search['mingcheng'])) {
            $paginate = $paginate->where("changmingcheng", "LIKE", "%". $search['mingcheng'] ."%");
        }
        $paginate = $paginate->paginate(20);
        return new BumenCollection($paginate);
    }

    // 树
    public function tree(Request $request)
    {
        $bumensModel = DdBumen::where("shanchu", "<>", "1")->get();
        $bumenTree = DdBumen::generateTree($bumensModel->toArray(), '1', 'children', ['dept_id' => 'value', 'name'=>'label','parent_id'=>'parent_id'], 'dept_id', 'parent_id');

        return response()->json([
            'success' => true,
            'error' => '',
            'errorCode' => 0,
            'data' => $bumenTree
        ]);
    }

    // lookup
    public function lookup(Request $request)
    {
        $ids = $request->input('ids', []);
        if (empty($ids)) $ids = [];
        $models = DdBumen::whereIn('dept_id', $ids)->get();

        return response()->json([
            'success' => true,
            'error' => '',
            'errorCode' => 0,
            'data' => $models
        ]);

//        return new BumenCollection($models);
    }



}
