<?php

namespace App\Http\Controllers\AppKejizhongxin\Dd\Zuzhijiagou;


use App\Http\Requests\AppKejizhongxin\Dd\Juese\JueseStoreRequest;
use App\Http\Resources\AppKejizhongxin\Dd\Juese\JueseCollection;
use App\Http\Resources\AppKejizhongxin\Dd\Zuzhijiagou\YuangongCollection;
use App\Models\Dd\DdJuese;
use App\Models\DdYuangong;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Inertia\Inertia;
use App\Facades\DemoInterfaceFacade;

class ZhiwuController extends BaseController
{


    // 列表
    public function list(Request $request)
    {
        $list = DdYuangong::select("title")->where("title","<>", "")->distinct()->orderBy("title")->get()->pluck("title");

        return response()->json(
            [
                'error' => 0,
                'errorMsg'=>'',
                'success' => true,
                'data' => $list,
                'message' => "",
            ]
        );
    }
}
