<?php

namespace App\Http\Controllers\AppKejizhongxin\Dd\Zuzhijiagou;


use App\Http\Requests\AppKejizhongxin\Dd\Juese\JueseStoreRequest;
use App\Http\Resources\AppKejizhongxin\Dd\Juese\JueseCollection;
use App\Http\Resources\AppKejizhongxin\Dd\Zuzhijiagou\YuangongCollection;
use App\Models\Dd\DdJuese;
use App\Models\DdYuangong;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Inertia\Inertia;
use App\Facades\DemoInterfaceFacade;

class YuangongController extends BaseController
{

// 列表
    public function list(Request $request)
    {
        $search = $request->input('search', []);

        $paginate = DdYuangong::where("shanchu","<>", "1");

        if (isset($search['mingcheng']) && !empty($search['mingcheng'])) {
            $paginate = $paginate->where("name", "LIKE", "%". $search['mingcheng'] ."%");
        }

        $paginate = $paginate->paginate(20);

        return new YuangongCollection($paginate);
    }

    // lookup
    public function lookup(Request $request)
    {
        $ids = $request->input('ids', []);
        if (empty($ids)) $ids = [];

        $models = DdYuangong::whereIn('userid', $ids)->get();

        return response()->json([
            'success' => true,
            'error' => '',
            'errorCode' => 0,
            'data' => $models
        ]);

//        return new YuangongCollection($models);
    }
}
