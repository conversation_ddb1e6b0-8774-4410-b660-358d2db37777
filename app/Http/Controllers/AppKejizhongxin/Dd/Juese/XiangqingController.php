<?php

namespace App\Http\Controllers\AppKejizhongxin\Dd\Juese;

use App\Exports\DdJueseMingdanExport;
use App\Http\Requests\AppKejizhongxin\Dd\Juese\JueseUpdateRequest;
use App\Http\Resources\AppKejizhongxin\Dd\Juese\JueseRenyuanCollection;
use App\Http\Resources\AppKejizhongxin\Dd\Juese\JueseXiangqingResource;
use App\Http\Resources\AppKejizhongxin\Dd\Juese\JueseYuangongCollection;
use App\Imports\DdJueseMingdanImport;
use App\Models\Dd\DdJuese;
use App\Models\Dd\DdJueseGuize;
use App\Models\Dd\DdJueserenyuan;
use App\Models\DdYuangong;
use App\Services\Dd\DdApiService;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use App\Facades\DemoInterfaceFacade;
use Maatwebsite\Excel\Facades\Excel;

class XiangqingController extends BaseController
{
    // 名单页面
    public function mingdan(Request $request, $id)
    {
        $ddJueseModel = DdJuese::find($id);

        return Inertia::render('AppKejizhongxin/Dd/Juese/Mingdan', ['juese' => $ddJueseModel]);
    }

    // 名单列表
    public function mingdanlist(Request $request, $id)
    {

        $name = $request->input('name', "");


        $ddJueseModel = DdJuese::find($id);
        $ddJueseYuangongModel = $ddJueseModel->yuangong();
        $ddJueseYuangongModel = $ddJueseYuangongModel->get();
        $ddYuangongListModel = DdYuangong::whereIn('userid', $ddJueseYuangongModel->pluck("ddUserId"));



        $paginate = $ddYuangongListModel->paginate(20);
        return new JueseYuangongCollection($paginate);
    }

    // 编辑页面
    public function edit(Request $request, $id)
    {
        // $juese = DdJuese::find($id);

        $dbJueseModel = DdJuese::with('guize')->where('id', $id)->first();
        $ddJueseResource = new JueseXiangqingResource($dbJueseModel);

        return Inertia::render('AppKejizhongxin/Dd/Juese/Xiangqing', ['juese'=>$ddJueseResource]);
    }

    // 更新
    public function update(JueseUpdateRequest $request, $id)
    {
        if ($request->isMethod('put')) {

            $data = $request->validated();

            $dbJueseModel = DdJuese::where("id", $id)->first();
            $dbJueseModel->ddUserIds = $data['yuangong']['value'] ?? [];
            if ($dbJueseModel->mingcheng != $data['mingcheng']) {
                $dbJueseModel->mingcheng = $data['mingcheng'];

                if (!empty($dbJueseModel->roleid)) {
                    $ddApiService = new DdApiService();
                    $ddApiService->updateJuese($data['mingcheng'],$dbJueseModel->roleid);
                }
            }


            $dbJueseModel->save();



            if (isset($data['tiaojianList'])) {
                foreach ($data['tiaojianList'] as $tiaojian) {

                    $row = [];
                    $row['ddBumenIds'] = ($tiaojian['bumen']['value']);
                    $row['bumenfuzeren'] = $tiaojian['bumenfuzeren'];
                    $row['baohanxiajibumen'] = $tiaojian['xiajibumen'];
                    $row['ddZhiwuNames'] = ($tiaojian['zhiwei']);
                    $row['beisenZuzhileixingIds'] = ($tiaojian['zuzhileixing']);

                    if (empty($tiaojian['id'])) {
                        // 创建
                        $row['jueseid'] = $id;
                        $ddJueseGuizeModel = new DdJueseGuize($row);
                        $ddJueseGuizeModel->save();
                    } else {
                        // 更新
                        DdJueseGuize::where('id', $tiaojian['id'])->update($row);
                    }
                }
            }

            DdJueseGuize::where('jueseid', $id)->whereNotIn('id', array_column($data['tiaojianList'], 'id'))->delete();


            $dbJueseModel = DdJuese::with('guize')->where('id', $id)->first();


            return response()->json(
                [
                    'error' => 0,
                    'errorMsg'=>'',
                    'success' => true,
                    'data' => new JueseXiangqingResource($dbJueseModel),
                    'message' => 'ok',
                ]
            );
        }
    }


    public function mingdanStore(Request $request, $id)
    {
        if ($request->isMethod('post')) {

            $ddUserIds = $request->input('ddUserIds', []);

            $ddYuangongListModel = DdYuangong::whereIn('userid', $ddUserIds)->get();

            $ddYuangongListModel->each(function ($item) use ($id) {
               $row = $item->toArray();

               $newRow = [
                   'jueseid' => $id,
                   'ddUserId' => $row['userid'],
                   'jobNumber' => $row['job_number'],
                   'mingcheng' => $row['name'],
               ];

                DdJueserenyuan::upsert(
                    [$newRow],
                    ['jueseid', 'ddUserId'],
                    ['jueseid', 'ddUserId', 'jobNumber', 'mingcheng']
                );
            });

            return response()->json(
                [
                    'error' => 0,
                    'errorMsg'=>'',
                    'success' => true,
                    'data' => [],
                    'message' => 'ok',
                ]
            );

        }
    }

    public function mingdanDestroy(Request $request, $id)
    {
        if ($request->isMethod('delete')) {
            $ids = $request->input('ids', []);

            DdJueserenyuan::where('jueseid', $id)->whereIn('ddUserId', $ids)->delete();

            return response()->json(
                [
                    'error' => 0,
                    'errorMsg'=>'',
                    'success' => true,
                    'data' => [],
                    'message' => 'ok',
                ]
            );
        }
    }

    public function renyuanList(Request $request, $id)
    {
        $name = $request->input('name', "");

        $ddJueseModel = DdJuese::find($id);
        $ddJueseYuangongModel = $ddJueseModel->renyuan();
        $ddJueseYuangongModel = $ddJueseYuangongModel->get();
        $ddYuangongListModel = DdYuangong::whereIn('userid', $ddJueseYuangongModel->pluck("ddUserId"));


        if (!empty($name)) {

            $ddYuangongListModel = $ddYuangongListModel->where("name", "like", "%" . $name . "%");
        }
        $paginate = $ddYuangongListModel->paginate(20);
        return new JueseRenyuanCollection($paginate);
    }

    public function renyuantpldown(Request $request, $id)
    {
        if ($request->isMethod('get')) {
            return Excel::download(new DdJueseMingdanExport, "tpl_员工导入模板.xlsx");
        }
    }

    public function renyuanupload(Request $request, $id)
    {
        $file = $request->file('file')->store('temp');
        $path = storage_path('app').'/'.$file;

        \Maatwebsite\Excel\Imports\HeadingRowFormatter::default('none');
        $headings = (new \Maatwebsite\Excel\HeadingRowImport)->toArray(($path));

        $collection = (new DdJueseMingdanImport)->toCollection($path);

        $jobNumberList = [];
        try {
            foreach ($collection[0] as $row) {

                $row['员工编号'] = trim($row['员工编号']);
                if (empty($row['员工编号'])) continue;

                $jobNumberList[] = $row['员工编号'];
            }

        } catch (\Exception $e) {
            return response(json_encode(['error'=>$e->getCode(), 'errorMsg'=>$e->getMessage()]), 403);
        }


        $ddYuangongUserIdList = DdYuangong::select(["userid", "job_number", "name"])->whereIn('job_number', $jobNumberList)->get();
        $ddYuangongUserIdList = $ddYuangongUserIdList->toArray();
        $ddYuangongUserArr = [];
        foreach ($ddYuangongUserIdList as $row) {
            $ddYuangongUserArr[] = [
                'jueseid' => $id,
                'ddUserId' => $row['userid'],
                'jobNumber' => $row['job_number'],
                'mingcheng' => $row['name'],
            ];
        }

        $ddYuangongUserCollect = collect($ddYuangongUserArr);

        try {
            DB::beginTransaction();

            // 锁
            DdJuese::where('id', $id)->lockForUpdate()->get();

            $ddYuangongUserCollect->chunk(20)->each(function ($items) use ($ddYuangongUserIdList) {
                 $rows = $items->toArray();

                DdJueserenyuan::upsert(
                    $rows,
                    ['jueseid', 'ddUserId'],
                    ['jueseid', 'ddUserId', 'jobNumber', 'mingcheng']
                );
            });


            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();

            return response(json_encode(['error'=>$e->getCode(), 'errorMsg'=>$e->getMessage()]), 403);
        }

        return response()->json(
            [
                'error' => 0,
                'errorMsg'=>'',
                'success' => true,
                'data' => [

                ],
                'message' => '',
            ]);
    }

}
