<?php

namespace App\Http\Controllers\AppKejizhongxin\Dd\Juese;


use App\Http\Requests\AppKejizhongxin\Dd\Juese\JueseStoreRequest;
use App\Http\Resources\AppKejizhongxin\Dd\Juese\JueseCollection;
use App\Models\Dd\DdJuese;
use App\Services\Dd\DdApiService;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Inertia\Inertia;
use App\Facades\DemoInterfaceFacade;

class IndexController extends BaseController
{

    public function index(Request $request)
    {
        return Inertia::render('AppKejizhongxin/Dd/Juese/Index', []);
    }

    // 列表
    public function list(Request $request)
    {
        $paginate = DdJuese::orderByDesc('id')->paginate(20);
        return new JueseCollection($paginate);
    }

    // 保存
    public function store(JueseStoreRequest $request)
    {
        if ($request->isMethod('post')) {

            $data = $request->validated();

            $dbJuese = DdJuese::where("mingcheng", $data['mingcheng'])->first();

            if (!empty($dbJuese)) {
                return response()->json(
                    [
                        'error' => 100,
                        'errorMsg'=>'',
                        'success' => false,
                        'data' => [],
                        'message' => "名称：{$data['mingcheng']} 不能重复",
                    ]
                );
            }

            $dbJueseModel = DdJuese::create($data);

            $ddApiService = new DdApiService();

            // {"errcode":0,"errmsg":"ok","roleId":4407788676}
            $jueseRs = $ddApiService->createJuese($data['mingcheng'],"4399677341");
            if (isset($jueseRs['roleId']) && !empty($jueseRs['roleId'])) {
                $dbJueseModel->roleid = $jueseRs['roleId'];
            }
            $dbJueseModel->save();

            return response()->json(
                [
                    'error' => 0,
                    'errorMsg'=>'',
                    'success' => true,
                    'data' => [],
                    'message' => 'ok',
                ]
            );
        }
    }

    // 更新
    public function update(JueseStoreRequest $request, $id)
    {
        if ($request->isMethod('put')) {

            $data = $request->validated();

            $dbJuese = DdJuese::where("mingcheng", $data['mingcheng'])->first();

            if (!empty($dbJuese)) {
                return response()->json(
                    [
                        'error' => 100,
                        'errorMsg'=>'',
                        'success' => false,
                        'data' => [],
                        'message' => "名称：{$data['mingcheng']} 不能重复",
                    ]
                );
            }

            $dbJuese = DdJuese::find($id);

            if ($dbJuese) {
                $dbJuese->mingcheng = $data['mingcheng'];
                $dbJuese->save();

                if (!empty($dbJuese->roleid)) {
                    $ddApiService = new DdApiService();
                    $ddApiService->updateJuese($data['mingcheng'],$dbJuese->roleid);
                }
            }

            return response()->json(
                [
                    'error' => 0,
                    'errorMsg'=>'',
                    'success' => true,
                    'data' => [],
                    'message' => 'ok',
                ]
            );
        }
    }

    // 删除
    public function destroy(Request $request, $id)
    {
        if ($request->isMethod('delete')) {

            DdJuese::where('id', $id)->delete();

            return response()->json(
                [
                    'error' => 0,
                    'errorMsg'=>'',
                    'success' => true,
                    'data' => [],
                    'message' => 'ok',
                ]
            );
        }
    }
}
