<?php

namespace App\Http\Controllers\AppKejizhongxin\Dd\Juese;


use App\Http\Requests\AppKejizhongxin\Dd\Juese\JueseStoreRequest;
use App\Http\Resources\AppKejizhongxin\Dd\Juese\JueseCollection;
use App\Models\Dd\DdJuese;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Inertia\Inertia;
use App\Facades\DemoInterfaceFacade;

class MingdanController extends BaseController
{

    public function index(Request $request)
    {
        return Inertia::render('AppKejizhongxin/Dd/Juese/Index', []);
    }

    // 列表
    public function list(Request $request)
    {
        $paginate = DdJuese::orderByDesc('id')->paginate(20);
        return new JueseCollection($paginate);
    }

    // 保存
    public function store(JueseStoreRequest $request)
    {
        if ($request->isMethod('post')) {

            $data = $request->validated();

            $dbJuese = DdJuese::where("mingcheng", $data['mingcheng'])->first();

            if (!empty($dbJuese)) {
                return response()->json(
                    [
                        'error' => 100,
                        'errorMsg'=>'',
                        'success' => false,
                        'data' => [],
                        'message' => "名称：{$data['mingcheng']} 不能重复",
                    ]
                );
            }

            DdJuese::create($data);

            return response()->json(
                [
                    'error' => 0,
                    'errorMsg'=>'',
                    'success' => true,
                    'data' => [],
                    'message' => 'ok',
                ]
            );
        }
    }

    // 更新
    public function update(JueseStoreRequest $request, $id)
    {
        if ($request->isMethod('put')) {

            $data = $request->validated();

            $dbJuese = DdJuese::where("mingcheng", $data['mingcheng'])->first();

            if (!empty($dbJuese)) {
                return response()->json(
                    [
                        'error' => 100,
                        'errorMsg'=>'',
                        'success' => false,
                        'data' => [],
                        'message' => "名称：{$data['mingcheng']} 不能重复",
                    ]
                );
            }

            $dbJuese = DdJuese::find($id);

            if ($dbJuese) {
                $dbJuese->mingcheng = $data['mingcheng'];
                $dbJuese->save();
            }

            return response()->json(
                [
                    'error' => 0,
                    'errorMsg'=>'',
                    'success' => true,
                    'data' => [],
                    'message' => 'ok',
                ]
            );
        }
    }

    // 删除
    public function destroy(Request $request)
    {

    }

}
