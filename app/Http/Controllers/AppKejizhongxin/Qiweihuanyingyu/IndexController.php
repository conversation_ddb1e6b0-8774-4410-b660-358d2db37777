<?php
namespace App\Http\Controllers\AppKejizhongxin\Qiweihuanyingyu;


use App\Http\Requests\AppKejizhongxin\Dd\Juese\JueseStoreRequest;
use App\Http\Requests\AppKejizhongxin\Qiweihuanyingyu\QiweihuanyingyuStoreRequest;
use App\Http\Requests\AppKejizhongxin\Qiweihuanyingyu\QiweihuanyingyuUpdateRequest;
use App\Http\Resources\AppKejizhongxin\Dd\Juese\JueseCollection;
use App\Http\Resources\AppKejizhongxin\Dd\Zuzhijiagou\BumenCollection;
use App\Http\Resources\AppKejizhongxin\Qiweihuanyingyu\HuanyingyuCollection;
use App\Http\Resources\AppKejizhongxin\Qiweihuanyingyu\HuanyingyuguizeCollection;
use App\Models\Dd\DdJuese;
use App\Models\DdBumen;
use App\Models\Qiwei\QiweiEventhuanyingyu;
use App\Models\Qiwei\QiweiEventhuanyingyuguize;
use App\Services\Dd\DdApiService;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Inertia\Inertia;
use App\Facades\DemoInterfaceFacade;


class IndexController extends BaseController
{

    public function index(Request $request)
    {
        return Inertia::render('AppKejizhongxin/Qiweihuanyingyu/Index', []);
    }



    // 列表
    public function list(Request $request)
    {
        $paginate = QiweiEventhuanyingyuguize::orderByDesc("id")->paginate(20);
        return new HuanyingyuguizeCollection($paginate);
    }

    // 推送记录列表
    public function huanyingyutuisonglist(Request $request)
    {
        $paginate = QiweiEventhuanyingyu::orderByDesc("id")->paginate(20);


        return new HuanyingyuCollection($paginate);
    }

    // 保存
    public function store(QiweihuanyingyuStoreRequest $request)
    {

        if ($request->isMethod('post')) {

            $data = $request->validated();

            $data['state'] = $data['state'] ?? '';

            $dbJueseModel = QiweiEventhuanyingyuguize::create($data);

            return response()->json(
                [
                    'error' => 0,
                    'errorMsg'=>'',
                    'success' => true,
                    'data' => [],
                    'message' => 'ok',
                ]
            );
        }
    }

    // 更新
    public function update(QiweihuanyingyuUpdateRequest $request, $id)
    {
        if ($request->isMethod('put')) {

            $data = $request->validated();


            $guizeModel = QiweiEventhuanyingyuguize::findOrFail($id);
            $data['state'] = $data['state'] ?? '';

            $guizeModel->fill($data);
            $guizeModel->save();


            return response()->json(
                [
                    'error' => 0,
                    'errorMsg'=>'',
                    'success' => true,
                    'data' => [],
                    'message' => 'ok',
                ]
            );
        }
    }

    // 删除
    public function destroy(Request $request, $id)
    {
        if ($request->isMethod('delete')) {

            QiweiEventhuanyingyuguize::where('id', $id)->delete();

            return response()->json(
                [
                    'error' => 0,
                    'errorMsg'=>'',
                    'success' => true,
                    'data' => [],
                    'message' => 'ok',
                ]
            );
        }
    }
}
