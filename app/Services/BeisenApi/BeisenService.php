<?php

namespace App\Services\BeisenApi;

use App\Models\BeisenBumen;
use App\Models\BeisenYuangongRenzhi;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class BeisenService
{

    // 获取北森组织架构
    public function getBeisenBumenModel($now = null)
    {
        if (empty($now)) $now = date('Y-m-d H:i:s');

        $beisenBumensModel = BeisenBumen::select(['changmingcheng', 'oId', 'id', 'pOIdOrgAdmin', 'name',
            'personInCharge', 'extzuzhileixing', 'extyewuxitongbianma', 'code', 'pOidOrgAdmin_TreePath'])
            ->where('status', 1)
            ->where('shanchu', 0)
            ->where('stdIsDeleted', 0)
            ->where("startDate" , "<=", $now)
            ->where("stopDate", ">=", $now)
//            ->whereNotIn("code", ['RootOrg', 'DefaultDept'])
            ->orderBy("pOidOrgAdmin_TreePath");
//            ->get();
        return $beisenBumensModel;
    }

    // 在职+离职员工数据,根据lastWorkDate筛选
    public function getBeisenYuangongModelWithLizhi()
    {
        if (empty($now)) $now = date('Y-m-d H:i:s');

        $beisenYuangongsModel = DB::table('t_beisen_yuangong')
            ->leftJoin('t_beisen_yuangong_renzhi', 't_beisen_yuangong.userId', '=', 't_beisen_yuangong_renzhi.userId')
            ->leftJoin('t_beisen_bumen', 't_beisen_yuangong_renzhi.oIdDepartment', '=', 't_beisen_bumen.oId')
            ->leftJoin('t_beisen_zhiwu', 't_beisen_yuangong_renzhi.oIdJobPost', '=', 't_beisen_zhiwu.oId')
            ->select([
                't_beisen_yuangong.name','t_beisen_yuangong.gender','t_beisen_yuangong.mobilePhone','t_beisen_yuangong.gender','t_beisen_yuangong.latestEntryDate', 't_beisen_yuangong.birthday',
                't_beisen_yuangong_renzhi.userId', 't_beisen_yuangong_renzhi.jobNumber', 't_beisen_yuangong_renzhi.oIdDepartment','t_beisen_yuangong_renzhi.serviceType', 't_beisen_yuangong_renzhi.lastWorkDate', 't_beisen_yuangong_renzhi.employeeStatus',
                't_beisen_bumen.personInCharge','t_beisen_bumen.changmingcheng',
                't_beisen_zhiwu.name as zhiwuname'
            ])
            ->whereIn("t_beisen_yuangong_renzhi.employType", [0, 2])
            ->whereIn("t_beisen_yuangong_renzhi.employeeStatus", [2, 3, 4, 5,     6,8,12])
            ->where(function(Builder $query) {
                $query->where(function(Builder $query2) {
                    $query2->where('t_beisen_yuangong_renzhi.isCurrentRecord', 1)
                        ->where('t_beisen_yuangong_renzhi.serviceType', 0);
                })->orWhere('t_beisen_yuangong_renzhi.serviceType', 1);
            })
//            ->where("t_beisen_yuangong_renzhi.startDate", "<=", $now)
//            ->where('t_beisen_yuangong_renzhi.stopDate', ">=", $now)
            ->where('t_beisen_yuangong_renzhi.stdIsDeleted', 0)
            ->where('t_beisen_yuangong.stdIsDeleted', 0)
            ->where('t_beisen_bumen.status', 1)
            ->where('t_beisen_bumen.shanchu', 0)
            ->where('t_beisen_bumen.stdIsDeleted', 0)
            ->where('t_beisen_bumen.startDate', '<=', $now)
            ->where('t_beisen_bumen.stopDate', '>=', $now);
//            ->whereNotIn('t_beisen_bumen.code', ['RootOrg', 'DefaultDept'])
//            ->where('t_beisen_yuangong_renzhi.jobNumber', '0110210366')
//            ->get();

        return $beisenYuangongsModel;
    }

    // 获取北森在职主职+兼职
    public function getBeisenYuangongModel($now = null)
    {
        if (empty($now)) $now = date('Y-m-d H:i:s');

        $beisenYuangongsModel = DB::table('t_beisen_yuangong')
            ->leftJoin('t_beisen_yuangong_renzhi', 't_beisen_yuangong.userId', '=', 't_beisen_yuangong_renzhi.userId')
            ->leftJoin('t_beisen_bumen', 't_beisen_yuangong_renzhi.oIdDepartment', '=', 't_beisen_bumen.oId')
            ->leftJoin('t_beisen_zhiwu', 't_beisen_yuangong_renzhi.oIdJobPost', '=', 't_beisen_zhiwu.oId')
            ->select([
                't_beisen_yuangong.name','t_beisen_yuangong.gender','t_beisen_yuangong.mobilePhone','t_beisen_yuangong.gender','t_beisen_yuangong.latestEntryDate', 't_beisen_yuangong.birthday',
                't_beisen_yuangong_renzhi.userId', 't_beisen_yuangong_renzhi.jobNumber', 't_beisen_yuangong_renzhi.oIdDepartment','t_beisen_yuangong_renzhi.serviceType', 't_beisen_yuangong_renzhi.lastWorkDate','t_beisen_yuangong_renzhi.userID',
                't_beisen_bumen.personInCharge','t_beisen_bumen.changmingcheng',
                't_beisen_zhiwu.name as zhiwuname'
            ])
            ->whereIn("t_beisen_yuangong_renzhi.employType", [0, 2])
            ->whereIn("t_beisen_yuangong_renzhi.employeeStatus", [2, 3, 4, 5])
            ->where(function(Builder $query) {
                $query->where(function(Builder $query2) {
                    $query2->where('t_beisen_yuangong_renzhi.isCurrentRecord', 1)
                        ->where('t_beisen_yuangong_renzhi.serviceType', 0);
                })->orWhere('t_beisen_yuangong_renzhi.serviceType', 1);
            })
            ->where("t_beisen_yuangong_renzhi.startDate", "<=", $now)
            ->where('t_beisen_yuangong_renzhi.stopDate', ">=", $now)
            ->where('t_beisen_yuangong_renzhi.stdIsDeleted', 0)
            ->where('t_beisen_yuangong.stdIsDeleted', 0)
            ->where('t_beisen_bumen.status', 1)
            ->where('t_beisen_bumen.shanchu', 0)
            ->where('t_beisen_bumen.stdIsDeleted', 0)
            ->where('t_beisen_bumen.startDate', '<=', $now)
            ->where('t_beisen_bumen.stopDate', '>=', $now);
//            ->whereNotIn('t_beisen_bumen.code', ['RootOrg', 'DefaultDept'])
//            ->where('t_beisen_yuangong_renzhi.jobNumber', '0110210366')
//            ->get();

        return $beisenYuangongsModel;
    }

    // 获得离职人员信息
    public function getBeisenLizhiYuangongModel($now = null)
    {
        if (empty($now)) $now = date('Y-m-d H:i:s');

        $zaizhiJobNumberList = BeisenYuangongRenzhi::select('jobNumber')
            ->whereIn('employType', [0,2])
            ->whereIn('employeeStatus', [2,3,4,5])
            ->where('isCurrentRecord', 1)
            ->where('serviceType', 0)
            ->where('startDate', '<=', $now)
            ->where('stopDate', '>=', $now)
            ->where('stdIsDeleted', 0)
            ->get();

        $beisenLizhiYuangongsModel = BeisenYuangongRenzhi::select('jobNumber','userID', 'oIdDepartment', 'lastWorkDate', )
            ->where('isCurrentRecord', 1)
            ->where('serviceType', 0)
            ->where('stdIsDeleted', 0)
            ->whereNotIn('jobNumber', $zaizhiJobNumberList->pluck('jobNumber')->toArray());

        return $beisenLizhiYuangongsModel;
    }



}
