<?php
namespace App\Console\Commands;

use App\Models\AppDuanxinfasongjihua;
use App\Models\AppDuanxinfasongjihuaFenlu;
use App\Services\CmdService;
use App\Services\Duanxinfasongjihua\DuanxinService;
use Illuminate\Console\Command;
use Library\EduConst;
use \App\Services\Fanwei\FanweiApiService;
use \App\Services\Fanwei\FanweiService;

// 短信发送计划
class Duanxinfasongjihua extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:duanxinfasongjihua';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $microtime = microtime(true);

        // 发送计划0默认不操作，1开始发送，2发送完毕
        // 短信分录2发送完毕

//        $cmdService = new CmdService();
//        $cmd = "duanxinfasongjihua";
//        $psCnt = $cmdService->psAux($cmd);
//        echo "psCnd:{$psCnt}\n";
//        if ($psCnt > 1) {
//            echo "进程已存在不能重复执行：" . $cmd . "\n";
//            return;
//        }
//
//
//        echo __DIR__;
//        echo "\n";

        // /data/www/hw_jdb_eduappV2/eduappV2/app/Console/Commands
        $lockFile = __DIR__ . "/command_duanxinfasongjihua.lock";
        $fp = fopen($lockFile, 'w');
        if (flock($fp, LOCK_EX | LOCK_NB)) {
            // 执行脚本的代码


            echo time() . "开始执行\n";



            $duanxinsModel = AppDuanxinfasongjihua::where("zhuangtai", 1)->lockForUpdate()->get();

//            // 测试
//            $duanxinsModel = AppDuanxinfasongjihua::where("id", 12)->lockForUpdate()->get();

            if (empty($duanxinsModel || $duanxinsModel->count() <= 0)) {
                echo "暂无发送计划\n";
            }

            echo "开始发送 - " . time() . "\n";



            $duanxinFenlusModel = AppDuanxinfasongjihuaFenlu::whereIn("duanxinfasongjihuaId", $duanxinsModel->pluck("id"))->where("zhuangtai", "<>", "2")->lockForUpdate()->get();

            // 测试
            // $duanxinFenlusModel = AppDuanxinfasongjihuaFenlu::whereIn("duanxinfasongjihuaId", $duanxinsModel->pluck("id"))->where("resultZhuangtai", "=", "1")->lockForUpdate()->get();


            $duanxinFenlusModelByGroup = $duanxinFenlusModel->groupBy("duanxinfasongjihuaId");

            $duanxinService = new DuanxinService();


            foreach ($duanxinsModel as $duanxinModel) {
                if ( isset($duanxinFenlusModelByGroup[$duanxinModel['id']])) {
                    foreach ($duanxinFenlusModelByGroup[$duanxinModel['id']] as $row) {

                        // if ($row->shouji != '13472536045') continue;

                        echo $row->shouji . " - " . $duanxinModel->neirong . "\n";

                        $result = $duanxinService->fasongduanxin($row->shouji, $duanxinModel->neirong . " 拒收请回复R");
                        echo json_encode($result);
                        echo "\n";
                        $row->zhuangtai = 2;
                        $row->result = $result;
                        $row->resultZhuangtai = intval($result['error']);
                        $row->save();


                        sleep(1);

                    }
                }

                $duanxinModel->zhuangtai = 2;
                $duanxinModel->save();
            }




//            sleep(100);



            flock($fp, LOCK_UN); // 释放锁


        } else {
            echo time() . "进程已存在不能重复执行：\n";
        }



        fclose($fp); // 关闭文件

//        return Command::SUCCESS;

        echo "ok-" . time() . "\n";

        return Command::SUCCESS;
    }
}
