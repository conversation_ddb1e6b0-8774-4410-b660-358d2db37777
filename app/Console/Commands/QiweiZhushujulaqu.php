<?php

namespace App\Console\Commands;

use App\Models\BeisenBumen;
use App\Models\BeisenYuangongRenzhi;
use App\Models\Qiwei\QiweiYuangong;
use App\Models\Qiwei\QiweiBumen;
use Illuminate\Console\Command;
use Library\EduConst;


// 测试矩阵同步
class QiweiZhushujulaqu extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:qiweizhushujulaqu';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $microtime = microtime(true);

        echo "begin-" . time() . "\n";

        $qiweiApiService = new \App\Services\Qiwei\QiweiApiService();

        // 标签拉取
        echo "获取标签信息\n";

        $biaoqianRep = $qiweiApiService->getTag();

        if ($biaoqianRep['errcode'] == 0) {
            $biaoqianRows = [];
            foreach ($biaoqianRep['tag_group'] as $key=>$val) {

                foreach ($val['tag'] as $tagKey=>$tagVal) {
                    $biaoqianRow = [];
                    $biaoqianRow['group_id'] = $val['group_id'];
                    $biaoqianRow['group_name'] = $val['group_name'];
                    $biaoqianRow['group_create_time'] = $val['create_time'];
                    $biaoqianRow['tag_id'] = $tagVal['id'];
                    $biaoqianRow['tag_name']= $tagVal['name'];
                    $biaoqianRow['tag_create_time']= $tagVal['create_time'];
                    $biaoqianRow['tag_order']= $tagVal['order'];

                    $biaoqianRow['tag_shanchu'] = 0;
                    $biaoqianRow['group_shanchu'] = 0;

                    $biaoqianRows[] = $biaoqianRow;
                }
            }


            if (!empty($biaoqianRows)) {

                $rowsCollect = collect($biaoqianRows);


                $qiweiBiaoqianModel = new \App\Models\Qiwei\QiweiBiaoqian();

                $rowsCollect->chunk(100)->each(function ($items) use($qiweiBiaoqianModel) {
                    $rows = $items->toArray();

                    \App\Models\Qiwei\QiweiBiaoqian::upsert(
                        $rows,
                        ['group_id', 'tag_id'],
                        $qiweiBiaoqianModel->fillable
                    );

                });
            }

        }



        echo "获取部门信息\n";
        // 获取企微部门信息
        $bumenList = $qiweiApiService->getBumenAll();
        $bumenCollect = collect($bumenList);
        $qiweiBumenModel = new QiweiBumen();
        $qiweiBumenFillable = $qiweiBumenModel->fillable;

        echo "更新企微部门信息\n";
        $bumenCollect->chunk(1)->each(function($items, $key) use ($qiweiBumenFillable) {

            $rows = $items->toArray();

            foreach ($rows as $key=>$val) {
                $rows[$key]['department_leader'] = isset($val['department_leader']) ? json_encode($val['department_leader']) : "[]";
            }

            QiweiBumen::upsert(
                $rows,
                ['id'],
                $qiweiBumenFillable
            );
        });

        echo "删除企微中部门\n";
        // 标记企微中部门删除
        QiweiBumen::whereNotIn("id", $bumenCollect->pluck("id"))->update(['shanchu'=>1]);


        $bumenListModel = QiweiBumen::where('shanchu', 0)->get();
        $bumenListModelById = $bumenListModel->keyBy('id');


        // select * from t_beisen_yuangong_renzhi where stopDate>now() and isCurrentRecord=1 and stdIsDeleted=0  and serviceType=0 and employeeStatus in (2,3)

        echo "获取企微中员工\n";
        $beisenRenzhisModel = BeisenYuangongRenzhi::select(["jobNumber"])
//            ->where("stopDate", ">", date("Y-m-d H:i:s"))
//            ->where("isCurrentRecord", "1")
//            ->where("stdIsDeleted","0")
//            ->whereIn("employeeStatus", [2, 3])
            ->get();
        $beisenRenzhisModelByJobNumber = $beisenRenzhisModel->keyBy("jobNumber");


        // 获取企微员工信息
        $yuangongList = $qiweiApiService->getYuangongAll();
        $yuangongCollect = collect($yuangongList);



        $qiweiYuangongModel = new QiweiYuangong();
        $qiweiYuangongFillable = $qiweiYuangongModel->fillable;

        echo "更新企微中员工\n";
        $yuangongCollect->chunk(100)->each(function ($items, $key) use($qiweiYuangongFillable, $beisenRenzhisModelByJobNumber, $bumenListModelById) {
            $rows = $items->toArray();

            foreach ($rows as $key=>$val) {

                #97todo 修改吴磊 职工编号   吴磊：0110210366  97：0111810002
                if ($val['userid'] == '0110210366' || $val['userid'] == '0111810002') {
                    echo json_encode($val);
                    echo "\n\n";
                }


                //if ($val['userid'] == "0145020635") echo "1";

                $newUserId =  isset(\App\Services\Qiwei\QiweiApiService::$userIdExchange[$val['userid']]) ? \App\Services\Qiwei\QiweiApiService::$userIdExchange[$val['userid']] : $val['userid'];

                $shoudongjiaren = isset($beisenRenzhisModelByJobNumber[$newUserId]) ? "0" : "1";

                $rows[$key]['newuserid'] = $newUserId;
                $rows[$key]['shoudongjiaren'] = $shoudongjiaren;
                $rows[$key]['department'] = isset($val['department']) ? json_encode($val['department']) : "[]";
                $rows[$key]['order'] = isset($val['order']) ? json_encode($val['order']) : "[]";
                $rows[$key]['extattr'] = isset($val['extattr']) ? json_encode($val['extattr']) : "[]";
                $rows[$key]['is_leader_in_dept'] = isset($val['is_leader_in_dept']) ? json_encode($val['is_leader_in_dept']) : "[]";
                $rows[$key]['direct_leader'] = isset($val['direct_leader']) ? json_encode($val['direct_leader']) : "[]";
                $rows[$key]['external_profile'] = isset($val['external_profile']) ? json_encode($val['external_profile']) : "[]";
                $rows[$key]['external_position'] = isset($val['external_position']) ? json_encode($val['external_position']) : "[]";
                $changmingcheng = '';

                if (isset($val['main_department']) && isset($bumenListModelById[$val['main_department']])) {
                    $changmingcheng = $bumenListModelById[$val['main_department']]['bumenchangmingcheng'];
                }
                echo $changmingcheng . "\n";

                $chengyuanzhanghao = "";
                if (isset($val['extattr']) && isset($val['extattr']['attrs'])) {
                    foreach ($val['extattr']['attrs'] as $attreRow) {
                        if ($attreRow['name'] == '成员账号') {
                            $chengyuanzhanghao = $attreRow['value'];
                        }
                    }
                }

                $rows[$key]['chengyuanzhanghao'] = $chengyuanzhanghao;
                $rows[$key]['bumenchangmingcheng']= $changmingcheng;
                $rows[$key]['shanchu'] = 0;
            }



            QiweiYuangong::upsert(
                $rows,
                ['userid'],
                $qiweiYuangongFillable
            );
        });

        echo "删除企微中员工\n";
        // 标记企微中员工删除
        QiweiYuangong::whereNotIn("userid", $yuangongCollect->pluck("userid"))->update(['shanchu'=>1]);




        // 企微部门匹配北森部门

        echo "企微部门信息匹配北森数据\n";

        $now = date("Y-m-d H:i:s");

        // status=1 and shanchu=0 and stdIsDeleted=0 and startDate <= NOW() AND NOW() <= stopDate;
        $qiweiBumensModel = QiweiBumen::where("shanchu", 0)->get();
        $beisenBumensModel = BeisenBumen::select(['changmingcheng', 'oId'])->where('status', 1)->where('shanchu', 0)->where('stdIsDeleted', 0)->where("startDate" , "<=", $now)->where("stopDate", ">=", $now)->get();

        $beisenBumensModelByChangmingcheng = $beisenBumensModel->keyBy('changmingcheng');

        foreach ($qiweiBumensModel as $model) {

            if (!empty($model['beisenOid'])) continue;

            if (isset($beisenBumensModelByChangmingcheng[$model['bumenchangmingcheng']])) {
                $model['beisenOid'] = $beisenBumensModelByChangmingcheng[$model['bumenchangmingcheng']]['oId'];
            } else {
                $model['beisenOid'] = 0;
            }
            $model->save();
        }




        echo "ok-" . time() . "\n";



        return Command::SUCCESS;
    }
}
