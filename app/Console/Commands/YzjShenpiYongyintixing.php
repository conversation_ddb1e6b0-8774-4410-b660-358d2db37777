<?php

namespace App\Console\Commands;

use App\Models\Jdb\YzjDept;
use App\Models\Jdb\YzjPerson;
use App\Models\Jdb\YzjPersonJob;
use App\Models\YzjShenpi;
use App\Models\YzjShenpiLog;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Library\EduConst;
use Illuminate\Support\Arr;


// 法务用印提醒
class YzjShenpiYongyintixing extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:YzjShenpiYongyintixing';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $microtime = microtime(true);

        $client = new \GuzzleHttp\Client();

        $appAccessToken = '';
        $accessToken = '';
        // 用印申请 模板ID
        $formCodeIds = ['b5910bab256a472289498c8ee9e422d8'];
        // 模板数据
        $formDefResponses = [];
        // 流程列表数据
        $formFlowResponses = [];



        $yzjDeptIds = [];
        $yzjDeptHierarchy = YzjDept::getDeptHierarchy('cc227e3e-6dfc-41a0-85e3-792c28343000');
        foreach ($yzjDeptHierarchy as $row) {
            foreach ($row as $row2) {
                $yzjDeptIds[] = $row2['orgId'];
            }
        }

        // 获取accessToken
        try {

            $response = $client->request('POST', 'https://yunzhijia.com/gateway/oauth2/token/getAccessToken', [
                    'form_params' => [
                        'timestamp' => intval($microtime * 1000),
                        'appId' => '500672631',
                        'secret' => '7QHu5vGdx488lBq9hYm2',
                        'scope' => 'team',
                        'eid' => '17916472',
                    ]
                ]
            );

            $tokenContent = $response->getBody()->getContents();
            $tokenContent = json_decode($tokenContent, true);
            if (isset($tokenContent['success'])) {
                $accessToken = $tokenContent['data']['accessToken'];
            }

            if (empty($accessToken)) throw new \Exception('accessToken failed');

        } catch (Exception $e) {

            Log::warning(json_encode([
                'c' => 'YzjShenpiYongyintixing',
                'a' => 'handle',
                's' => 'getAccessToken',
                'errMsg' => $e->getMessage(),
                'errCode' => $e->getCode(),
                'form_params' => [
                    'timestamp' => intval($microtime * 1000),
                    'appId' => '500672***',
                    'secret' => '7QHu5vGdx488lBq9h***',
                    'scope' => 'team',
                    'eid' => '17916472',
                ]
            ]));

            return Command::FAILURE;
        }

        // 获取模板数据
        try {

            foreach ($formCodeIds as $formCodeId) {
                $flowResponse = $client->request('POST', 'https://yunzhijia.com/gateway/workflow/form/thirdpart/viewFormDef?accessToken=' . $accessToken, [
                    'headers' => ['Content-Type' => 'application/json'],
                    'json' => [
                        'formCodeId' => $formCodeId,
                    ]
                ]);

                $formDefContent = $flowResponse->getBody()->getContents();
                $formDefResponses[$formCodeId] = json_decode($formDefContent, true);

                if (empty($formDefResponses[$formCodeId]))  throw new \Exception("viewFormDef {$formCodeId} failed");
            }

        } catch (Exception $e) {

            Log::warning(json_encode([
                'c' => 'YzjShenpiYongyintixing',
                'a' => 'handle',
                's' => 'viewFormDef',
                'errMsg' => $e->getMessage(),
                'errCode' => $e->getCode(),
                'headers' => ['Content-Type' => 'application/json'],
                'json' => [
                    'formCodeId' => $formCodeId,
                ]
            ]));

            return Command::FAILURE;
        }


        $flowStartTime = mktime(0,0,0, date("m")  , date("d")-90, date("Y"));
        $flowEndTime = mktime(0,0,0, date("m")  , date("d")+1, date("Y"));

        // 获取流程列表
        try {
            // 获取流程列表接口
            $pageSize = 500;
            $pageNumber = 1;
            $totalPage = 1;

            do {

                $flowResponse = $client->request('POST', 'https://yunzhijia.com/gateway/workflow/form/thirdpart/findFlows?accessToken=' . $accessToken, [
                'headers' => ['Content-Type' => 'application/json'],
                    'json' => [
                        'pageNumber' => $pageNumber,
                        'pageSize' => $pageSize,
                        'formCodeIds' => $formCodeIds,
                        "createTime" => [$flowStartTime * 1000,$flowEndTime * 1000],
                    ]
                ]);

                $flowContent = $flowResponse->getBody()->getContents();
                $flowContent = json_decode($flowContent, true);

                if(empty($flowContent)) throw new \Exception("findFlows failed. pageNumber:{$pageNumber},pageSize:{$pageSize},formCodeIds:" . implode(",", $formCodeIds));
                if ($flowContent['errorCode'] != 0)  throw new \Exception("findFlows failed errorCode ". $flowContent['errorCode'] ." error ". $flowContent['error'] .". pageNumber:{$pageNumber},pageSize:{$pageSize},formCodeIds:" . implode(",", $formCodeIds));
                //$formFlowResponses = array_merge($formFlowResponses, $flowContent['data']['list']);

                Log::info(json_encode([
                    'c' => 'YzjShenpiYongyintixing',
                    'a' => 'handle',
                    's' => 'infoFindFlows',
                    'pageNumber' => $pageNumber,
                    'total' => $flowContent['data']['total'],
                    'pages' => $flowContent['data']['pages'],
                    'pageSize' => $flowContent['data']['list'],
                    'listCount' => count($flowContent['data']['list']),
                    //'mergeListCount' => count($formFlowResponses),
                ]));


                foreach ($flowContent['data']['list'] as $row) {

                    $shenpi = YzjShenpi::firstOrNew([
                        'serialNo' => $row['serialNo']
                    ]);

                    $shenpi->title = $row['title'];
                    $shenpi->templateName = $row['templateName'];
                    $shenpi->flowInstId = $row['flowInstId'];
                    $shenpi->formCodeId = $row['formCodeId'];
                    $shenpi->formInstId = $row['formInstId'];
                    $shenpi->formDefId = $row['formDefId'];
                    $shenpi->flowSpendTime = $row['flowSpendTime'];
                    $shenpi->activityName = $row['activityName'];
                    $shenpi->activitySpendTime = $row['activitySpendTime'];
                    $shenpi->currentApprovers = $row['currentApprovers'];

                    if ($row['activityName'] == '我方用印') {
                        $shenpi->wofangyongyinApprovers = $row['currentApprovers'];
                    }
                    $shenpi->creator = $row['creator'];
                    $shenpi->createName = $row['createName'];
                    $shenpi->status = $row['status'];
                    $shenpi->flowType = $row['flowType'];
                    $shenpi->createTime = $row['createTime'];

                    if ($shenpi->warnActivityName != '' && $shenpi->warnActivityName != $shenpi->activityName ) {
                        $shenpi->warnActivityName = '';
                        $shenpi->warnStatus = '';
                    }

                    $shenpi->save();

                }

                // TODO break
//                break;

                $pageNumber++;
                $totalPage = $flowContent['data']['pages'];

                sleep(mt_rand(1,4));

            } while($pageNumber <= $totalPage);

        } catch(Exception $e) {

            Log::warning(json_encode([
                'c' => 'YzjShenpiYongyintixing',
                'a' => 'handle',
                's' => 'findFlows',
                'errMsg' => $e->getMessage(),
                'errCode' => $e->getCode(),
                'headers' => ['Content-Type' => 'application/json'],
                'json' => [
                    'pageNumber' => $pageNumber,
                    'pageSize' => $pageSize,
                    'formCodeIds' => $formCodeIds,
                ]
            ]));

            return Command::FAILURE;
        }



        // 获取领导信息
        try {

            $resGroupTokenResponse = $client->request('POST', 'https://yunzhijia.com/gateway/oauth2/token/getAccessToken', [
                    'form_params' => [
                        'timestamp' => intval($microtime * 1000),
                        'appId' => '500672631',
                        'secret' => 'YJpK1LHI20HGkEBrXU3xEDxrB47Dmsg0',
                        'scope' => 'resGroupSecret',
                        'eid' => '17916472',
                    ]
                ]
            );
            $resGroupToken = '';
            $resGroupTokenResponse = $resGroupTokenResponse->getBody()->getContents();
            $resGroupTokenResponse = json_decode($resGroupTokenResponse, true);
            if ($resGroupTokenResponse['errorCode'] == 0) {
                $resGroupToken = $resGroupTokenResponse['data']['accessToken'];
            }
            if (empty($resGroupToken)) throw new \Exception('resGroupToken is empty');

            $userLeaders = [];

            for ($i=0; $i<10; $i++) {

                $relationResponse = $client->request('POST', 'https://yunzhijia.com/gateway/openimport/open/company/queryRelations?accessToken=' . $resGroupToken, [
                    'headers' => ['Content-Type' => 'application/x-www-form-urlencoded'],
                    'form_params' => [
                        'eid' => '17916472',
                        'data' => json_encode(
                            ['relationType' => 'LEADER',
                                'begin' => $i * 1000,
                                'count' => 1000]
                        ),

                    ],
                ]);

                $relationResponse = json_decode($relationResponse->getBody()->getContents(), true);
                if (!$relationResponse['success'] || !isset($relationResponse['data']) && count($relationResponse['data']) <= 0) {
                    break;
                }

                $userLeaders = array_merge($userLeaders, $relationResponse['data']);
            }

            $userLeaders = collect($userLeaders);
            $userLeaders = $userLeaders->keyBy('openId');

        } catch(Exception $e) {

            Log::warning(json_encode([
                'c' => 'YzjShenpiYongyintixing',
                'a' => 'handle',
                's' => 'queryRelations',
                'errMsg' => $e->getMessage(),
                'errCode' => $e->getCode(),
            ]));

            return Command::FAILURE;
        }


        try {

            $response = $client->request('POST', 'https://yunzhijia.com/gateway/oauth2/token/getAccessToken', [
                    'form_params' => [
                        'timestamp' => intval($microtime * 1000),
                        'appId' => '500672631',
                        'secret' => 'q9XqBU6wtzmCTeUCDkEB',
                        'scope' => 'app',
                        'eid' => '17916472',
                    ]
                ]
            );

            $appTokenContent = $response->getBody()->getContents();
            $appTokenContent = json_decode($appTokenContent, true);

            if ($appTokenContent['errorCode'] == 0) {
                $appAccessToken = $appTokenContent['data']['accessToken'];
            }

            if (empty($appAccessToken)) throw new \Exception('app accessToken is empty');

        } catch (Exception $e) {

            Log::warning(json_encode([
                'c' => 'YzjShenpiYongyintixing',
                'a' => 'handle',
                's' => 'getAppToken',
                'errMsg' => $e->getMessage(),
                'errCode' => $e->getCode(),
            ]));

            return Command::FAILURE;
        }






        $yzjPersonJob = YzjPersonJob::select('openId')
            ->where('is_disable', 0)
            ->whereIn('orgId', $yzjDeptIds)
            ->get();
        $openIds = $yzjPersonJob->pluck('openId')->toArray();


        //---------------------------- 发送 -------------------------------

        // 14 日提醒
        $shenpiList = YzjShenpi::whereIn("activityName", ['我方用印','双方用印电子文档上传','双方用印纸质法务归档','对方用印','双方用印纸质园财务归档'])
            ->whereIn('warnStatus', ['WARNING', ''])
            ->where('activitySpendTime', '>=', 24 * 14)
            ->where('status', 'RUNNING')
            ->where('wofangyongyinApprovers', '<>', '')
            ->get();



        foreach ($shenpiList as $row) {


            if ($row['creator'] == '62de26e2e4b0bad4ff886e83') continue;


            $jituan = false;
            if (empty($row['wofangyongyinApprovers'])) continue;
            foreach ($row['wofangyongyinApprovers'] as $wofangyongyin) {
                if (in_array($wofangyongyin['oid'], $openIds)) {
                    $jituan = true;
                }
            }
            if (!$jituan) continue;

            $content = "您申请的用印流程（编号：". $row['serialNo'] ."）合同已超过14天尚未归档，请尽快将合同移交法务！点击跳转查看用印流程";

            if (isset($userLeaders[$row['creator']])) {
                $leaderPerson = YzjPerson::where('openId', $row['creator'])->first();
                $leaderContent = "您部门下". $leaderPerson['name'] ."老师的用印流程（编号为：". $row['serialNo'] ."）合同已超过14天未归档，请提醒他及时将合同移交法务，谢谢。";
            }

            $url = "https://yunzhijia.com/cloudflow-mobile/approval/". $row['formDefId'] ."/". $row['formInstId'] ."/". $row['flowInstId'];

            try {

                $sourceId = $row['serialNo'] . time().mt_rand(10000,99999);

                $body = [
                    "title" =>  "合同归档通知",
                    "content" => "",
                    "headImg" => "https://www.yunzhijia.com/docrest/file/downloadfile/63b69cda710b120001aafeb4",
                    "appId" => '500672631',
                    'sourceId' => $sourceId,
                    "params" => [
                        [
                            // 用印申请人
                            "openId"  =>  $row['creator'],
                            "url" => $url,
                            "content"  =>  $content
                        ],
//                        [
//                            // 吴磊
//                            "openId"  =>  "60e26896e4b01596045c9c38",
//                            "url" => $url,
//                            "content"  =>  $content
//                        ],
                    ]
                ];

                // 用印申请人领导
                if (isset($userLeaders[$row['creator']])) {
                    $body['params'][] = [
                            "openId"  =>  $userLeaders[$row['creator']]['leaderOpenId'],
                            "url" => $url,
                            "content"  =>  $leaderContent
                        ];
                }

                $newtodoResponse = $client->request('POST', 'https://yunzhijia.com/gateway/newtodo/open/generatetodo.json?accessToken=' . $appAccessToken, [
                        'headers' => ['Content-Type' => 'application/json'],
                        'json' => $body,
                    ]
                );

                $newtodoResponse = $newtodoResponse->getBody()->getContents();
                $newtodoResponse = json_decode($newtodoResponse, true);

                Log::info(json_encode([
                    'c' => 'YzjShenpiYongyintixing',
                    'a' => 'handle',
                    's' => 'sendNotice',
                    'body' => $body,
                    'success' => $newtodoResponse['success'],
                ]));


                $row['warnActivityName'] = $row['activityName'];
                $row['warnStatus'] = 'WARNINGAGAIN';
                $row->save();

                $yzjShenpiLog = new YzjShenpiLog();

                $yzjShenpiLog->shenpiId = $row['id'];
                $yzjShenpiLog->serialNo = $row['serialNo'];
                $yzjShenpiLog->title = $row['title'];
                $yzjShenpiLog->creator = $row['creator'];
                $yzjShenpiLog->type = 2;
                $yzjShenpiLog->params = $body;

                if (isset($userLeaders[$row['creator']])) {
                    $yzjShenpiLog->leader = $userLeaders[$row['creator']]['leaderOpenId'];
                }
                $yzjShenpiLog->save();

            } catch(Exception $e) {

                Log::warning(json_encode([
                    'c' => 'YzjShenpiYongyintixing',
                    'a' => 'handle',
                    's' => 'sendNoticeError',
                    'errMsg' => $e->getMessage(),
                    'errCode' => $e->getCode(),
                    'body' => $body,
                ]));

            }
        }



        // 7 日提醒
        $shenpiList=  YzjShenpi::whereIn("activityName", ['我方用印','双方用印电子文档上传','双方用印纸质法务归档','对方用印','双方用印纸质园财务归档'])
                    ->where('warnStatus', '')
                    ->where('activitySpendTime', '>=', 24 * 7)
                    ->where('status', 'RUNNING')
                    ->where('wofangyongyinApprovers', '<>', '')
                    ->get();

        foreach ($shenpiList as $row) {

            if ($row['creator'] == '62de26e2e4b0bad4ff886e83') continue;

            $jituan = false;
            if (empty($row['wofangyongyinApprovers'])) continue;
            foreach ($row['wofangyongyinApprovers'] as $wofangyongyin) {
                if (in_array($wofangyongyin['oid'], $openIds)) {
                    $jituan = true;
                }
            }
            if (!$jituan) continue;

            $content = "您申请的用印流程（编号：". $row['serialNo'] ."）合同已超过7天尚未归档，请尽快将合同移交法务！点击跳转查看用印流程";

            $url = "https://yunzhijia.com/cloudflow-mobile/approval/". $row['formDefId'] ."/". $row['formInstId'] ."/". $row['flowInstId'] ."";

            try {

                $sourceId = $row['serialNo'] . time().mt_rand(10000,99999);

                $body = [
                    "title" =>  "合同归档通知",
                    "content" => "",
                    "headImg" => "https://www.yunzhijia.com/docrest/file/downloadfile/63b69cda710b120001aafeb4",
                    "appId" => '500672631',
                    'sourceId' => $sourceId,
                    "params" => [
                            [
                                // 用印申请人
                                "openId"  =>  $row['creator'], //"60e26896e4b01596045c9c38",
                                "url" => $url,
                                "content"  =>  $content
                            ],
//                            [
//                                // 吴磊
//                                "openId"  =>  "60e26896e4b01596045c9c38",
//                                "url" => $url,
//                                "content"  =>  $content
//                            ],
                    ]
                ];

                $newtodoResponse = $client->request('POST', 'https://yunzhijia.com/gateway/newtodo/open/generatetodo.json?accessToken=' . $appAccessToken, [
                        'headers' => ['Content-Type' => 'application/json'],
                        'json' => $body,
                    ]
                );

                $newtodoResponse = $newtodoResponse->getBody()->getContents();
                $newtodoResponse = json_decode($newtodoResponse, true);

                Log::info(json_encode([
                    'c' => 'YzjShenpiYongyintixing',
                    'a' => 'handle',
                    's' => 'sendNotice',
                    'body' => $body,
                    'success' => $newtodoResponse['success'],
                ]));


                $row['warnActivityName'] = $row['activityName'];
                $row['warnStatus'] = 'WARNING';
                $row->save();


                $yzjShenpiLog = new YzjShenpiLog();

                $yzjShenpiLog->shenpiId = $row['id'];
                $yzjShenpiLog->serialNo = $row['serialNo'];
                $yzjShenpiLog->title = $row['title'];
                $yzjShenpiLog->creator = $row['creator'];
                $yzjShenpiLog->type = 1;
                $yzjShenpiLog->params = $body;

                $yzjShenpiLog->save();

            } catch(Exception $e) {

                Log::warning(json_encode([
                    'c' => 'YzjShenpiYongyintixing',
                    'a' => 'handle',
                    's' => 'sendNoticeError',
                    'errMsg' => $e->getMessage(),
                    'errCode' => $e->getCode(),
                    'body' => $body,
                ]));

            }
        }



        //----------------------------- 星期一推送列表给法务 ---------------------------------

        $week = date('N');

        if ($week == 1) {

            $day14Total = 0;
            $day7Total = 0;

            // 14 日提醒
            $shenpiList = YzjShenpi::whereIn("activityName", ['我方用印', '双方用印电子文档上传', '双方用印纸质法务归档', '对方用印', '双方用印纸质园财务归档'])
                //->whereIn('warnStatus', ['WARNING', ''])
                ->where('activitySpendTime', '>=', 24 * 14)
                ->where('status', 'RUNNING')
                ->where('wofangyongyinApprovers', '<>', '')
                ->get();

            foreach ($shenpiList as $row) {
                $jituan = false;
                if (empty($row['wofangyongyinApprovers'])) continue;
                foreach ($row['wofangyongyinApprovers'] as $wofangyongyin) {
                    if (in_array($wofangyongyin['oid'], $openIds)) {
                        $jituan = true;
                    }
                }
                if (!$jituan) continue;

                $day14Total++;
            }

            // 7 日提醒
            $shenpiList = YzjShenpi::whereIn("activityName", ['我方用印', '双方用印电子文档上传', '双方用印纸质法务归档', '对方用印', '双方用印纸质园财务归档'])
                // ->where('warnStatus', '')
                ->where('activitySpendTime', '>=', 24 * 7)
                ->where('activitySpendTime', '<=', 24 * 14)
                ->where('status', 'RUNNING')
                ->where('wofangyongyinApprovers', '<>', '')
                ->get();

            foreach ($shenpiList as $row) {
                $jituan = false;
                if (empty($row['wofangyongyinApprovers'])) continue;
                foreach ($row['wofangyongyinApprovers'] as $wofangyongyin) {
                    if (in_array($wofangyongyin['oid'], $openIds)) {
                        $jituan = true;
                    }
                }
                if (!$jituan) continue;

                $day7Total++;
            }

            foreach ([7, 14] as $weekIndex) {

                $total = ($weekIndex == 7 ? $day7Total : $day14Total);

                $content = "截止". date('Y年m月d日') ."，超过". $weekIndex ."天未归档的合同共". $total ."笔。点击跳转查看列表";
                // $url = "http://localhost:8001/yzjshenpi/yongyingrid";

                $url = "https://eduappv2.kidcastle.com.cn/yzjshenpi/yongyingrid";

                try {

                    $sourceId = time().mt_rand(10000,99999);

                    $body = [
                        "title" =>  "合同归档通知",
                        "content" => "",
                        "headImg" => "https://www.yunzhijia.com/docrest/file/downloadfile/63b69cda710b120001aafeb4",
                        "appId" => '500672631',
                        'sourceId' => $sourceId,
                        "params" => [
                            [
                                // 吴磊
                                "openId"  =>  "60e26896e4b01596045c9c38",
                                "url" => $url,
                                "content"  =>  $content
                            ],
                            [
                                // 张蕾
                                "openId"  =>  "62de26e2e4b0bad4ff886e83",
                                "url" => $url,
                                "content"  =>  $content
                            ],
                            [
                                // 王亚玲
                                "openId"  =>  "6453ad7ae4b0ea177a7a8928",
                                "url" => $url,
                                "content"  =>  $content
                            ],
                            [
                                // 李晓影
                                "openId"  =>  "63fede0ce4b048e2ed893748",
                                "url" => $url,
                                "content"  =>  $content
                            ],
                        ]
                    ];

                    if ($weekIndex == 14) {
                        $body['params'][] =  [
                                // 刘燕
                                "openId"  =>  "5dfc63f2e4b0b998f62d1df5",
                                "url" => $url,
                                "content"  =>  $content
                            ];
                        $body['params'][] =  [
                                // 顾问
                                "openId"  =>  "5dfc63fde4b09c41c3c35097",
                                "url" => $url,
                                "content"  =>  $content
                            ];
                    }

                    $newtodoResponse = $client->request('POST', 'https://yunzhijia.com/gateway/newtodo/open/generatetodo.json?accessToken=' . $appAccessToken, [
                            'headers' => ['Content-Type' => 'application/json'],
                            'json' => $body,
                        ]
                    );

                    $newtodoResponse = $newtodoResponse->getBody()->getContents();
                    $newtodoResponse = json_decode($newtodoResponse, true);

                    Log::info(json_encode([
                        'c' => 'YzjShenpiYongyintixing',
                        'a' => 'handle',
                        's' => 'sendFawuNotice',
                        'body' => $body,
                        'success' => $newtodoResponse['success'],
                    ]));

                    $yzjShenpiLog = new YzjShenpiLog();

                    $yzjShenpiLog->shenpiId = $row['id'];
                    $yzjShenpiLog->serialNo = $row['serialNo'];
                    $yzjShenpiLog->title = $row['title'];
                    $yzjShenpiLog->creator = $row['creator'];
                    $yzjShenpiLog->type = ($weekIndex == 7 ? 3 : 4);
                    $yzjShenpiLog->params = $body;

                    $yzjShenpiLog->save();

                } catch(Exception $e) {

                    Log::warning(json_encode([
                        'c' => 'YzjShenpiYongyintixing',
                        'a' => 'handle',
                        's' => 'sendFawuNoticeError',
                        'errMsg' => $e->getMessage(),
                        'errCode' => $e->getCode(),
                        'body' => $body,
                    ]));

                }

            }

        }








        return Command::SUCCESS;
    }
}
