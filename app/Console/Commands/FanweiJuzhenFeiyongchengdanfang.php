<?php
namespace App\Console\Commands;

use Illuminate\Console\Command;
use Library\EduConst;
use \App\Services\Fanwei\FanweiApiService;
use \App\Services\Fanwei\FanweiService;

// 泛微矩阵同步-费用承担方矩阵
class FanweiJuzhenFeiyongchengdanfang extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:fanweifeiyongchengdanfang';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $microtime = microtime(true);

        $client = new \GuzzleHttp\Client();

        $fanweiService = new FanweiService();
        $fanweiApiService = new FanweiApiService();

        $host = "";
        if (env("app_debug")) {
            $host = config("fanwei.host_dev", '');
        } else {
            $host = config("fanwei.host", '');
        }

        $beisentaizhangList = $fanweiService->getBeisenXiaoquzhuguan();
        $beisentaizhangCollect = collect($beisentaizhangList);
        $beisentaizhangByOid = $beisentaizhangCollect->keyBy("beisenoid");

        $juzhenFeiyongchengdanfangModels = \App\Models\Fanwei\JuzhenFeiyongchengdanfang::all();
        $jianmoFeiyongchengdanfangModels = \App\Models\Fanwei\JianmoFeiyongchengdanfang::orderBy("xinzuzhileixing")->get(); //all();


        $juzhenFeiyongchengdanfangByYxmc = $juzhenFeiyongchengdanfangModels->keyBy('yxmc');

        $xiaoZuzhileixing = ['园_园', '校_校', '早教_校'];
        $shiyebuZuzhileixing = ['园_事业部', '总部_部', '校_事业部', '早教_事业部', '卡通尼_事业部', '线上课程_事业部', '总部_处'];


        $table = [];
        foreach ($jianmoFeiyongchengdanfangModels as $row) {

            if (!(in_array($row['xinzuzhileixing'], $xiaoZuzhileixing)
                || strpos($row['xinzuzhileixing'], '事业部') !== false)
            )    {
                continue;
            }
            if (!is_numeric($row['id'])) {
                continue;
            }

//            ||
//             echo $row['id']. " - " . intval(is_numeric($row['id'])) . "\n\n";


            $updateData = [];

            $updateData['yxmc'] = $row['id'];

            if (in_array($row['xinzuzhileixing'], $xiaoZuzhileixing)) {

                $updateData['xc'] = $beisentaizhangByOid[$row['id']]['xiaozhangFanweiId'] ?? '';
                $updateData['qxc'] = $beisentaizhangByOid[$row['id']]['quxiaozhangFanweiId'] ?? '';
                $updateData['dd'] = $beisentaizhangByOid[$row['id']]['dudaoFanweiId'] ?? '';

                if($row['xinzuzhileixing'] == "校_校"){
                    $updateData['kxc'] = $beisentaizhangByOid[$row['id']]['kuaxiaozhangFanweiId'] ?? '';
                } else{
                    $updateData['kxc'] = '';
                }

                 // 财务督导，会计字段读取矩阵原值
                $updateData['cwdd'] = $juzhenFeiyongchengdanfangByYxmc[$row['id']]['cwdd'] ?? '';
                $updateData['hj'] = $juzhenFeiyongchengdanfangByYxmc[$row['id']]['hj'] ?? '';

            } else {

                // 事业部全部读取原值
                $updateData['xc'] = $juzhenFeiyongchengdanfangByYxmc[$row['id']]['xc'] ?? '';
                $updateData['qxc'] = $juzhenFeiyongchengdanfangByYxmc[$row['id']]['qxc'] ?? '';
                $updateData['dd'] = $juzhenFeiyongchengdanfangByYxmc[$row['id']]['dd'] ?? '';
                $updateData['cwdd'] = $juzhenFeiyongchengdanfangByYxmc[$row['id']]['cwdd'] ?? '';
                $updateData['hj'] = $juzhenFeiyongchengdanfangByYxmc[$row['id']]['hj'] ?? '';
                $updateData['kxc'] = $juzhenFeiyongchengdanfangByYxmc[$row['id']]['kxc'] ?? '';
            }

            $table[] = $updateData;
        }



        $token = $fanweiApiService->getToken();

        $url = $host . "/api/Matrix/synMatrixDept";
        #97todo 修改吴磊编号 3016->2758胡老师ID
        $userid = $fanweiApiService->encrypt('2758'); //3016

        $body = [
            'isSystem' => "1",
            'config' => [
                'matrixid' => '18',
                'userfieldname' => 'id',
                'departmentfieldname' => 'id',
                'subcompanyfieldname' => 'id',
                'indexKeyFieldKey' => 'yxmc',
                'departFieldKey' => [
//                    'yxmc',
                ],
                'subcompanyFieldKey' => [
                ],
                'userFieldKey' => [
                    'xc','qxc','kxc','dd','cwdd','hj'
                ],
            ],
            'data' => $table
        ];

        echo json_encode($body);
        echo "\n\n";



        // 费用承担方矩阵同步
        $response = $client->request('POST', $url, [
            'headers' => [
//                'Content-Type' => 'application/x-www-form-urlencoded'
                'appid' => 'd4ca4b2a-664a-4260-9423-186635c0ba02',
                'token' => $token,
                'userid' => $userid,
            ],
            'body' => json_encode($body),
        ]);

        $content = $response->getBody()->getContents();
        echo $content;




        //---------------------园校矩阵-------------------------

        $table = [];
        foreach ($jianmoFeiyongchengdanfangModels as $row) {

            if (!in_array($row['xinzuzhileixing'], $xiaoZuzhileixing))  continue;

            $updateData = [];

            $updateData['yxmc'] = $row['id'];
            $updateData['xc'] = $beisentaizhangByOid[$row['id']]['xiaozhangFanweiId'] ?? '';
            $updateData['qxc'] = $beisentaizhangByOid[$row['id']]['quxiaozhangFanweiId'] ?? '';
            $updateData['dd'] = $beisentaizhangByOid[$row['id']]['dudaoFanweiId'] ?? '';

            if($row['xinzuzhileixing'] == "校_校"){
                $updateData['kxc'] = $beisentaizhangByOid[$row['id']]['kuaxiaozhangFanweiId'] ?? '';
            } else{
                $updateData['kxc'] = '';
            }

            // 会计字段读取矩阵原值
            $updateData['hj'] = $juzhenFeiyongchengdanfangByYxmc[$row['id']]['hj'] ?? '';

            $table[] = $updateData;
        }



        $token = $fanweiApiService->getToken();
        $url = $host . "/api/Matrix/synMatrixDept";
        #97todo 修改吴磊编号 3016->2758胡老师ID
        $userid = $fanweiApiService->encrypt('2758'); //3016
        $body = [
            'isSystem' => "1",
            'config' => [
                'matrixid' => '16',
                'userfieldname' => 'id',
                'departmentfieldname' => 'id',
                'subcompanyfieldname' => 'id',
                'indexKeyFieldKey' => 'yxmc',
                'departFieldKey' => [
//                    'yxmc',
                ],
                'subcompanyFieldKey' => [
                ],
                'userFieldKey' => [
                    'xc','qxc','kxc','dd','hj'
                ],
            ],
            'data' => $table
        ];

        echo json_encode($body);
        echo "\n\n";

        // 园校矩阵同步
        $response = $client->request('POST', $url, [
            'headers' => [
                'appid' => 'd4ca4b2a-664a-4260-9423-186635c0ba02',
                'token' => $token,
                'userid' => $userid,
            ],
            'body' => json_encode($body),
        ]);
        $content = $response->getBody()->getContents();
        echo $content;
        echo "\n\n";





        echo "ok-" . time();

        return Command::SUCCESS;
    }
}
