<?php

namespace App\Console\Commands;

use App\Models\BeisenBumen;
use App\Models\Dd\DdJuese;
use App\Models\Dd\DdJueseyuangong;
use App\Models\DdBumen;
use App\Models\DdYuangong;
use App\Services\BeisenApi\BeisenService;
use App\Services\Dd\DdApiService;
use Illuminate\Console\Command;
use Library\EduConst;

// 获取北森主数据
class DdJuesetongbu extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:ddjuesetongbu';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {



        $microtime = microtime(true);
        $now = date("Y-m-d H:i:s");

        $ddJueseListModel = DdJuese::with("guize")->where("roleid", "<>", "")->get();

        $ddApiService = new DdApiService();

        //查询角色列表
        //$a  =$ddApiService->getJueseList();
        //删除某个角色
        //$a = $ddApiService->deleteJuese(4410519859);

        foreach ($ddJueseListModel as $juese) {


            echo "钉钉角色ID：" . $juese->id . "\n";

            $ddJueseYuangongListModel = DdJueseyuangong::where("jueseid", $juese->id)->get();

            // 增加
            $ddJueseYuangongListModel->chunk(20)->each(function($items) use ($juese, $ddApiService) {
                $ddUserIds = $items->pluck("ddUserId");
                echo "增加数量 : " . $ddUserIds->count() . "\n";
                $ddApiService->juesezengjiayuangong($juese->roleid, implode(",", $ddUserIds->toArray()));
            });

            // 删除
            $newDdUserIdList = $ddJueseYuangongListModel->pluck('ddUserId')->toArray();

            // [{"name":"\u738b\u5b87","userid":"619268840"}]
            $oldDdyuangonglist = $ddApiService->getJueseYuangongList($juese->roleid);

            $oldDdyuangongCollect = collect($oldDdyuangonglist);


            $oldDdUserIdList = $oldDdyuangongCollect->pluck('userid');

            $shanchuDdUserIds = $oldDdUserIdList->diff($newDdUserIdList);

            $shanchuDdUserIds->chunk(100)->each(function($items) use ($juese, $ddApiService) {
                $ddApiService->jueseshanchuyuangong($juese->roleid, implode(",", $items->toArray()));
            });
        }

        echo "ok-" . time() . "\n";

        return Command::SUCCESS;
    }

}
