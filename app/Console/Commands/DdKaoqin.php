<?php

namespace App\Console\Commands;



use AlibabaCloud\SDK\Dingtalk\Voauth2_1_0\Dingtalk;
use AlibabaCloud\SDK\Dingtalk\Voauth2_1_0\Models\GetAccessTokenRequest;
use AlibabaCloud\Tea\Exception\TeaError;
use AlibabaCloud\Tea\Utils\Utils;
use App\Models\BeisenBumen;
use App\Models\BeisenYuangong;
use App\Models\BeisenYuangongRenzhi;
use App\Models\BeisenZhiwu;
use App\Models\BeisenZhiji;
use App\Models\BeisenZhideng;
use App\Models\DdBumen;
use App\Models\DdDakajilu;
use App\Models\DdKaoqinzu;
use App\Models\DdKaoqinzuYuangong;
use App\Models\DdYuangong;
use App\Models\IccMendian;
use App\Models\IccMendianxiangqing;
use App\Models\IccRenwu;
use App\Models\IccRenwuResult;
use App\Services\BeisenApi\BeisenApiService;
use App\Services\Dd\DdApiService;
use Darabonba\OpenApi\Models\Config;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Library\EduConst;

// 获取北森主数据
class DdKaoqin extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:ddkaoqin';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';


    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $microtime = microtime(true);

//        include __DIR__ . "/../../../taobaosdk/TopSdk.php";


        $ddKaoqinzuModel = new DdKaoqinzu();
        $ddKaoqinzuYuangongModel = new DdKaoqinzuYuangong();
        $ddDakajiluModel = new DdDakajilu();
        $kaoqinzuFillable = $ddKaoqinzuModel->fillable;
        $kaoqinzuYuangongFillable = $ddKaoqinzuYuangongModel->fillable;
        $dakajiluFillable = $ddDakajiluModel->fillable;


        $ddApiService = new  DdApiService();


//--------

//        $beginTime  = mktime(0, 0, 0, date("m")  , date("d")-7, date("Y"));
//        $endTime  = mktime(0, 0, 0, date("m")  , date("d"), date("Y"));
//
//
//        // 拉取打卡记录
//        $kaoqinzuYuangongsModel = DdKaoqinzuYuangong::all();
//        $kaoqinzuYuangongsModel->chunk(50)->each(function($items, $key) use ($ddApiService, $beginTime, $endTime, $dakajiluFillable) {
//            $memberIds = $items->pluck("member_id");
//
//            $dakajiluList = $ddApiService->getDakaJieguoDetail(date("Y-m-d H:i:s", $beginTime), date("Y-m-d H:i:s", $endTime), $memberIds);
//            if(!empty($dakajiluList)){
//                print_r("<pre>");
//                print_r($dakajiluList);
//                exit;
//            }
//
//
//
//        });
//        print_r("<pre>");
//        print_r(111);
//        exit;
//        exit;

//----

        // 更新考勤组
        $kaoqinzuList = $ddApiService->getAllKaoqinzu();
        $kaoqinzuCollect = collect($kaoqinzuList);

        $kaoqinzuCollect->chunk(100)->each(function($items, $key) use ($kaoqinzuFillable) {

            $rows = $items->toArray();

            foreach ($rows as $key=>$val) {
                $rows[$key]['selected_class'] = json_encode(($val['selected_class'] ?? []));
                $rows[$key]['work_day_list'] = json_encode(($val['work_day_list'] ?? []));
                $rows[$key]['classes_list'] = json_encode(($val['classes_list'] ?? []));
                $rows[$key]['manager_list'] = json_encode(($val['manager_list'] ?? []));
                $rows[$key]['dept_name_list'] = json_encode(($val['dept_name_list'] ?? []));
                $rows[$key]['freecheck_work_days'] = json_encode(($val['freecheck_work_days'] ?? []));

                $rows[$key]['is_default'] = intval($val['is_default'] ?? '0');
                $rows[$key]['group_name'] = $val['group_name'] ?? '';
                $rows[$key]['type'] = intval($val['type'] ?? '');
                $rows[$key]['member_count'] = intval($val['member_count'] ?? '0');
                $rows[$key]['default_class_id'] = intval($val['default_class_id'] ?? '0');
                $rows[$key]['owner_user_id'] = intval($val['owner_user_id'] ?? '');

                $rows[$key]['disable_check_when_rest'] = intval($val['disable_check_when_rest'] ?? '0');
                $rows[$key]['disable_check_without_schedule'] = intval($val['disable_check_without_schedule'] ?? '0');
                $rows[$key]['enable_emp_select_class'] = intval($val['enable_emp_select_class'] ?? '0');
                $rows[$key]['freecheck_day_start_min_offset'] = intval($val['freecheck_day_start_min_offset'] ?? '0');
            }

            DdKaoqinzu::upsert(
                $rows,
                ['group_id'],
                $kaoqinzuFillable
            );
        });


        // 拉取考勤组下员工
        $opUserId = "0618223132699158";
        $kaoqinzusModel = DdKaoqinzu::all(); //where("group_id", 1218427452)->get(); //all();

        foreach ($kaoqinzusModel as $kaoqinzuModel) {

            $groupId = $kaoqinzuModel->group_id;

            $kaoqinzuYuangongList = $ddApiService->getAllKaoqinzuYuangong($opUserId, $kaoqinzuModel->group_id);
            $kaoqinzuYuangongCollect = collect($kaoqinzuYuangongList);

            $kaoqinzuYuangongCollect->chunk(100)->each(function($items, $key) use ($groupId, $kaoqinzuYuangongFillable) {

                $rows = $items->toArray();

                foreach ($rows as $key=>$val) {
                    $rows[$key]['group_id'] = $groupId;
                }

                DdKaoqinzuYuangong::upsert(
                    $rows,
                    ['group_id', 'member_id'],
                    $kaoqinzuYuangongFillable
                );

            });

            $memberIdsList = $kaoqinzuYuangongCollect->pluck("member_id");
            DdKaoqinzuYuangong::where("group_id", $groupId)->whereNotIn("member_id", $memberIdsList)->delete();
        }


        $beginTime  = mktime(0, 0, 0, date("m")  , date("d")-7, date("Y"));
        $endTime  = mktime(0, 0, 0, date("m")  , date("d"), date("Y"));



        // 拉取打卡记录
        $kaoqinzuYuangongsModel = DdKaoqinzuYuangong::all();
        $kaoqinzuYuangongsModel->chunk(50)->each(function($items, $key) use ($ddApiService, $beginTime, $endTime, $dakajiluFillable) {
            $memberIds = $items->pluck("member_id");

            $dakajiluList = $ddApiService->getDakaJieguo(date("Y-m-d H:i:s", $beginTime), date("Y-m-d H:i:s", $endTime), $memberIds);

            $dakajiluCollect = collect($dakajiluList);

            $dakajiluCollect->chunk(1)->each(function($items2, $key2) use ($dakajiluFillable) {

                $rows = $items2->toArray();
                echo json_encode($rows);
                echo "\n\n";

                DdDakajilu::upsert(
                    $rows,
                    ['id'],
                    $dakajiluFillable
                );
            });
        });


//        public function getDakaJieguo($workDateFrom, $workDateTo, $userList)







        echo "\n\n";




//        echo json_encode($kaoqinzuList);

        exit();

//        $accessToken = self::getAccessToken();

//        $deptList = $this->getAllDept();
//        $deptCollect = collect($deptList);
//
//        $ddBumen = new DdBumen();
//        $ddBumenFillable = $ddBumen->fillable;
//
//        $deptCollect->chunk(1)->each(function($items, $key) use($ddBumenFillable) {
//
//            $rows = $items->toArray();
//
//            DdBumen::upsert(
//                $rows,
//                ['dept_id'],
//                $ddBumenFillable
//            );
//        });

        $ddBumensModel = DdBumen::all();
        $ddYuangong = new DdYuangong();
        foreach ($ddBumensModel as $bumenRow) {
            $bumenUserResult = $this->getUserByBumenId($bumenRow['dept_id']);

            if (empty($bumenUserResult)) continue;

            foreach ($bumenUserResult as $key=>$val) {
                $bumenUserResult[$key]['dept_id_list'] = json_encode(($val['dept_id_list'] ?? []));

                foreach ($ddYuangong->fillable as $k) {
                    if (!isset($bumenUserResult[$key][$k])
                        && !in_array($k, ['id']))  {
                        if (in_array($k, ['dept_order', 'hired_date'])) {
                            $bumenUserResult[$key][$k] = 0;
                        } else {
                            $bumenUserResult[$key][$k] = "";
                        }

                    }
                }
            }
            echo json_encode($bumenUserResult);
            echo "\n\n";

            DdYuangong::upsert(
                $bumenUserResult,
                ['userid'],
                $ddYuangong->fillable
            );
        }



//        var_dump($arr);




//        source_identifier

//        echo json_encode($arr);

        exit();


        $c = new \DingTalkClient(\DingTalkConstant::$CALL_TYPE_OAPI, \DingTalkConstant::$METHOD_POST , \DingTalkConstant::$FORMAT_JSON);
        $req = new \OapiV2DepartmentListsubRequest();
        $req->setDeptId("930974771");

        $req->setLanguage("zh_CN");
        // {"errcode":0,"errmsg":"ok","result":[],"request_id":"16kf5gbr0a91x"}
        // {"errcode":40009,"errmsg":"\u4e0d\u5408\u6cd5\u7684\u90e8\u95e8id","request_id":"15se5a3tdzs52"}o
        // {"errcode":0,"errmsg":"ok","result":[{"auto_add_user":false,"create_dept_group":false,"dept_id":930974771,"name":"\u9ed8\u8ba4\u90e8\u95e8","parent_id":1,"source_identifier":"0"},{"auto_add_user":true,"create_dept_group":true,"dept_id":931006755,"ext":"{\"faceCount\":\"1\"}","name":"\u5409\u7684\u5821\u6559\u80b2\u96c6\u56e2","parent_id":1,"source_identifier":"1640567"},{"auto_add_user":false,"create_dept_group":false,"dept_id":937130912,"name":"\u4f9b\u5e94\u5546","parent_id":1}],"request_id":"16msjr8r8gjoq"}

        $resp=$c->execute($req, $accessToken->body->accessToken,"https://oapi.dingtalk.com/topapi/v2/department/listsub");

        echo json_encode($resp);

        var_dump($resp);




        echo "1\n\n\n";





        $c = new \DingTalkClient(\DingTalkConstant::$CALL_TYPE_OAPI, \DingTalkConstant::$METHOD_GET , \DingTalkConstant::$FORMAT_JSON);
        $req = new \OapiUserGetRequest();

        $req->setUserid("0618223132699158");
        $resp=$c->execute($req, $accessToken->body->accessToken,"https://oapi.dingtalk.com/user/get");
        echo json_encode($resp);



//        $iccApiService = new \App\Services\Icc\IccApiService("1000066");
//
//        $mendianModel = new IccMendian();
//        $mengdianXiangqingModel = new IccMendianxiangqing();
//        $renwuModel = new IccRenwu();
//        $renwuJieguoModel = new IccRenwuResult();
//
//
//        $storeFillable = $mendianModel->fillable;
//        $mendianxiangqingFillable = $mengdianXiangqingModel->fillable;
//
//        $storeList = $iccApiService->getStoreAll();
//        $storeList = collect($storeList);
//        $storeList->chunk(300)->each(function($items, $key) use ($iccApiService, $storeFillable, $mendianxiangqingFillable) {
//            $rows = $items->toArray();
//
//            foreach ($rows as $key=>$val) {
//                $rows[$key]['tags'] = json_encode(($val['tags'] ?? []));
//                $rows[$key]['fullTags'] = json_encode(($val['fullTags'] ?? []));
//                $rows[$key]['businessDistrict'] = json_encode(($val['businessDistrict'] ?? []));
//
//                foreach ($storeFillable as $fillKey) {
//                    $rows[$key][$fillKey] = $rows[$key][$fillKey] ?? null;
//                }
//            }
//
//            IccMendian::upsert(
//                $rows,
//                ['storeNo'],
//                $storeFillable
//            );
//
//            // 门店详情
//            foreach ($rows as $key=>$val) {
//                if (!isset($val['storeNo']) || empty($val['storeNo'])) continue;
//
//                $mendianxiangqing = $iccApiService->getStoreDetail(['storeNo' => $val['storeNo']]);
//
//                $mendianxiangqing['wxUserIds'] = json_encode(($mendianxiangqing['wxUserIds'] ?? []));
//                $mendianxiangqing['storeLabel'] = json_encode(($mendianxiangqing['storeLabel'] ?? []));
//                $mendianxiangqing['businessDistrict'] = json_encode(($mendianxiangqing['businessDistrict'] ?? []));
//                $mendianxiangqing['chatGroupIdPrefix'] = json_encode(($mendianxiangqing['chatGroupIdPrefix'] ?? []));
//                $mendianxiangqing['chatGroupIds'] = json_encode(($mendianxiangqing['chatGroupIds'] ?? []));
//                $mendianxiangqing['departmentPaths'] = json_encode(($mendianxiangqing['departmentPaths'] ?? []));
//
//                IccMendianxiangqing::upsert(
//                    $mendianxiangqing,
//                    ['storeNo'],
//                    $mendianxiangqingFillable
//                );
//            }
//
//        });
//
//
//        $renwuFillable = $renwuModel->fillable;
//        $renwuJieguoFillable = $renwuJieguoModel->fillable;
//        $taskList = $iccApiService->getTaskAll();
//        $taskList = collect($taskList);
//
//        $taskList->chunk(300)->each(function($items, $key) use ($iccApiService, $renwuFillable, $renwuJieguoFillable) {
//            $rows = $items->toArray();
//
////            foreach ($rows as $key=>$val) {
////                $rows[$key]['tags'] = json_encode(($val['tags'] ?? []));
////                $rows[$key]['fullTags'] = json_encode(($val['fullTags'] ?? []));
////                $rows[$key]['businessDistrict'] = json_encode(($val['businessDistrict'] ?? []));
////
////                foreach ($storeFillable as $fillKey) {
////                    $rows[$key][$fillKey] = $rows[$key][$fillKey] ?? null;
////                }
////            }
//
//            IccRenwu::upsert(
//                $rows,
//                ['taskId'],
//                $renwuFillable
//            );
//
//
//            // 循环获取任务详情
//            foreach ($rows as $key=>$val) {
//                $renwu = $val;
//                $renwujieguoList = $iccApiService->getTaskDetailAll(['taskId'=>$val['taskId']]);
//                $renwujieguoList = collect($renwujieguoList);
//
//                $renwujieguoList->chunk(100)->each(function ($jieguoItems) use($renwu, $renwuJieguoFillable) {
//                    $jieguoItemsArr = $jieguoItems->toArray();
//
//                    foreach ($jieguoItemsArr as $key=>$val) {
//                        $jieguoItemsArr[$key]['taskId'] = $renwu['taskId'];
//                        $jieguoItemsArr[$key]['storeNos'] = json_encode(($jieguoItemsArr[$key]['storeNos']??[]));
//                    }
//
//                    IccRenwuResult::upsert(
//                        $jieguoItemsArr,
//                        ['taskId', 'taskDate', 'wxUserId', 'externalUserId'],
//                        $renwuJieguoFillable
//                    );
//
//                });
//            }
//        });


//        $zhijiCollect->chunk(300)->each(function($items, $key) use ($beisenZhijiFillable, $zhijiCollectByOid) {
//
//            $rows = $items->toArray();
//            foreach ($rows as $key=>$val) {
//                unset($rows[$key]['customProperties']);
//                unset($rows[$key]['translateProperties']);
//                unset($rows[$key]['sysMartionProperties']);
//            }
//
//            BeisenZhiji::upsert(
//                $rows,
//                ['objectId'],
//                $beisenZhijiFillable
//            );
//
////                DB::table('t_beisen_bumen')->insert($rows);
//        });


        echo "ok-" . time();

        return Command::SUCCESS;
    }
}
