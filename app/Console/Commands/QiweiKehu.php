<?php

namespace App\Console\Commands;

use App\Models\BeisenBumen;
use App\Models\BeisenYuangong;
use App\Models\Kdd\KddKehubiaoqian;
use App\Models\Qiwei\QiweiBumen;
use App\Models\Qiwei\QiweiXunibumenyuangong;
use App\Models\Qiwei\QiweiYuangong;
use App\Models\Qiwei\QiweiYuangongqun;
use App\Services\BeisenApi\BeisenService;
use App\Services\Qiwei\QiweiApiService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Query\Builder;

// 企微组织架构同步
class QiweiKehu extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:qiweikehu';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $microtime = microtime(true);

        echo "begin-" . time() . "\n";
        // QiweiYuangong::where('enable',1)->where('qiweishanchu', 0)->where('shanchu', 0)->where('userid', '0110210366')->dd();

//        ->where('userid','81100002571')
//         $qiweiYuangongsModel = QiweiYuangong::where('enable',1)->where('qiweishanchu', 0)->where('shanchu', 0)->where('userid', '0110210366')->get();
        $qiweiYuangongsModel = QiweiYuangong::where('enable',1)->where('qiweishanchu', 0)->where('shanchu', 0)->get();
        // TODO 测试
        // $qiweiYuangongsModel = $qiweiYuangongsModel->where('userid', '0720310002');


        $qiweiApiService = new QiweiApiService();
        $qiweiKehuModel = new \App\Models\Qiwei\QiweiKehu();
        $kddKehubiaoqianModel = new KddKehubiaoqian();
        $qiweiYuanongsModelByUserId =  QiweiYuangong::all()->keyBy("userid");

        $qiweiYuangongsModel->chunk(1)->each(function($items) use ($qiweiApiService, $qiweiKehuModel, $qiweiYuanongsModelByUserId) {

            $userIds = $items->pluck("userid")->toArray();
//            $userIds[] = '0110210366'; //['0110210366'];
           // $userIds= ['81101000054']; //['0110210366'];

            $kehuList = $qiweiApiService->getKehuXiangqingPiliangAll($userIds);

            if (empty($kehuList)) return;
            $kehuIdList = [];
            foreach ($kehuList as $kehuVal) {
                $userIdKey = $kehuVal['follow_info']['userid'];
                $kehuIdList[$userIdKey][] = $kehuVal['external_contact']['external_userid'];
            }

            $kehuCollect = collect($kehuList);

            $kehuCollect->chunk(100)->each(function($kehuItems) use ($qiweiApiService, $qiweiKehuModel, $qiweiYuanongsModelByUserId) {


                $kehuList = $kehuItems->toArray();

//                echo json_encode($kehuList);
//                echo "\n\n";

                $rows = [];
                foreach ($kehuList as $key=>$val) {
                    $row = [];

                    $row['externalUnionId'] = $val['external_contact']['unionid'] ?? '';
                    $row['externalUserId'] = $val['external_contact']['external_userid'] ?? '';
                    $row['externalName'] = $val['external_contact']['name'] ?? '';
                    $row['type'] = $val['external_contact']['type'];



                    $row['userId'] = $val['follow_info']['userid'];
                    $row['userName'] = $qiweiYuanongsModelByUserId[$row['userId']]['name'] ?? '';
                    $row['userLongDept'] = $qiweiYuanongsModelByUserId[$row['userId']]['bumenchangmingcheng'] ?? '';
                    $row['deptId'] = $qiweiYuanongsModelByUserId[$row['userId']]['main_department'] ?? 0;

                    $row['addTime'] = $val['follow_info']['createtime'];
                    $row['addDateTime'] = date("Y-m-d H:i:s", $val['follow_info']['createtime']);
                    $row['addWay'] = $val['follow_info']['add_way'];

                    $row['state'] = $val['follow_info']['state'] ?? '';

                    $row['shanchu'] = 0;

                    $row['tagIds'] = json_encode($val['follow_info']['tag_id']);

//                    if ($row['externalName'] == '王哪跑') {
//                        echo json_encode($row);
//                        echo "\n\n\n";
//                    }
                    //20250311同步更新企微标签状态
                    KddKehubiaoqian::where('wxunionid',$row['externalUnionId'])->update(['gengxinzhuangtai' => 1]);
                    $rows[] = $row;
                }

                if (!empty($rows)) {
                    $rs = \App\Models\Qiwei\QiweiKehu::upsert(
                        $rows,
                        ['externalUserId', 'externalUnionId', 'userId'],
                        $qiweiKehuModel->fillable
                    );
                }
            });




//            echo json_encode($kehuIdList);
//            echo "\n\n";




            // 标记删除
            if (!empty($kehuIdList)) {
                foreach ($kehuIdList as $kehuKey=>$kehuVal) {

//                    echo json_encode($kehuKey);
//                    echo "\n\n";
//                    echo json_encode($kehuVal);
//                    echo "\n\n";

                    $kehuExternalUserIdList =  \App\Models\Qiwei\QiweiKehu::select(['externalUserId'])->where('userId', $kehuKey)->get();
                    $kehuExternalUserIdCollect = $kehuExternalUserIdList->pluck('externalUserId');

                    $shanchuUserIds = $kehuExternalUserIdCollect->diff($kehuVal)->toArray();


                    if (!empty($shanchuUserIds)) {


//                        echo json_encode($shanchuUserIds);
//                        echo "\n\n";
//                        exit();

                        \App\Models\Qiwei\QiweiKehu::where('userId', $kehuKey)->whereIn("externalUserId", $shanchuUserIds)->update(['shanchu'=>"1"]);
                    }

                }
            }
        });




        echo "ok-" . time() . "\n";

        return Command::SUCCESS;
    }


}
