<?php

namespace App\Console\Commands;

use App\Models\BeisenBumen;
use App\Models\BeisenYuangong;
use App\Models\BeisenYuangongRenzhi;
use App\Models\BeisenZhiwu;
use App\Models\BeisenZhiji;
use App\Models\BeisenZhideng;
use App\Services\BeisenApi\BeisenApiService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Library\EduConst;

// 获取北森主数据
class Beisenzhushuju extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:beisenzhushuju';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $microtime = microtime(true);

        $client = new \GuzzleHttp\Client();

        $beisenBumen = new BeisenBumen();
        $beisenZhiwu = new BeisenZhiwu();
        $beisenYuangong = new BeisenYuangong();
        $beisenYuangongRenzhi = new BeisenYuangongRenzhi();
        $beisenZhiji = new BeisenZhiji();
        $beisenZhideng = new BeisenZhideng();

        $beisenBumenFillable = $beisenBumen->fillable;
        $beisenZhiwuFillable = $beisenZhiwu->fillable;
        $beisenYuangongFillable = $beisenYuangong->fillable;
        $beisenYuangongRenzhiFillable = $beisenYuangongRenzhi->fillable;
        $beisenZhijiFillable = $beisenZhiji->fillable;
        $beisenZhidengFillable = $beisenZhideng->fillable;

        unset($beisenBumenFillable['id']);
        unset($beisenZhiwuFillable['id']);
        unset($beisenYuangongFillable['id']);
        unset($beisenYuangongRenzhiFillable['id']);
        unset($beisenZhijiFillable['id']);
        unset($beisenZhidengFillable['id']);

        try {
            // 测试服
            //$beisenApiService = new BeisenApiService('EC7CDBEF911247CE8A87824894B63762');
            // 正式服
            $beisenApiService = new BeisenApiService('017A8EFF9C2F4E8F923A3F3B0574F797');

            // 获取职级信息
            $search = [
                'withDisabled' => false,
                'timeWindowQueryType' => '1',
                'isWithDeleted' => false,
            ];
            $zhijiList = $beisenApiService->getZhijiAll($search);
            echo "职级总数:" . count($zhijiList) . "\n";
            $zhijiCollect = collect($zhijiList);
            $zhijiCollectByOid = $zhijiCollect->keyBy('oId');

            $zhijiCollect->chunk(300)->each(function($items, $key) use ($beisenZhijiFillable, $zhijiCollectByOid) {

                $rows = $items->toArray();
                foreach ($rows as $key=>$val) {
                    unset($rows[$key]['customProperties']);
                    unset($rows[$key]['translateProperties']);
                    unset($rows[$key]['sysMartionProperties']);
                }

                BeisenZhiji::upsert(
                    $rows,
                    ['objectId'],
                    $beisenZhijiFillable
                );

//                DB::table('t_beisen_bumen')->insert($rows);
            });


            BeisenZhiji::whereNotIn('objectId', $zhijiCollect->pluck('objectId'))->update(["shanchu" => 1]);

            // 获取职等信息
            $search = [
                'withDisabled' => false,
                'timeWindowQueryType' => '1',
                'isWithDeleted' => false,
            ];
            $zhidengList = $beisenApiService->getZhidengAll($search);

            echo "职等总数:" . count($zhidengList) . "\n";
            $zhidengCollect = collect($zhidengList);
            $zhidengCollectByOid = $zhidengCollect->keyBy('oId');

            $zhidengCollect->chunk(300)->each(function($items, $key) use ($beisenZhidengFillable, $zhidengCollectByOid) {

                $rows = $items->toArray();
                foreach ($rows as $key=>$val) {
                    unset($rows[$key]['customProperties']);
                    unset($rows[$key]['translateProperties']);
                    unset($rows[$key]['sysMartionProperties']);
                }

                BeisenZhideng::upsert(
                    $rows,
                    ['objectId'],
                    $beisenZhidengFillable
                );

//                DB::table('t_beisen_bumen')->insert($rows);
            });


            BeisenZhideng::whereNotIn('objectId', $zhidengCollect->pluck('objectId'))->update(["shanchu" => 1]);



            // 获取部门信息
            $search = [
                'withDisabled' => false,
                'isOnlyGetCurrent' => true,
                'timeWindowQueryType' => '1',
                'isWithDeleted' => false,
            ];
            $bumenList = $beisenApiService->getBumenAll($search);
            echo "部门总数:" . count($bumenList) . "\n";
            $bumenCollect = collect($bumenList);
            $bumenCollectByOid = $bumenCollect->keyBy('oId');


            collect($bumenList)->chunk(300)->each(function($items, $key) use ($beisenBumenFillable, $bumenCollectByOid) {

                // customProperties/extyewuxitongbianma1_611888_1841956546   业务系统编码
                // customProperties/extzuzhileixing_611888_2044304065   组织类型

                // [{"name":"\u9ed8\u8ba4\u90e8\u95e8","name_en_US":"Default Department","name_zh_TW":"\u9ed8\u8a8d\u90e8\u9580","shortName":null,"shortName_en_US":null,"shortName_zh_TW":null,"code":"DefaultDept","oId":0,"level":null,"status":1,"establishDate":"1900-01-01T00:00:00","startDate":"1900-01-01T00:00:00","stopDate":"9999-12-31T00:00:00","changeDate":"1900-01-01T00:00:00","pOIdOrgAdmin":*********,"pOIdOrgReserve2":*********,"pOIdOrgReserve3":*********,"isCurrentRecord":true,"personInCharge":null,"hRBP":null,"shopOwner":null,"administrativeAssistant":null,"personInChargeDeputy":null,"businessModifiedBy":619169758,"businessModifiedTime":"2024-06-03T10:40:49","legalMan":null,"address":null,"fax":null,"postcode":null,"phone":null,"url":null,"description":null,"number":null,"broadType":"2","economicType":null,"industry":null,"place":null,"orderAdmin":1,"orderReserve2":null,"orderReserve3":null,"comment":null,"oIdOrganizationType":null,"pOIdOrgAdmin_TreePath":"*********\/0","pOIdOrgAdmin_TreeLevel":2,"pOIdOrgReserve2_TreePath":"*********\/0","pOIdOrgReserve2_TreeLevel":2,"firstLevelOrganization":null,"secondLevelOrganization":null,"thirdLevelOrganization":null,"fourthLevelOrganization":null,"fifthLevelOrganization":null,"sixthLevelOrganization":null,"seventhLevelOrganization":null,"eighthLevelOrganization":null,"ninthLevelOrganization":null,"tenthLevelOrganization":null,"orderCode":null,"pOIdOrgAdminNameTreePath":"*********\/0","isVirtualOrg":false,"leaderWithSpecificDuty":null,"objectId":"52c7651b-f954-4ef0-9d65-b68bb4239938","customProperties":null,"translateProperties":null,"sysMartionProperties":{"Name_en_US":"Default Department","Name_zh_TW":"\u9ed8\u8a8d\u90e8\u9580"},"createdBy":10000,"createdTime":"2024-05-28T10:46:04","modifiedBy":10000,"modifiedTime":"2024-06-03T10:40:49","stdIsDeleted":false},{"name":"\u5409\u7684\u5821\u6559\u80b2\u96c6\u56e2","name_en_US":null,"name_zh_TW":null,"shortName":"\u5409\u7684\u5821\u6559\u80b2\u96c6\u56e2","shortName_en_US":null,"shortName_zh_TW":null,"code":"KID","oId":1640567,"level":"ac935744-132a-42a7-b814-766059bb7335","status":1,"establishDate":"1990-01-01T00:00:00","startDate":"1990-01-01T00:00:00","stopDate":"9999-12-31T00:00:00","changeDate":"1990-01-01T00:00:00","pOIdOrgAdmin":*********,"pOIdOrgReserve2":-2,"pOIdOrgReserve3":-2,"isCurrentRecord":true,"personInCharge":619275697,"hRBP":null,"shopOwner":null,"administrativeAssistant":null,"personInChargeDeputy":null,"businessModifiedBy":619222558,"businessModifiedTime":"2024-06-06T12:30:04","legalMan":null,"address":null,"fax":null,"postcode":null,"phone":null,"url":null,"description":null,"number":null,"broadType":"2","economicType":null,"industry":null,"place":null,"orderAdmin":19,"orderReserve2":null,"orderReserve3":null,"comment":null,"oIdOrganizationType":null,"pOIdOrgAdmin_TreePath":"*********\/1640567","pOIdOrgAdmin_TreeLevel":2,"pOIdOrgReserve2_TreePath":null,"pOIdOrgReserve2_TreeLevel":null,"firstLevelOrganization":1640567,"secondLevelOrganization":null,"thirdLevelOrganization":null,"fourthLevelOrganization":null,"fifthLevelOrganization":null,"sixthLevelOrganization":null,"seventhLevelOrganization":null,"eighthLevelOrganization":null,"ninthLevelOrganization":null,"tenthLevelOrganization":null,"orderCode":null,"pOIdOrgAdminNameTreePath":"*********\/1640567","isVirtualOrg":false,"leaderWithSpecificDuty":null,"objectId":"5e8f0851-2e37-4f65-a395-1a9debfdec5b","customProperties":{"extyewuxitongbianma1_611888_1841956546":"KID"},"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T11:06:17","modifiedBy":619222558,"modifiedTime":"2024-06-06T12:30:04","stdIsDeleted":false},{"name":"\u5b89\u4eb2\u8bfe\u8f85\u90e8","name_en_US":null,"name_zh_TW":null,"shortName":"\u5b89\u4eb2\u8bfe\u8f85\u90e8","shortName_en_US":null,"shortName_zh_TW":null,"code":"SRDQ1NC106410AQ","oId":1636282,"level":"14763d07-f648-45b1-96ac-182fdc4bb0b1","status":1,"establishDate":"1990-01-01T00:00:00","startDate":"2024-06-03T00:00:00","stopDate":"9999-12-31T00:00:00","changeDate":"2024-06-03T00:00:00","pOIdOrgAdmin":1636117,"pOIdOrgReserve2":null,"pOIdOrgReserve3":null,"isCurrentRecord":true,"personInCharge":null,"hRBP":null,"shopOwner":null,"administrativeAssistant":null,"personInChargeDeputy":null,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-03T12:25:36","legalMan":null,"address":null,"fax":null,"postcode":null,"phone":null,"url":null,"description":null,"number":null,"broadType":"2","economicType":null,"industry":null,"place":null,"orderAdmin":2,"orderReserve2":null,"orderReserve3":null,"comment":null,"oIdOrganizationType":null,"pOIdOrgAdmin_TreePath":"*********\/1640567\/1636044\/1636046\/1636055\/1636117\/1636282","pOIdOrgAdmin_TreeLevel":7,"pOIdOrgReserve2_TreePath":"*********\/0\/1636282","pOIdOrgReserve2_TreeLevel":3,"firstLevelOrganization":1640567,"secondLevelOrganization":1636044,"thirdLevelOrganization":1636046,"fourthLevelOrganization":1636055,"fifthLevelOrganization":1636117,"sixthLevelOrganization":1636282,"seventhLevelOrganization":null,"eighthLevelOrganization":null,"ninthLevelOrganization":null,"tenthLevelOrganization":null,"orderCode":null,"pOIdOrgAdminNameTreePath":"*********\/1640567\/1636044\/1636046\/1636055\/1636117\/1636282","isVirtualOrg":false,"leaderWithSpecificDuty":null,"objectId":"29ecbf3a-097b-4626-8db3-0e61c92e3364","customProperties":{"extyewuxitongbianma1_611888_1841956546":"SRDQ1NC106410AQ","extzuzhileixing_611888_2044304065":"17"},"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T12:25:36","modifiedBy":*********,"modifiedTime":"2024-06-03T12:26:18","stdIsDeleted":false},{"name":"\u6559\u5b66\u90e8","name_en_US":null,"name_zh_TW":null,"shortName":"\u6559\u5b66\u90e8","shortName_en_US":null,"shortName_zh_TW":null,"code":"SRDQ1NC206406JX","oId":1636303,"level":"14763d07-f648-45b1-96ac-182fdc4bb0b1","status":1,"establishDate":"1990-01-01T00:00:00","startDate":"2024-06-03T00:00:00","stopDate":"9999-12-31T00:00:00","changeDate":"2024-06-03T00:00:00","pOIdOrgAdmin":1636127,"pOIdOrgReserve2":null,"pOIdOrgReserve3":null,"isCurrentRecord":true,"personInCharge":null,"hRBP":null,"shopOwner":null,"administrativeAssistant":null,"personInChargeDeputy":null,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-03T12:25:36","legalMan":null,"address":null,"fax":null,"postcode":null,"phone":null,"url":null,"description":null,"number":null,"broadType":"2","economicType":null,"industry":null,"place":null,"orderAdmin":1,"orderReserve2":null,"orderReserve3":null,"comment":null,"oIdOrganizationType":null,"pOIdOrgAdmin_TreePath":"*********\/1640567\/1636044\/1636046\/1636056\/1636127\/1636303","pOIdOrgAdmin_TreeLevel":7,"pOIdOrgReserve2_TreePath":"*********\/0\/1636303","pOIdOrgReserve2_TreeLevel":3,"firstLevelOrganization":1640567,"secondLevelOrganization":1636044,"thirdLevelOrganization":1636046,"fourthLevelOrganization":1636056,"fifthLevelOrganization":1636127,"sixthLevelOrganization":1636303,"seventhLevelOrganization":null,"eighthLevelOrganization":null,"ninthLevelOrganization":null,"tenthLevelOrganization":null,"orderCode":null,"pOIdOrgAdminNameTreePath":"*********\/1640567\/1636044\/1636046\/1636056\/1636127\/1636303","isVirtualOrg":false,"leaderWithSpecificDuty":null,"objectId":"f2d391b6-827a-419d-964f-009eda08e7de","customProperties":{"extyewuxitongbianma1_611888_1841956546":"SRDQ1NC206406JX","extzuzhileixing_611888_2044304065":"17"},"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T12:25:36","modifiedBy":*********,"modifiedTime":"2024-06-03T12:26:17","stdIsDeleted":false},{"name":"\u5b9e\u4e60\u90e8","name_en_US":null,"name_zh_TW":null,"shortName":"\u5b9e\u4e60\u90e8","shortName_en_US":null,"shortName_zh_TW":null,"code":"SRDQ1NC106403SX","oId":1636277,"level":"14763d07-f648-45b1-96ac-182fdc4bb0b1","status":1,"establishDate":"1990-01-01T00:00:00","startDate":"2024-06-03T00:00:00","stopDate":"9999-12-31T00:00:00","changeDate":"2024-06-03T00:00:00","pOIdOrgAdmin":1636116,"pOIdOrgReserve2":null,"pOIdOrgReserve3":null,"isCurrentRecord":true,"personInCharge":null,"hRBP":null,"shopOwner":null,"administrativeAssistant":null,"personInChargeDeputy":null,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-03T12:25:36","legalMan":null,"address":null,"fax":null,"postcode":null,"phone":null,"url":null,"description":null,"number":null,"broadType":"2","economicType":null,"industry":null,"place":null,"orderAdmin":2,"orderReserve2":null,"orderReserve3":null,"comment":null,"oIdOrganizationType":null,"pOIdOrgAdmin_TreePath":"*********\/1640567\/1636044\/1636046\/1636055\/1636116\/1636277","pOIdOrgAdmin_TreeLevel":7,"pOIdOrgReserve2_TreePath":"*********\/0\/1636277","pOIdOrgReserve2_TreeLevel":3,"firstLevelOrganization":1640567,"secondLevelOrganization":1636044,"thirdLevelOrganization":1636046,"fourthLevelOrganization":1636055,"fifthLevelOrganization":1636116,"sixthLevelOrganization":1636277,"seventhLevelOrganization":null,"eighthLevelOrganization":null,"ninthLevelOrganization":null,"tenthLevelOrganization":null,"orderCode":null,"pOIdOrgAdminNameTreePath":"*********\/1640567\/1636044\/1636046\/1636055\/1636116\/1636277","isVirtualOrg":false,"leaderWithSpecificDuty":null,"objectId":"1246eca1-1e05-4e68-a2e3-bc60801306d5","customProperties":{"extyewuxitongbianma1_611888_1841956546":"SRDQ1NC106403SX","extzuzhileixing_611888_2044304065":"17"},"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T12:25:36","modifiedBy":*********,"modifiedTime":"2024-06-03T12:26:18","stdIsDeleted":false},{"name":"\u6559\u5b66\u90e8","name_en_US":null,"name_zh_TW":null,"shortName":"\u6559\u5b66\u90e8","shortName_en_US":null,"shortName_zh_TW":null,"code":"SRDQ1SH201489JX","oId":1636201,"level":"14763d07-f648-45b1-96ac-182fdc4bb0b1","status":1,"establishDate":"1990-01-01T00:00:00","startDate":"2024-06-03T00:00:00","stopDate":"9999-12-31T00:00:00","changeDate":"2024-06-03T00:00:00","pOIdOrgAdmin":1636082,"pOIdOrgReserve2":null,"pOIdOrgReserve3":null,"isCurrentRecord":true,"personInCharge":619268625,"hRBP":null,"shopOwner":null,"administrativeAssistant":null,"personInChargeDeputy":null,"businessModifiedBy":619169758,"businessModifiedTime":"2024-06-04T11:58:56","legalMan":null,"address":null,"fax":null,"postcode":null,"phone":null,"url":null,"description":null,"number":null,"broadType":"2","economicType":null,"industry":null,"place":null,"orderAdmin":2,"orderReserve2":null,"orderReserve3":null,"comment":null,"oIdOrganizationType":null,"pOIdOrgAdmin_TreePath":"*********\/1640567\/1636044\/1636046\/1636052\/1636082\/1636201","pOIdOrgAdmin_TreeLevel":7,"pOIdOrgReserve2_TreePath":"*********\/0\/1636201","pOIdOrgReserve2_TreeLevel":3,"firstLevelOrganization":1640567,"secondLevelOrganization":1636044,"thirdLevelOrganization":1636046,"fourthLevelOrganization":1636052,"fifthLevelOrganization":1636082,"sixthLevelOrganization":1636201,"seventhLevelOrganization":null,"eighthLevelOrganization":null,"ninthLevelOrganization":null,"tenthLevelOrganization":null,"orderCode":null,"pOIdOrgAdminNameTreePath":"*********\/1640567\/1636044\/1636046\/1636052\/1636082\/1636201","isVirtualOrg":false,"leaderWithSpecificDuty":null,"objectId":"14cb4adf-20a0-48ed-b484-f98e67f163c9","customProperties":{"extyewuxitongbianma1_611888_1841956546":"SRDQ1SH201489JX","extzuzhileixing_611888_2044304065":"17"},"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T12:25:36","modifiedBy":619169758,"modifiedTime":"2024-06-04T11:58:56","stdIsDeleted":false},{"name":"\u4e0a\u6d774\u533a","name_en_US":null,"name_zh_TW":null,"shortName":"\u4e0a\u6d774\u533a","shortName_en_US":null,"shortName_zh_TW":null,"code":"SRDQ1SH4","oId":1636054,"level":"13afb9ed-cdef-4693-a7fc-7369a4af8b85","status":1,"establishDate":"1990-01-01T00:00:00","startDate":"2024-06-03T00:00:00","stopDate":"9999-12-31T00:00:00","changeDate":"2024-06-03T00:00:00","pOIdOrgAdmin":1636046,"pOIdOrgReserve2":null,"pOIdOrgReserve3":null,"isCurrentRecord":true,"personInCharge":619268544,"hRBP":619268859,"shopOwner":null,"administrativeAssistant":null,"personInChargeDeputy":null,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-04T12:25:54","legalMan":null,"address":null,"fax":null,"postcode":null,"phone":null,"url":null,"description":null,"number":null,"broadType":"2","economicType":null,"industry":null,"place":null,"orderAdmin":2,"orderReserve2":null,"orderReserve3":null,"comment":null,"oIdOrganizationType":null,"pOIdOrgAdmin_TreePath":"*********\/1640567\/1636044\/1636046\/1636054","pOIdOrgAdmin_TreeLevel":5,"pOIdOrgReserve2_TreePath":"*********\/0\/1636054","pOIdOrgReserve2_TreeLevel":3,"firstLevelOrganization":1640567,"secondLevelOrganization":1636044,"thirdLevelOrganization":1636046,"fourthLevelOrganization":1636054,"fifthLevelOrganization":null,"sixthLevelOrganization":null,"seventhLevelOrganization":null,"eighthLevelOrganization":null,"ninthLevelOrganization":null,"tenthLevelOrganization":null,"orderCode":null,"pOIdOrgAdminNameTreePath":"*********\/1640567\/1636044\/1636046\/1636054","isVirtualOrg":false,"leaderWithSpecificDuty":null,"objectId":"63c7f20f-1383-4d00-8f74-533a1b7c1e05","customProperties":{"extyewuxitongbianma1_611888_1841956546":"SRDQ1SH4","extyuangongguanxi_611888_1546679475":619268855,"extzuzhileixing_611888_2044304065":"13"},"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T12:25:36","modifiedBy":*********,"modifiedTime":"2024-06-04T12:25:54","stdIsDeleted":false},{"name":"\u5357\u660c\u4e16\u7eaa\u6821","name_en_US":null,"name_zh_TW":null,"shortName":"\u5357\u660c\u4e16\u7eaa\u6821","shortName_en_US":null,"shortName_zh_TW":null,"code":"SRDQ1NC206405","oId":1636125,"level":"b66d012d-25be-46b5-99b5-f96042ce3e55","status":1,"establishDate":"1990-01-01T00:00:00","startDate":"2024-06-03T00:00:00","stopDate":"9999-12-31T00:00:00","changeDate":"2024-06-03T00:00:00","pOIdOrgAdmin":1636056,"pOIdOrgReserve2":null,"pOIdOrgReserve3":null,"isCurrentRecord":true,"personInCharge":619268437,"hRBP":null,"shopOwner":null,"administrativeAssistant":null,"personInChargeDeputy":null,"businessModifiedBy":619169758,"businessModifiedTime":"2024-06-04T11:58:56","legalMan":null,"address":null,"fax":null,"postcode":null,"phone":null,"url":null,"description":null,"number":null,"broadType":"2","economicType":null,"industry":null,"place":null,"orderAdmin":3,"orderReserve2":null,"orderReserve3":null,"comment":null,"oIdOrganizationType":null,"pOIdOrgAdmin_TreePath":"*********\/1640567\/1636044\/1636046\/1636056\/1636125","pOIdOrgAdmin_TreeLevel":6,"pOIdOrgReserve2_TreePath":"*********\/0\/1636125","pOIdOrgReserve2_TreeLevel":3,"firstLevelOrganization":1640567,"secondLevelOrganization":1636044,"thirdLevelOrganization":1636046,"fourthLevelOrganization":1636056,"fifthLevelOrganization":1636125,"sixthLevelOrganization":null,"seventhLevelOrganization":null,"eighthLevelOrganization":null,"ninthLevelOrganization":null,"tenthLevelOrganization":null,"orderCode":null,"pOIdOrgAdminNameTreePath":"*********\/1640567\/1636044\/1636046\/1636056\/1636125","isVirtualOrg":false,"leaderWithSpecificDuty":null,"objectId":"472e41ff-0621-4e04-8ff7-14e16e820dd4","customProperties":{"extyewuxitongbianma1_611888_1841956546":"06405","extzuzhileixing_611888_2044304065":"14"},"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T12:25:36","modifiedBy":619169758,"modifiedTime":"2024-06-04T11:58:56","stdIsDeleted":false},{"name":"\u5357\u660c\u5b50\u5b89\u6821","name_en_US":null,"name_zh_TW":null,"shortName":"\u5357\u660c\u5b50\u5b89\u6821","shortName_en_US":null,"shortName_zh_TW":null,"code":"SRDQ1NC206401","oId":1636126,"level":"b66d012d-25be-46b5-99b5-f96042ce3e55","status":1,"establishDate":"1990-01-01T00:00:00","startDate":"2024-06-03T00:00:00","stopDate":"9999-12-31T00:00:00","changeDate":"2024-06-03T00:00:00","pOIdOrgAdmin":1636056,"pOIdOrgReserve2":null,"pOIdOrgReserve3":null,"isCurrentRecord":true,"personInCharge":619268431,"hRBP":null,"shopOwner":null,"administrativeAssistant":null,"personInChargeDeputy":null,"businessModifiedBy":619169758,"businessModifiedTime":"2024-06-04T11:58:56","legalMan":null,"address":null,"fax":null,"postcode":null,"phone":null,"url":null,"description":null,"number":null,"broadType":"2","economicType":null,"industry":null,"place":null,"orderAdmin":1,"orderReserve2":null,"orderReserve3":null,"comment":null,"oIdOrganizationType":null,"pOIdOrgAdmin_TreePath":"*********\/1640567\/1636044\/1636046\/1636056\/1636126","pOIdOrgAdmin_TreeLevel":6,"pOIdOrgReserve2_TreePath":"*********\/0\/1636126","pOIdOrgReserve2_TreeLevel":3,"firstLevelOrganization":1640567,"secondLevelOrganization":1636044,"thirdLevelOrganization":1636046,"fourthLevelOrganization":1636056,"fifthLevelOrganization":1636126,"sixthLevelOrganization":null,"seventhLevelOrganization":null,"eighthLevelOrganization":null,"ninthLevelOrganization":null,"tenthLevelOrganization":null,"orderCode":null,"pOIdOrgAdminNameTreePath":"*********\/1640567\/1636044\/1636046\/1636056\/1636126","isVirtualOrg":false,"leaderWithSpecificDuty":null,"objectId":"ba104c3b-a8c7-408f-abba-9dcdf7415763","customProperties":{"extyewuxitongbianma1_611888_1841956546":"06401","extzuzhileixing_611888_2044304065":"14"},"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T12:25:36","modifiedBy":619169758,"modifiedTime":"2024-06-04T11:58:56","stdIsDeleted":false},{"name":"\u5b89\u4eb2\u8bfe\u8f85\u90e8","name_en_US":null,"name_zh_TW":null,"shortName":"\u5b89\u4eb2\u8bfe\u8f85\u90e8","shortName_en_US":null,"shortName_zh_TW":null,"code":"SRDQ1NC206406AQ","oId":1636300,"level":"14763d07-f648-45b1-96ac-182fdc4bb0b1","status":1,"establishDate":"1990-01-01T00:00:00","startDate":"2024-06-03T00:00:00","stopDate":"9999-12-31T00:00:00","changeDate":"2024-06-03T00:00:00","pOIdOrgAdmin":1636127,"pOIdOrgReserve2":null,"pOIdOrgReserve3":null,"isCurrentRecord":true,"personInCharge":null,"hRBP":null,"shopOwner":null,"administrativeAssistant":null,"personInChargeDeputy":null,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-03T12:25:36","legalMan":null,"address":null,"fax":null,"postcode":null,"phone":null,"url":null,"description":null,"number":null,"broadType":"2","economicType":null,"industry":null,"place":null,"orderAdmin":2,"orderReserve2":null,"orderReserve3":null,"comment":null,"oIdOrganizationType":null,"pOIdOrgAdmin_TreePath":"*********\/1640567\/1636044\/1636046\/1636056\/1636127\/1636300","pOIdOrgAdmin_TreeLevel":7,"pOIdOrgReserve2_TreePath":"*********\/0\/1636300","pOIdOrgReserve2_TreeLevel":3,"firstLevelOrganization":1640567,"secondLevelOrganization":1636044,"thirdLevelOrganization":1636046,"fourthLevelOrganization":1636056,"fifthLevelOrganization":1636127,"sixthLevelOrganization":1636300,"seventhLevelOrganization":null,"eighthLevelOrganization":null,"ninthLevelOrganization":null,"tenthLevelOrganization":null,"orderCode":null,"pOIdOrgAdminNameTreePath":"*********\/1640567\/1636044\/1636046\/1636056\/1636127\/1636300","isVirtualOrg":false,"leaderWithSpecificDuty":null,"objectId":"e4971f2b-7376-43e0-810e-c02a5b74de97","customProperties":{"extyewuxitongbianma1_611888_1841956546":"SRDQ1NC206406AQ","extzuzhileixing_611888_2044304065":"17"},"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T12:25:36","modifiedBy":*********,"modifiedTime":"2024-06-03T12:26:17","stdIsDeleted":false}]
                $rows = $items->toArray();
                foreach ($rows as $key=>$val) {

//                    if ($val['oId'] == '1640482') {
//                        echo json_encode($val) . "\n";
//                        exit;
//                    }


                    //
                    $tree = [];
                    $treePath = explode("/", $val['pOIdOrgAdmin_TreePath']);
                    foreach ($treePath as $currentOid) {
                        if ($currentOid == '*********') {
                            continue;
                        }
                        $tree[] = $bumenCollectByOid[$currentOid]['name'];
                    }

                    $changmingcheng = implode("_", $tree);

                    $rows[$key]['changmingcheng'] = $changmingcheng;

//                    if (isset($rows[$key]['customProperties'])) {
//                        echo json_encode($rows[$key]);
//                        echo "\n\n";
//                    }

                    if (isset($rows[$key]['customProperties']) && isset($rows[$key]['customProperties']['extyewuxitongbianma1_611888_1841956546']) ) {
                        $rows[$key]['extyewuxitongbianma'] = $rows[$key]['customProperties']['extyewuxitongbianma1_611888_1841956546'];
                    } else {
                        $rows[$key]['extyewuxitongbianma'] = '';
                    }
                    if (isset($rows[$key]['customProperties']) && isset($rows[$key]['customProperties']['extzuzhileixing_611888_2044304065']) ) {
                        $rows[$key]['extzuzhileixing'] = $rows[$key]['customProperties']['extzuzhileixing_611888_2044304065'];
                    } else {
                        $rows[$key]['extzuzhileixing'] = '';
                    }

                    unset($rows[$key]['customProperties']);
                    unset($rows[$key]['translateProperties']);
                    unset($rows[$key]['sysMartionProperties']);
                }

//                exit();

                BeisenBumen::upsert(
                    $rows,
                    ['objectId'],
                    $beisenBumenFillable
                );

//                DB::table('t_beisen_bumen')->insert($rows);
            });

            BeisenBumen::whereNotIn('objectId', $bumenCollect->pluck('objectId'))->update(["shanchu" => 1]);



            echo "部门完成\n";






            // 获取职务信息
            $search = [
                'withDisabled' => true,
                'timeWindowQueryType' => '1',
                'isWithDeleted' => true,
            ];
            $zhiwuList = $beisenApiService->getZhiwuAll($search);
            echo "部门总数:" . count($zhiwuList) . "\n";
            collect($zhiwuList)->chunk(300)->each(function($items, $key) use ($beisenZhiwuFillable) {


                $rows = $items->toArray();
                // [{"name":"\u540e\u52e4\u4e3b\u4efb","oId":"388267","code":"HQZR","establishDate":"1990-01-01T00:00:00","startDate":"1990-01-01T00:00:00","stopDate":"9999-12-31T00:00:00","status":1,"oIdResourceSet":null,"oIdJobGradeLow":null,"oIdJobGradeHigh":null,"oIdProfessionalLine":null,"oIdJobSequence":null,"oIdJobLevel":null,"highestOIdJobLevel":null,"oIdJobLevelType":null,"name_zh_TW":null,"name_en_US":null,"description":null,"description_zh_TW":null,"description_en_US":null,"oIdTalentCriterion":null,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-03T16:38:30","order":null,"score":null,"jobPostKey":null,"jobPostSecret":null,"jobRequirements":null,"stdSetID":null,"objectId":"ee07452c-e324-4119-ac76-77ecc39e94c0","customProperties":null,"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T14:35:50","modifiedBy":*********,"modifiedTime":"2024-06-03T16:38:30","stdIsDeleted":false},{"name":"\u4e13\u6848\u4e3b\u7ba1","oId":"388246","code":"ZAZG","establishDate":"1990-01-01T00:00:00","startDate":"1990-01-01T00:00:00","stopDate":"9999-12-31T00:00:00","status":1,"oIdResourceSet":null,"oIdJobGradeLow":null,"oIdJobGradeHigh":null,"oIdProfessionalLine":null,"oIdJobSequence":null,"oIdJobLevel":null,"highestOIdJobLevel":null,"oIdJobLevelType":null,"name_zh_TW":null,"name_en_US":null,"description":null,"description_zh_TW":null,"description_en_US":null,"oIdTalentCriterion":null,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-03T16:38:30","order":null,"score":null,"jobPostKey":null,"jobPostSecret":null,"jobRequirements":null,"stdSetID":null,"objectId":"30b4683c-a1bc-4aa6-a2b6-222b00ef03a4","customProperties":null,"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T14:35:50","modifiedBy":*********,"modifiedTime":"2024-06-03T16:38:30","stdIsDeleted":false},{"name":"\u526f\u8bfe\u957f","oId":"388234","code":"FKZ","establishDate":"1990-01-01T00:00:00","startDate":"1990-01-01T00:00:00","stopDate":"9999-12-31T00:00:00","status":1,"oIdResourceSet":null,"oIdJobGradeLow":null,"oIdJobGradeHigh":null,"oIdProfessionalLine":null,"oIdJobSequence":null,"oIdJobLevel":null,"highestOIdJobLevel":null,"oIdJobLevelType":null,"name_zh_TW":null,"name_en_US":null,"description":null,"description_zh_TW":null,"description_en_US":null,"oIdTalentCriterion":null,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-03T16:38:30","order":null,"score":null,"jobPostKey":null,"jobPostSecret":null,"jobRequirements":null,"stdSetID":null,"objectId":"8d194a7b-2aea-4896-9eb4-804a58b26c52","customProperties":null,"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T14:35:50","modifiedBy":*********,"modifiedTime":"2024-06-03T16:38:30","stdIsDeleted":false},{"name":"\u52a9\u7406","oId":"388222","code":"ZL","establishDate":"1990-01-01T00:00:00","startDate":"1990-01-01T00:00:00","stopDate":"9999-12-31T00:00:00","status":1,"oIdResourceSet":null,"oIdJobGradeLow":null,"oIdJobGradeHigh":null,"oIdProfessionalLine":null,"oIdJobSequence":null,"oIdJobLevel":null,"highestOIdJobLevel":null,"oIdJobLevelType":null,"name_zh_TW":null,"name_en_US":null,"description":null,"description_zh_TW":null,"description_en_US":null,"oIdTalentCriterion":null,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-03T16:38:30","order":null,"score":null,"jobPostKey":null,"jobPostSecret":null,"jobRequirements":null,"stdSetID":null,"objectId":"26dbdc77-3bd1-4431-8e30-51b381adf605","customProperties":null,"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T14:35:50","modifiedBy":*********,"modifiedTime":"2024-06-03T16:38:30","stdIsDeleted":false},{"name":"\u5458\u5de5\u5173\u7cfb\u4e3b\u7ba1","oId":"388228","code":"YGGXZG","establishDate":"1990-01-01T00:00:00","startDate":"1990-01-01T00:00:00","stopDate":"9999-12-31T00:00:00","status":1,"oIdResourceSet":null,"oIdJobGradeLow":null,"oIdJobGradeHigh":null,"oIdProfessionalLine":null,"oIdJobSequence":null,"oIdJobLevel":null,"highestOIdJobLevel":null,"oIdJobLevelType":null,"name_zh_TW":null,"name_en_US":null,"description":null,"description_zh_TW":null,"description_en_US":null,"oIdTalentCriterion":null,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-03T16:38:30","order":null,"score":null,"jobPostKey":null,"jobPostSecret":null,"jobRequirements":null,"stdSetID":null,"objectId":"bc96c1cf-a42e-48a2-8cd6-3c48369c68c0","customProperties":null,"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T14:35:50","modifiedBy":*********,"modifiedTime":"2024-06-03T16:38:30","stdIsDeleted":false},{"name":"\u987e\u95ee","oId":"388248","code":"GW","establishDate":"1990-01-01T00:00:00","startDate":"1990-01-01T00:00:00","stopDate":"9999-12-31T00:00:00","status":1,"oIdResourceSet":null,"oIdJobGradeLow":null,"oIdJobGradeHigh":null,"oIdProfessionalLine":null,"oIdJobSequence":null,"oIdJobLevel":null,"highestOIdJobLevel":null,"oIdJobLevelType":null,"name_zh_TW":null,"name_en_US":null,"description":null,"description_zh_TW":null,"description_en_US":null,"oIdTalentCriterion":null,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-03T16:38:30","order":null,"score":null,"jobPostKey":null,"jobPostSecret":null,"jobRequirements":null,"stdSetID":null,"objectId":"963e67bb-9037-4e72-849f-7f7e92b7e96e","customProperties":null,"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T14:35:50","modifiedBy":*********,"modifiedTime":"2024-06-03T16:38:30","stdIsDeleted":false},{"name":"\u6280\u672f\u7ec4\u957f","oId":"388217","code":"JSZZ","establishDate":"1990-01-01T00:00:00","startDate":"1990-01-01T00:00:00","stopDate":"9999-12-31T00:00:00","status":1,"oIdResourceSet":null,"oIdJobGradeLow":null,"oIdJobGradeHigh":null,"oIdProfessionalLine":null,"oIdJobSequence":null,"oIdJobLevel":null,"highestOIdJobLevel":null,"oIdJobLevelType":null,"name_zh_TW":null,"name_en_US":null,"description":null,"description_zh_TW":null,"description_en_US":null,"oIdTalentCriterion":null,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-03T16:38:30","order":null,"score":null,"jobPostKey":null,"jobPostSecret":null,"jobRequirements":null,"stdSetID":null,"objectId":"6fb3b294-8841-4341-952d-27643ceb64cc","customProperties":null,"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T14:35:50","modifiedBy":*********,"modifiedTime":"2024-06-03T16:38:30","stdIsDeleted":false},{"name":"\u4fdd\u80b2\u5458","oId":"388263","code":"TYB","establishDate":"1990-01-01T00:00:00","startDate":"1990-01-01T00:00:00","stopDate":"9999-12-31T00:00:00","status":1,"oIdResourceSet":null,"oIdJobGradeLow":null,"oIdJobGradeHigh":null,"oIdProfessionalLine":null,"oIdJobSequence":null,"oIdJobLevel":null,"highestOIdJobLevel":null,"oIdJobLevelType":null,"name_zh_TW":null,"name_en_US":null,"description":null,"description_zh_TW":null,"description_en_US":null,"oIdTalentCriterion":null,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-03T16:38:30","order":null,"score":null,"jobPostKey":null,"jobPostSecret":null,"jobRequirements":null,"stdSetID":null,"objectId":"1b326732-7272-4a45-8b7c-a131d95e2b43","customProperties":null,"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T14:35:50","modifiedBy":*********,"modifiedTime":"2024-06-03T16:38:30","stdIsDeleted":false},{"name":"\u4fdd\u6d01\u4eba\u5458","oId":"388265","code":"BJRY","establishDate":"1990-01-01T00:00:00","startDate":"1990-01-01T00:00:00","stopDate":"9999-12-31T00:00:00","status":1,"oIdResourceSet":null,"oIdJobGradeLow":null,"oIdJobGradeHigh":null,"oIdProfessionalLine":null,"oIdJobSequence":null,"oIdJobLevel":null,"highestOIdJobLevel":null,"oIdJobLevelType":null,"name_zh_TW":null,"name_en_US":null,"description":null,"description_zh_TW":null,"description_en_US":null,"oIdTalentCriterion":null,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-03T16:38:30","order":null,"score":null,"jobPostKey":null,"jobPostSecret":null,"jobRequirements":null,"stdSetID":null,"objectId":"e23fa60a-2b2e-459e-a206-7181df31456a","customProperties":null,"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T14:35:50","modifiedBy":*********,"modifiedTime":"2024-06-03T16:38:30","stdIsDeleted":false},{"name":"\u53f8\u673a","oId":"388262","code":"SJ","establishDate":"1990-01-01T00:00:00","startDate":"1990-01-01T00:00:00","stopDate":"9999-12-31T00:00:00","status":1,"oIdResourceSet":null,"oIdJobGradeLow":null,"oIdJobGradeHigh":null,"oIdProfessionalLine":null,"oIdJobSequence":null,"oIdJobLevel":null,"highestOIdJobLevel":null,"oIdJobLevelType":null,"name_zh_TW":null,"name_en_US":null,"description":null,"description_zh_TW":null,"description_en_US":null,"oIdTalentCriterion":null,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-03T16:38:30","order":null,"score":null,"jobPostKey":null,"jobPostSecret":null,"jobRequirements":null,"stdSetID":null,"objectId":"ca5c32f9-8f22-412b-9d93-68485867ae8a","customProperties":null,"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T14:35:50","modifiedBy":*********,"modifiedTime":"2024-06-03T16:38:30","stdIsDeleted":false}]
                foreach ($rows as $key=>$val) {
                    unset($rows[$key]['customProperties']);
                    unset($rows[$key]['translateProperties']);
                     unset($rows[$key]['sysMartionProperties']);
                }

                BeisenZhiwu::upsert(
                    $rows,
                    ['objectId'],
                    $beisenZhiwuFillable
                );

//                DB::table('t_beisen_zhiwu')->insert($rows);
            });

            echo "职务完成\n";





            $search = [
                'empStatus' =>  [1, 2, 3, 4, 5, 6,8,12],
                'employType' => [0,1,2],
                'serviceType' => [0, 1],


                'isGetLatestRecord' => false,

                'withDisabled' => true,
                'isWithDeleted' => true,
                'timeWindowQueryType' => 1,
//                'startTime' => '2024-07-01T12:00:00',
//                'stopTime' => '2024-07-02T00:00:00'

//                'extQueries' => [
//                    [
//                        'fieldName' => 'name',
//                        "queryType" => 'Equal', //等于
//                        "values" => [
//                            "胡锦添"
//                        ]
//                    ]
//                ]

            ];


            // 获取人员信息
            $yuangongList = $beisenApiService->getYuangongAll($search);
            echo "员工总数:" . count($yuangongList) . "\n";
            collect($yuangongList)->chunk(300)->each(function($items, $key) use ($beisenApiService, $beisenYuangongFillable, $beisenYuangongRenzhiFillable) {


                $rows = $items->toArray();


                // 获取员工任职全量信息
                $body = [
                    'oIds' => $items->pluck('employeeInfo')->pluck('userID')->toArray(),
                    'empStatus' =>  [1, 2, 3, 4, 5, 6,8,12],
                    'employType' => [0,1,2],
                    'serviceType' => [0, 1],
                    'isWithDeleted' => true,
                ];
                $jianzhiList = $beisenApiService->getRenzhiByIds($body);
                $jianzhiCollect = collect(($jianzhiList['data'] ?? []));
                $jianzhiCollectGroupByUserId = $jianzhiCollect->groupBy('userID');


                $list = [];
                // [{"originalId":"","employeeInfo":{"userID":619267393,"name":"\u9b4f\u8bd7\u5a77","_Name":"weishiting","phoneticOfXing":null,"phoneticOfMing":null,"gender":1,"email":"<EMAIL>","iDType":"1","iDNumber":"360122200408246349","timeZone":null,"isLongTermCertificate":null,"certificateStartDate":"0001-01-01T00:00:00","certificateValidityTerm":"0001-01-01T00:00:00","birthday":"2004-08-24T00:00:00","workDate":"2023-09-01T00:00:00","homeAddress":null,"mobilePhone":null,"weiXin":null,"iDPhoto":null,"smallIDPhoto":null,"iDPortraitSide":null,"iDCountryEmblemSide":null,"allowToLoginIn":null,"personalHomepage":null,"speciality":null,"major":null,"postalCode":null,"passportNumber":null,"constellation":null,"bloodType":null,"residenceAddress":null,"joinPartyDate":null,"domicileType":null,"emergencyContact":null,"emergencyContactRelationship":null,"emergencyContactPhone":null,"qQ":null,"aboutMe":null,"homePhone":null,"graduateDate":null,"marryCategory":null,"politicalStatus":null,"nationality":1,"nation":null,"birthplace":"\u6c5f\u897f\u7701\u5357\u660c\u5e02\u65b0\u5efa\u53bf","registAddress":"3601","educationLevel":null,"lastSchool":null,"engName":null,"firstname":null,"lastname":null,"officeTel":null,"businessAddress":null,"backupMail":null,"applicantId":null,"applyIdV6":null,"applicantIdV6":null,"age":19,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-07T14:23:08","sourceType":0,"firstEntryDate":"2023-09-01T00:00:00","latestEntryDate":"2023-09-01T00:00:00","tutorNew":null,"iDFront":null,"iDBehind":null,"preRetireDate":null,"actualRetireDate":null,"isConfirmRetireDate":null,"activationState":null,"issuingAuthority":null,"isDisabled":null,"disabledNumber":null,"orderCode":null,"objectId":"1eae039e-ddf7-42bf-93f1-28da5fd1d940","customProperties":null,"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T14:18:28","modifiedBy":*********,"modifiedTime":"2024-06-07T14:23:08","stdIsDeleted":false},"recordInfoList":[{"userID":619267393,"pObjectDataID":"1eae039e-ddf7-42bf-93f1-28da5fd1d940","oIdDepartment":1640435,"startDate":"2023-09-01T00:00:00","stopDate":"9999-12-31T00:00:00","jobNumber":"0680100017","entryDate":"2023-09-01T00:00:00","lastWorkDate":null,"regularizationDate":"2024-03-01T00:00:00","probation":6,"order":null,"employType":0,"serviceType":0,"serviceStatus":0,"approvalStatus":4,"employmentSource":null,"employmentForm":"0d2daf9f-1cba-4222-ab5f-316c20ed43bd","isCharge":"0","oIdJobPost":"388219","oIdJobSequence":null,"oIdProfessionalLine":null,"oIdJobPosition":null,"oIdJobLevel":null,"oidJobGrade":null,"place":null,"employeeStatus":"3","employmentType":null,"employmentChangeID":"06bc740a-f7b8-4907-95a0-de248db7ad4e","changedStatus":null,"pOIdEmpAdmin":*********,"pOIdEmpReserve2":null,"businessTypeOID":"1","changeTypeOID":"1","entryStatus":null,"isCurrentRecord":true,"lUOffer":null,"entryType":"1","staffID":"59ba6bcb-83d1-4316-820c-b0dbfac3b480","workYearBefore":null,"workYearGroupBefore":null,"workYearCompanyBefore":0,"workYearTotal":0.8,"workYearGroupTotal":null,"workYearCompanyTotal":0.8,"oIdOrganization":*********,"whereabouts":null,"blackStaffDesc":null,"blackListAddReason":null,"transitionTypeOID":null,"changeReason":null,"probationResult":"3","probationActualStopDate":"2024-02-29T00:00:00","probationStartDate":"2023-09-01T00:00:00","probationStopDate":"2024-02-29T00:00:00","isHaveProbation":"1","remarks":null,"addOrNotBlackList":null,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-12T15:14:34","traineeStartDate":null,"dimension1":null,"dimension2":null,"dimension3":null,"dimension4":null,"dimension5":null,"objectId":"47a7dda3-14ee-4226-9b92-f36dc198d9ce","customProperties":{"extanquanjibie_611888_1712193916":40},"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T14:18:29","modifiedBy":10000,"modifiedTime":"2024-06-19T02:49:13","stdIsDeleted":false}]},{"originalId":"","employeeInfo":{"userID":*********,"name":"\u9648\u5c0f\u6e05","_Name":"chenxiaoqing","phoneticOfXing":null,"phoneticOfMing":null,"gender":1,"email":"<EMAIL>","iDType":"1","iDNumber":"360103199007200329","timeZone":null,"isLongTermCertificate":null,"certificateStartDate":"0001-01-01T00:00:00","certificateValidityTerm":"0001-01-01T00:00:00","birthday":"1990-07-20T00:00:00","workDate":"2023-04-03T00:00:00","homeAddress":null,"mobilePhone":null,"weiXin":null,"iDPhoto":null,"smallIDPhoto":null,"iDPortraitSide":null,"iDCountryEmblemSide":null,"allowToLoginIn":null,"personalHomepage":null,"speciality":null,"major":null,"postalCode":null,"passportNumber":null,"constellation":null,"bloodType":null,"residenceAddress":null,"joinPartyDate":null,"domicileType":null,"emergencyContact":null,"emergencyContactRelationship":null,"emergencyContactPhone":null,"qQ":null,"aboutMe":null,"homePhone":null,"graduateDate":null,"marryCategory":null,"politicalStatus":null,"nationality":1,"nation":null,"birthplace":"\u6c5f\u897f\u7701\u5357\u660c\u5e02\u897f\u6e56\u533a","registAddress":"3601","educationLevel":null,"lastSchool":null,"engName":null,"firstname":null,"lastname":null,"officeTel":null,"businessAddress":null,"backupMail":null,"applicantId":null,"applyIdV6":null,"applicantIdV6":null,"age":33,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-07T14:48:31","sourceType":0,"firstEntryDate":"2023-04-03T00:00:00","latestEntryDate":"2023-04-03T00:00:00","tutorNew":null,"iDFront":null,"iDBehind":null,"preRetireDate":null,"actualRetireDate":null,"isConfirmRetireDate":null,"activationState":null,"issuingAuthority":null,"isDisabled":null,"disabledNumber":null,"orderCode":null,"objectId":"286201ea-c328-4bbe-b349-846469f5e3af","customProperties":null,"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T14:18:28","modifiedBy":*********,"modifiedTime":"2024-06-07T14:48:31","stdIsDeleted":false},"recordInfoList":[{"userID":*********,"pObjectDataID":"286201ea-c328-4bbe-b349-846469f5e3af","oIdDepartment":1640435,"startDate":"2023-04-03T00:00:00","stopDate":"9999-12-31T00:00:00","jobNumber":"0680100012","entryDate":"2023-04-03T00:00:00","lastWorkDate":null,"regularizationDate":null,"probation":null,"order":null,"employType":0,"serviceType":0,"serviceStatus":0,"approvalStatus":4,"employmentSource":null,"employmentForm":"0d2daf9f-1cba-4222-ab5f-316c20ed43bd","isCharge":"0","oIdJobPost":"388219","oIdJobSequence":null,"oIdProfessionalLine":null,"oIdJobPosition":null,"oIdJobLevel":null,"oidJobGrade":null,"place":null,"employeeStatus":"3","employmentType":null,"employmentChangeID":"46fbbdf3-7051-4eb9-b0b4-c35180b104cc","changedStatus":null,"pOIdEmpAdmin":*********,"pOIdEmpReserve2":null,"businessTypeOID":"1","changeTypeOID":"1","entryStatus":null,"isCurrentRecord":true,"lUOffer":null,"entryType":"1","staffID":"fe82a3bf-c0ca-4d57-bc3c-8233ff4a5029","workYearBefore":null,"workYearGroupBefore":null,"workYearCompanyBefore":0,"workYearTotal":1.2,"workYearGroupTotal":null,"workYearCompanyTotal":1.2,"oIdOrganization":*********,"whereabouts":null,"blackStaffDesc":null,"blackListAddReason":null,"transitionTypeOID":null,"changeReason":null,"probationResult":null,"probationActualStopDate":null,"probationStartDate":null,"probationStopDate":null,"isHaveProbation":"0","remarks":null,"addOrNotBlackList":null,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-07T14:48:30","traineeStartDate":null,"dimension1":null,"dimension2":null,"dimension3":null,"dimension4":null,"dimension5":null,"objectId":"b2e952bd-42ee-4e83-a9e3-d9de091abefb","customProperties":{"extanquanjibie_611888_1712193916":40},"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T14:18:29","modifiedBy":10000,"modifiedTime":"2024-06-15T02:45:10","stdIsDeleted":false}]},{"originalId":"","employeeInfo":{"userID":*********,"name":"\u59dc\u4e3d\u5e73","_Name":"jiangliping","phoneticOfXing":null,"phoneticOfMing":null,"gender":1,"email":"<EMAIL>","iDType":"1","iDNumber":"360121197410253969","timeZone":null,"isLongTermCertificate":null,"certificateStartDate":"0001-01-01T00:00:00","certificateValidityTerm":"0001-01-01T00:00:00","birthday":"1974-10-25T00:00:00","workDate":"2023-09-26T00:00:00","homeAddress":null,"mobilePhone":null,"weiXin":null,"iDPhoto":null,"smallIDPhoto":null,"iDPortraitSide":null,"iDCountryEmblemSide":null,"allowToLoginIn":null,"personalHomepage":null,"speciality":null,"major":null,"postalCode":null,"passportNumber":null,"constellation":null,"bloodType":null,"residenceAddress":null,"joinPartyDate":null,"domicileType":null,"emergencyContact":null,"emergencyContactRelationship":null,"emergencyContactPhone":null,"qQ":null,"aboutMe":null,"homePhone":null,"graduateDate":null,"marryCategory":null,"politicalStatus":null,"nationality":1,"nation":null,"birthplace":"\u6c5f\u897f\u7701\u5357\u660c\u5e02\u5357\u660c\u53bf","registAddress":"3601","educationLevel":null,"lastSchool":null,"engName":null,"firstname":null,"lastname":null,"officeTel":null,"businessAddress":null,"backupMail":null,"applicantId":null,"applyIdV6":null,"applicantIdV6":null,"age":49,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-07T14:50:29","sourceType":0,"firstEntryDate":"2023-09-26T00:00:00","latestEntryDate":"2023-09-26T00:00:00","tutorNew":null,"iDFront":null,"iDBehind":null,"preRetireDate":null,"actualRetireDate":null,"isConfirmRetireDate":null,"activationState":null,"issuingAuthority":null,"isDisabled":null,"disabledNumber":null,"orderCode":null,"objectId":"fc23c7c7-21ac-4d1f-b602-7462cd0a86a9","customProperties":null,"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T14:18:28","modifiedBy":*********,"modifiedTime":"2024-06-07T14:50:29","stdIsDeleted":false},"recordInfoList":[{"userID":*********,"pObjectDataID":"fc23c7c7-21ac-4d1f-b602-7462cd0a86a9","oIdDepartment":1640435,"startDate":"2023-09-26T00:00:00","stopDate":"9999-12-31T00:00:00","jobNumber":"0680100021","entryDate":"2023-09-26T00:00:00","lastWorkDate":null,"regularizationDate":"2024-03-26T00:00:00","probation":6,"order":null,"employType":0,"serviceType":0,"serviceStatus":0,"approvalStatus":4,"employmentSource":null,"employmentForm":"0d2daf9f-1cba-4222-ab5f-316c20ed43bd","isCharge":"0","oIdJobPost":"388215","oIdJobSequence":null,"oIdProfessionalLine":null,"oIdJobPosition":null,"oIdJobLevel":null,"oidJobGrade":null,"place":null,"employeeStatus":"3","employmentType":null,"employmentChangeID":"bdafbc55-4533-41f8-bd25-e7df32204531","changedStatus":null,"pOIdEmpAdmin":*********,"pOIdEmpReserve2":null,"businessTypeOID":"1","changeTypeOID":"1","entryStatus":null,"isCurrentRecord":true,"lUOffer":null,"entryType":"1","staffID":"1accdb8a-baf7-4fc5-8a5c-844b6900c9ef","workYearBefore":null,"workYearGroupBefore":null,"workYearCompanyBefore":0,"workYearTotal":0.7,"workYearGroupTotal":null,"workYearCompanyTotal":0.7,"oIdOrganization":*********,"whereabouts":null,"blackStaffDesc":null,"blackListAddReason":null,"transitionTypeOID":null,"changeReason":null,"probationResult":"3","probationActualStopDate":"2024-03-25T00:00:00","probationStartDate":"2023-09-26T00:00:00","probationStopDate":"2024-03-25T00:00:00","isHaveProbation":"1","remarks":null,"addOrNotBlackList":null,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-12T15:14:34","traineeStartDate":null,"dimension1":null,"dimension2":null,"dimension3":null,"dimension4":null,"dimension5":null,"objectId":"5148cb8c-2015-4fd7-8e8f-8420b7b5ddc9","customProperties":{"extanquanjibie_611888_1712193916":40},"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T14:18:29","modifiedBy":*********,"modifiedTime":"2024-06-12T15:14:34","stdIsDeleted":false}]},{"originalId":"","employeeInfo":{"userID":*********,"name":"\u7956\u5efa\u82f1","_Name":"zujianying","phoneticOfXing":null,"phoneticOfMing":null,"gender":1,"email":"<EMAIL>","iDType":"1","iDNumber":"360122199909020342","timeZone":null,"isLongTermCertificate":null,"certificateStartDate":"0001-01-01T00:00:00","certificateValidityTerm":"0001-01-01T00:00:00","birthday":"1999-09-02T00:00:00","workDate":"2023-08-02T00:00:00","homeAddress":null,"mobilePhone":null,"weiXin":null,"iDPhoto":null,"smallIDPhoto":null,"iDPortraitSide":null,"iDCountryEmblemSide":null,"allowToLoginIn":null,"personalHomepage":null,"speciality":null,"major":null,"postalCode":null,"passportNumber":null,"constellation":null,"bloodType":null,"residenceAddress":null,"joinPartyDate":null,"domicileType":null,"emergencyContact":null,"emergencyContactRelationship":null,"emergencyContactPhone":null,"qQ":null,"aboutMe":null,"homePhone":null,"graduateDate":null,"marryCategory":null,"politicalStatus":null,"nationality":1,"nation":null,"birthplace":"\u6c5f\u897f\u7701\u5357\u660c\u5e02\u65b0\u5efa\u53bf","registAddress":"3601","educationLevel":null,"lastSchool":null,"engName":null,"firstname":null,"lastname":null,"officeTel":null,"businessAddress":null,"backupMail":null,"applicantId":null,"applyIdV6":null,"applicantIdV6":null,"age":24,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-07T14:58:06","sourceType":0,"firstEntryDate":"2023-08-02T00:00:00","latestEntryDate":"2023-08-02T00:00:00","tutorNew":null,"iDFront":null,"iDBehind":null,"preRetireDate":null,"actualRetireDate":null,"isConfirmRetireDate":null,"activationState":null,"issuingAuthority":null,"isDisabled":null,"disabledNumber":null,"orderCode":null,"objectId":"8109be97-b3b0-4f2b-964d-8b4667f14b2e","customProperties":null,"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T14:18:28","modifiedBy":10000,"modifiedTime":"2024-07-01T03:29:42","stdIsDeleted":false},"recordInfoList":[{"userID":*********,"pObjectDataID":"8109be97-b3b0-4f2b-964d-8b4667f14b2e","oIdDepartment":1640435,"startDate":"2023-08-02T00:00:00","stopDate":"9999-12-31T00:00:00","jobNumber":"0680100015","entryDate":"2023-08-02T00:00:00","lastWorkDate":null,"regularizationDate":"2023-11-01T00:00:00","probation":6,"order":null,"employType":0,"serviceType":0,"serviceStatus":0,"approvalStatus":4,"employmentSource":null,"employmentForm":"7caba9fd-434b-42c4-a314-9c71a015f46a","isCharge":"0","oIdJobPost":"388215","oIdJobSequence":null,"oIdProfessionalLine":null,"oIdJobPosition":null,"oIdJobLevel":null,"oidJobGrade":null,"place":null,"employeeStatus":"3","employmentType":null,"employmentChangeID":"8c8b804a-8e9c-494d-bcff-fef8ac4cac07","changedStatus":null,"pOIdEmpAdmin":*********,"pOIdEmpReserve2":null,"businessTypeOID":"1","changeTypeOID":"1","entryStatus":null,"isCurrentRecord":true,"lUOffer":null,"entryType":"1","staffID":"5fdb3800-0a89-4f49-a93f-3972bc71a02c","workYearBefore":null,"workYearGroupBefore":null,"workYearCompanyBefore":0,"workYearTotal":0.9,"workYearGroupTotal":null,"workYearCompanyTotal":0.9,"oIdOrganization":*********,"whereabouts":null,"blackStaffDesc":null,"blackListAddReason":null,"transitionTypeOID":null,"changeReason":null,"probationResult":"25","probationActualStopDate":"2023-10-31T00:00:00","probationStartDate":"2023-08-02T00:00:00","probationStopDate":"2024-02-01T00:00:00","isHaveProbation":"1","remarks":null,"addOrNotBlackList":null,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-12T15:14:34","traineeStartDate":null,"dimension1":null,"dimension2":null,"dimension3":null,"dimension4":null,"dimension5":null,"objectId":"8f07880e-680d-4bc5-bb4e-68204bd342f8","customProperties":{"extanquanjibie_611888_1712193916":40},"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T14:18:29","modifiedBy":10000,"modifiedTime":"2024-06-26T02:46:29","stdIsDeleted":false}]},{"originalId":"","employeeInfo":{"userID":*********,"name":"\u738b\u6167\u8d24","_Name":"wanghuixian","phoneticOfXing":null,"phoneticOfMing":null,"gender":1,"email":"<EMAIL>","iDType":"1","iDNumber":"130535199908145128","timeZone":null,"isLongTermCertificate":null,"certificateStartDate":"0001-01-01T00:00:00","certificateValidityTerm":"0001-01-01T00:00:00","birthday":"1999-08-14T00:00:00","workDate":"2022-07-03T00:00:00","homeAddress":null,"mobilePhone":null,"weiXin":null,"iDPhoto":null,"smallIDPhoto":null,"iDPortraitSide":null,"iDCountryEmblemSide":null,"allowToLoginIn":null,"personalHomepage":null,"speciality":null,"major":null,"postalCode":null,"passportNumber":null,"constellation":null,"bloodType":null,"residenceAddress":null,"joinPartyDate":null,"domicileType":null,"emergencyContact":null,"emergencyContactRelationship":null,"emergencyContactPhone":null,"qQ":null,"aboutMe":null,"homePhone":null,"graduateDate":null,"marryCategory":null,"politicalStatus":null,"nationality":1,"nation":null,"birthplace":"\u6cb3\u5317\u7701\u90a2\u53f0\u5e02\u4e34\u897f\u53bf","registAddress":"1305","educationLevel":null,"lastSchool":null,"engName":null,"firstname":null,"lastname":null,"officeTel":null,"businessAddress":null,"backupMail":null,"applicantId":null,"applyIdV6":null,"applicantIdV6":null,"age":24,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-07T14:50:29","sourceType":0,"firstEntryDate":"2022-07-03T00:00:00","latestEntryDate":"2022-07-03T00:00:00","tutorNew":null,"iDFront":null,"iDBehind":null,"preRetireDate":null,"actualRetireDate":null,"isConfirmRetireDate":null,"activationState":null,"issuingAuthority":null,"isDisabled":null,"disabledNumber":null,"orderCode":null,"objectId":"c767b9c7-bcef-4f37-b104-7e5924a9b590","customProperties":null,"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T14:18:28","modifiedBy":*********,"modifiedTime":"2024-06-07T14:50:29","stdIsDeleted":false},"recordInfoList":[{"userID":*********,"pObjectDataID":"c767b9c7-bcef-4f37-b104-7e5924a9b590","oIdDepartment":1640435,"startDate":"2022-07-03T00:00:00","stopDate":"9999-12-31T00:00:00","jobNumber":"0680100003","entryDate":"2022-07-03T00:00:00","lastWorkDate":null,"regularizationDate":"2023-01-03T00:00:00","probation":6,"order":null,"employType":0,"serviceType":0,"serviceStatus":0,"approvalStatus":4,"employmentSource":null,"employmentForm":"0d2daf9f-1cba-4222-ab5f-316c20ed43bd","isCharge":"0","oIdJobPost":"388215","oIdJobSequence":null,"oIdProfessionalLine":null,"oIdJobPosition":null,"oIdJobLevel":null,"oidJobGrade":null,"place":null,"employeeStatus":"3","employmentType":null,"employmentChangeID":"c67bc072-9650-4bfd-b696-07ee32a19977","changedStatus":null,"pOIdEmpAdmin":*********,"pOIdEmpReserve2":null,"businessTypeOID":"1","changeTypeOID":"1","entryStatus":null,"isCurrentRecord":true,"lUOffer":null,"entryType":"1","staffID":"5930fce0-f6c8-441b-ae3c-c081ae22f4bb","workYearBefore":null,"workYearGroupBefore":null,"workYearCompanyBefore":0,"workYearTotal":1.9,"workYearGroupTotal":null,"workYearCompanyTotal":1.9,"oIdOrganization":*********,"whereabouts":null,"blackStaffDesc":null,"blackListAddReason":null,"transitionTypeOID":null,"changeReason":null,"probationResult":"3","probationActualStopDate":"2023-01-02T00:00:00","probationStartDate":"2022-07-03T00:00:00","probationStopDate":"2023-01-02T00:00:00","isHaveProbation":"1","remarks":null,"addOrNotBlackList":null,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-12T15:14:33","traineeStartDate":null,"dimension1":null,"dimension2":null,"dimension3":null,"dimension4":null,"dimension5":null,"objectId":"5e73e031-9278-4726-88b9-32a6fbb36779","customProperties":{"extanquanjibie_611888_1712193916":40},"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T14:18:29","modifiedBy":*********,"modifiedTime":"2024-06-12T15:14:33","stdIsDeleted":false}]},{"originalId":"","employeeInfo":{"userID":*********,"name":"\u989c\u8d85","_Name":"yanchao","phoneticOfXing":null,"phoneticOfMing":null,"gender":0,"email":"<EMAIL>","iDType":"1","iDNumber":"362202199412110037","timeZone":null,"isLongTermCertificate":null,"certificateStartDate":"0001-01-01T00:00:00","certificateValidityTerm":"0001-01-01T00:00:00","birthday":"1994-12-11T00:00:00","workDate":"2024-03-17T00:00:00","homeAddress":null,"mobilePhone":null,"weiXin":null,"iDPhoto":null,"smallIDPhoto":null,"iDPortraitSide":null,"iDCountryEmblemSide":null,"allowToLoginIn":null,"personalHomepage":null,"speciality":null,"major":null,"postalCode":null,"passportNumber":null,"constellation":null,"bloodType":null,"residenceAddress":null,"joinPartyDate":null,"domicileType":null,"emergencyContact":null,"emergencyContactRelationship":null,"emergencyContactPhone":null,"qQ":null,"aboutMe":null,"homePhone":null,"graduateDate":null,"marryCategory":null,"politicalStatus":null,"nationality":1,"nation":null,"birthplace":"\u6c5f\u897f\u7701\u5b9c\u6625\u5730\u533a\u4e30\u57ce\u5e02","registAddress":"3600","educationLevel":null,"lastSchool":null,"engName":null,"firstname":null,"lastname":null,"officeTel":null,"businessAddress":null,"backupMail":null,"applicantId":null,"applyIdV6":null,"applicantIdV6":null,"age":29,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-07T14:46:33","sourceType":0,"firstEntryDate":"2024-03-17T00:00:00","latestEntryDate":"2024-03-17T00:00:00","tutorNew":null,"iDFront":null,"iDBehind":null,"preRetireDate":null,"actualRetireDate":null,"isConfirmRetireDate":null,"activationState":null,"issuingAuthority":null,"isDisabled":null,"disabledNumber":null,"orderCode":null,"objectId":"799f7d4c-40af-4e6f-90d8-43a518c0f856","customProperties":null,"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T14:18:28","modifiedBy":*********,"modifiedTime":"2024-06-07T14:46:33","stdIsDeleted":false},"recordInfoList":[{"userID":*********,"pObjectDataID":"799f7d4c-40af-4e6f-90d8-43a518c0f856","oIdDepartment":1640434,"startDate":"2024-03-17T00:00:00","stopDate":"9999-12-31T00:00:00","jobNumber":"0680100022","entryDate":"2024-03-17T00:00:00","lastWorkDate":null,"regularizationDate":null,"probation":6,"order":null,"employType":0,"serviceType":0,"serviceStatus":0,"approvalStatus":4,"employmentSource":null,"employmentForm":"0d2daf9f-1cba-4222-ab5f-316c20ed43bd","isCharge":"0","oIdJobPost":"388217","oIdJobSequence":null,"oIdProfessionalLine":null,"oIdJobPosition":null,"oIdJobLevel":null,"oidJobGrade":null,"place":null,"employeeStatus":"2","employmentType":null,"employmentChangeID":"9595e99d-fd4a-4b1f-aff2-d8de57408267","changedStatus":null,"pOIdEmpAdmin":*********,"pOIdEmpReserve2":null,"businessTypeOID":"1","changeTypeOID":"1","entryStatus":null,"isCurrentRecord":true,"lUOffer":null,"entryType":"1","staffID":"4ef86e82-9611-4dfa-a285-6128242f8be0","workYearBefore":null,"workYearGroupBefore":null,"workYearCompanyBefore":0,"workYearTotal":0.2,"workYearGroupTotal":null,"workYearCompanyTotal":0.2,"oIdOrganization":*********,"whereabouts":null,"blackStaffDesc":null,"blackListAddReason":null,"transitionTypeOID":null,"changeReason":null,"probationResult":null,"probationActualStopDate":null,"probationStartDate":"2024-03-17T00:00:00","probationStopDate":"2024-09-16T00:00:00","isHaveProbation":"1","remarks":null,"addOrNotBlackList":null,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-12T15:21:39","traineeStartDate":null,"dimension1":null,"dimension2":null,"dimension3":null,"dimension4":null,"dimension5":null,"objectId":"98272a3c-1980-49bd-8c23-42d03ce83698","customProperties":{"extanquanjibie_611888_1712193916":40},"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T14:18:29","modifiedBy":*********,"modifiedTime":"2024-06-12T15:21:39","stdIsDeleted":false}]},{"originalId":"","employeeInfo":{"userID":*********,"name":"\u674e\u656c\u5143","_Name":"lijingyuan","phoneticOfXing":null,"phoneticOfMing":null,"gender":0,"email":"<EMAIL>","iDType":"1","iDNumber":"310101198811101552","timeZone":null,"isLongTermCertificate":null,"certificateStartDate":"0001-01-01T00:00:00","certificateValidityTerm":"0001-01-01T00:00:00","birthday":"1988-11-10T00:00:00","workDate":"2024-04-17T00:00:00","homeAddress":null,"mobilePhone":null,"weiXin":null,"iDPhoto":null,"smallIDPhoto":null,"iDPortraitSide":null,"iDCountryEmblemSide":null,"allowToLoginIn":null,"personalHomepage":null,"speciality":null,"major":null,"postalCode":null,"passportNumber":null,"constellation":null,"bloodType":null,"residenceAddress":null,"joinPartyDate":null,"domicileType":null,"emergencyContact":null,"emergencyContactRelationship":null,"emergencyContactPhone":null,"qQ":null,"aboutMe":null,"homePhone":null,"graduateDate":null,"marryCategory":null,"politicalStatus":null,"nationality":1,"nation":null,"birthplace":"\u4e0a\u6d77\u5e02\u9ec4\u6d66\u533a","registAddress":"3100","educationLevel":null,"lastSchool":null,"engName":null,"firstname":null,"lastname":null,"officeTel":null,"businessAddress":null,"backupMail":null,"applicantId":null,"applyIdV6":null,"applicantIdV6":null,"age":35,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-07T15:02:18","sourceType":0,"firstEntryDate":"2024-04-17T00:00:00","latestEntryDate":"2024-04-17T00:00:00","tutorNew":null,"iDFront":null,"iDBehind":null,"preRetireDate":null,"actualRetireDate":null,"isConfirmRetireDate":null,"activationState":null,"issuingAuthority":null,"isDisabled":null,"disabledNumber":null,"orderCode":null,"objectId":"3f478d2b-20f0-4f93-985e-661980c7e934","customProperties":null,"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T14:18:28","modifiedBy":*********,"modifiedTime":"2024-06-07T15:02:18","stdIsDeleted":false},"recordInfoList":[{"userID":*********,"pObjectDataID":"3f478d2b-20f0-4f93-985e-661980c7e934","oIdDepartment":1640447,"startDate":"2024-04-17T00:00:00","stopDate":"9999-12-31T00:00:00","jobNumber":"0140013993","entryDate":"2024-04-17T00:00:00","lastWorkDate":null,"regularizationDate":null,"probation":6,"order":null,"employType":0,"serviceType":0,"serviceStatus":0,"approvalStatus":4,"employmentSource":null,"employmentForm":"0d2daf9f-1cba-4222-ab5f-316c20ed43bd","isCharge":"0","oIdJobPost":null,"oIdJobSequence":null,"oIdProfessionalLine":null,"oIdJobPosition":null,"oIdJobLevel":null,"oidJobGrade":null,"place":null,"employeeStatus":"2","employmentType":null,"employmentChangeID":"5d3fb35a-6a79-43f2-bcbc-9dd8d61a3380","changedStatus":null,"pOIdEmpAdmin":*********,"pOIdEmpReserve2":null,"businessTypeOID":"1","changeTypeOID":"1","entryStatus":null,"isCurrentRecord":true,"lUOffer":null,"entryType":"1","staffID":"a679c054-e0b9-464d-a972-d1921495c841","workYearBefore":null,"workYearGroupBefore":null,"workYearCompanyBefore":0,"workYearTotal":0.2,"workYearGroupTotal":null,"workYearCompanyTotal":0.2,"oIdOrganization":*********,"whereabouts":null,"blackStaffDesc":null,"blackListAddReason":null,"transitionTypeOID":null,"changeReason":null,"probationResult":null,"probationActualStopDate":null,"probationStartDate":"2024-04-17T00:00:00","probationStopDate":"2024-10-16T00:00:00","isHaveProbation":"1","remarks":null,"addOrNotBlackList":null,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-12T15:21:39","traineeStartDate":null,"dimension1":null,"dimension2":null,"dimension3":null,"dimension4":null,"dimension5":null,"objectId":"c6419a3d-fd15-4815-93df-86ad790e9827","customProperties":{"extanquanjibie_611888_1712193916":40},"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T14:18:29","modifiedBy":10000,"modifiedTime":"2024-06-29T02:45:06","stdIsDeleted":false}]},{"originalId":"","employeeInfo":{"userID":*********,"name":"\u90ed\u7d2b\u6b23","_Name":"guozixin","phoneticOfXing":null,"phoneticOfMing":null,"gender":1,"email":"<EMAIL>","iDType":"1","iDNumber":"362421200006143226","timeZone":null,"isLongTermCertificate":null,"certificateStartDate":"0001-01-01T00:00:00","certificateValidityTerm":"0001-01-01T00:00:00","birthday":"2000-06-14T00:00:00","workDate":"2023-11-02T00:00:00","homeAddress":null,"mobilePhone":null,"weiXin":null,"iDPhoto":null,"smallIDPhoto":null,"iDPortraitSide":null,"iDCountryEmblemSide":null,"allowToLoginIn":null,"personalHomepage":null,"speciality":null,"major":null,"postalCode":null,"passportNumber":null,"constellation":null,"bloodType":null,"residenceAddress":null,"joinPartyDate":null,"domicileType":null,"emergencyContact":null,"emergencyContactRelationship":null,"emergencyContactPhone":null,"qQ":null,"aboutMe":null,"homePhone":null,"graduateDate":null,"marryCategory":null,"politicalStatus":null,"nationality":1,"nation":null,"birthplace":"\u6c5f\u897f\u7701\u5409\u5b89\u5730\u533a\u5409\u5b89\u53bf","registAddress":"3600","educationLevel":null,"lastSchool":null,"engName":null,"firstname":null,"lastname":null,"officeTel":null,"businessAddress":null,"backupMail":null,"applicantId":null,"applyIdV6":null,"applicantIdV6":null,"age":24,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-07T14:36:45","sourceType":0,"firstEntryDate":"2023-11-02T00:00:00","latestEntryDate":"2023-11-02T00:00:00","tutorNew":null,"iDFront":null,"iDBehind":null,"preRetireDate":null,"actualRetireDate":null,"isConfirmRetireDate":null,"activationState":null,"issuingAuthority":null,"isDisabled":null,"disabledNumber":null,"orderCode":null,"objectId":"34eb3288-e689-426f-82c6-029bbf689e45","customProperties":null,"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T14:18:28","modifiedBy":10000,"modifiedTime":"2024-06-14T02:44:29","stdIsDeleted":false},"recordInfoList":[{"userID":*********,"pObjectDataID":"34eb3288-e689-426f-82c6-029bbf689e45","oIdDepartment":1640440,"startDate":"2023-11-02T00:00:00","stopDate":"9999-12-31T00:00:00","jobNumber":"0680200031","entryDate":"2023-11-02T00:00:00","lastWorkDate":null,"regularizationDate":"2024-05-02T00:00:00","probation":6,"order":null,"employType":0,"serviceType":0,"serviceStatus":0,"approvalStatus":4,"employmentSource":null,"employmentForm":"0d2daf9f-1cba-4222-ab5f-316c20ed43bd","isCharge":"0","oIdJobPost":"388219","oIdJobSequence":null,"oIdProfessionalLine":null,"oIdJobPosition":null,"oIdJobLevel":null,"oidJobGrade":null,"place":null,"employeeStatus":"3","employmentType":null,"employmentChangeID":"24aa9528-e076-4f5b-9d48-99b5b547fa94","changedStatus":null,"pOIdEmpAdmin":*********,"pOIdEmpReserve2":null,"businessTypeOID":"1","changeTypeOID":"1","entryStatus":null,"isCurrentRecord":true,"lUOffer":null,"entryType":"1","staffID":"fa0be9f1-75bd-4351-8404-26ce5afeee58","workYearBefore":null,"workYearGroupBefore":null,"workYearCompanyBefore":0,"workYearTotal":0.6,"workYearGroupTotal":null,"workYearCompanyTotal":0.6,"oIdOrganization":*********,"whereabouts":null,"blackStaffDesc":null,"blackListAddReason":null,"transitionTypeOID":null,"changeReason":null,"probationResult":"3","probationActualStopDate":"2024-05-01T00:00:00","probationStartDate":"2023-11-02T00:00:00","probationStopDate":"2024-05-01T00:00:00","isHaveProbation":"1","remarks":null,"addOrNotBlackList":null,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-12T15:14:33","traineeStartDate":null,"dimension1":null,"dimension2":null,"dimension3":null,"dimension4":null,"dimension5":null,"objectId":"8628ea64-2c33-47a8-847b-f4013635c83d","customProperties":{"extanquanjibie_611888_1712193916":40},"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T14:18:29","modifiedBy":*********,"modifiedTime":"2024-06-12T15:14:33","stdIsDeleted":false}]},{"originalId":"","employeeInfo":{"userID":*********,"name":"\u6768\u8000\u82b3","_Name":"yangyaofang","phoneticOfXing":null,"phoneticOfMing":null,"gender":1,"email":"<EMAIL>","iDType":"1","iDNumber":"43122119861124382X","timeZone":null,"isLongTermCertificate":null,"certificateStartDate":"0001-01-01T00:00:00","certificateValidityTerm":"0001-01-01T00:00:00","birthday":"1986-11-24T00:00:00","workDate":"2022-05-17T00:00:00","homeAddress":null,"mobilePhone":null,"weiXin":null,"iDPhoto":null,"smallIDPhoto":null,"iDPortraitSide":null,"iDCountryEmblemSide":null,"allowToLoginIn":null,"personalHomepage":null,"speciality":null,"major":null,"postalCode":null,"passportNumber":null,"constellation":null,"bloodType":null,"residenceAddress":null,"joinPartyDate":null,"domicileType":null,"emergencyContact":null,"emergencyContactRelationship":null,"emergencyContactPhone":null,"qQ":null,"aboutMe":null,"homePhone":null,"graduateDate":null,"marryCategory":null,"politicalStatus":null,"nationality":1,"nation":null,"birthplace":"\u6e56\u5357\u7701\u6000\u5316\u5e02\u4e2d\u65b9\u53bf","registAddress":"4312","educationLevel":null,"lastSchool":null,"engName":null,"firstname":null,"lastname":null,"officeTel":null,"businessAddress":null,"backupMail":null,"applicantId":null,"applyIdV6":null,"applicantIdV6":null,"age":37,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-07T15:02:18","sourceType":0,"firstEntryDate":"2022-05-17T00:00:00","latestEntryDate":"2022-05-17T00:00:00","tutorNew":null,"iDFront":null,"iDBehind":null,"preRetireDate":null,"actualRetireDate":null,"isConfirmRetireDate":null,"activationState":null,"issuingAuthority":null,"isDisabled":null,"disabledNumber":null,"orderCode":null,"objectId":"1a106711-7c5d-4e7c-ad7c-b013ace53871","customProperties":null,"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T14:18:28","modifiedBy":*********,"modifiedTime":"2024-06-07T15:02:18","stdIsDeleted":false},"recordInfoList":[{"userID":*********,"pObjectDataID":"1a106711-7c5d-4e7c-ad7c-b013ace53871","oIdDepartment":1640440,"startDate":"2022-05-17T00:00:00","stopDate":"9999-12-31T00:00:00","jobNumber":"0680200015","entryDate":"2022-05-17T00:00:00","lastWorkDate":null,"regularizationDate":"2022-11-17T00:00:00","probation":6,"order":null,"employType":0,"serviceType":0,"serviceStatus":0,"approvalStatus":4,"employmentSource":null,"employmentForm":"0d2daf9f-1cba-4222-ab5f-316c20ed43bd","isCharge":"0","oIdJobPost":"388219","oIdJobSequence":null,"oIdProfessionalLine":null,"oIdJobPosition":null,"oIdJobLevel":null,"oidJobGrade":null,"place":null,"employeeStatus":"3","employmentType":null,"employmentChangeID":"072632bd-d7aa-452c-92e2-89d19661a3da","changedStatus":null,"pOIdEmpAdmin":*********,"pOIdEmpReserve2":null,"businessTypeOID":"1","changeTypeOID":"1","entryStatus":null,"isCurrentRecord":true,"lUOffer":null,"entryType":"1","staffID":"569cc4d3-a7e8-42f1-b8d0-ee2a647977a7","workYearBefore":null,"workYearGroupBefore":null,"workYearCompanyBefore":0,"workYearTotal":2.1,"workYearGroupTotal":null,"workYearCompanyTotal":2.1,"oIdOrganization":*********,"whereabouts":null,"blackStaffDesc":null,"blackListAddReason":null,"transitionTypeOID":null,"changeReason":null,"probationResult":"3","probationActualStopDate":"2022-11-16T00:00:00","probationStartDate":"2022-05-17T00:00:00","probationStopDate":"2022-11-16T00:00:00","isHaveProbation":"1","remarks":null,"addOrNotBlackList":null,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-12T15:14:37","traineeStartDate":null,"dimension1":null,"dimension2":null,"dimension3":null,"dimension4":null,"dimension5":null,"objectId":"5b2a2f29-c901-46ba-ad13-fa49c3b0d118","customProperties":{"extanquanjibie_611888_1712193916":40},"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T14:18:29","modifiedBy":10000,"modifiedTime":"2024-06-23T02:44:47","stdIsDeleted":false}]},{"originalId":"","employeeInfo":{"userID":*********,"name":"\u6731\u4e3d\u534e","_Name":"zhulihua","phoneticOfXing":null,"phoneticOfMing":null,"gender":1,"email":"<EMAIL>","iDType":"1","iDNumber":"350301198802271849","timeZone":null,"isLongTermCertificate":null,"certificateStartDate":"0001-01-01T00:00:00","certificateValidityTerm":"0001-01-01T00:00:00","birthday":"1988-02-27T00:00:00","workDate":"2022-04-18T00:00:00","homeAddress":null,"mobilePhone":null,"weiXin":null,"iDPhoto":null,"smallIDPhoto":null,"iDPortraitSide":null,"iDCountryEmblemSide":null,"allowToLoginIn":null,"personalHomepage":null,"speciality":null,"major":null,"postalCode":null,"passportNumber":null,"constellation":null,"bloodType":null,"residenceAddress":null,"joinPartyDate":null,"domicileType":null,"emergencyContact":null,"emergencyContactRelationship":null,"emergencyContactPhone":null,"qQ":null,"aboutMe":null,"homePhone":null,"graduateDate":null,"marryCategory":null,"politicalStatus":null,"nationality":1,"nation":null,"birthplace":null,"registAddress":"3503","educationLevel":null,"lastSchool":null,"engName":null,"firstname":null,"lastname":null,"officeTel":null,"businessAddress":null,"backupMail":null,"applicantId":null,"applyIdV6":null,"applicantIdV6":null,"age":36,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-07T14:21:09","sourceType":0,"firstEntryDate":"2022-04-18T00:00:00","latestEntryDate":"2022-04-18T00:00:00","tutorNew":null,"iDFront":null,"iDBehind":null,"preRetireDate":null,"actualRetireDate":null,"isConfirmRetireDate":null,"activationState":null,"issuingAuthority":null,"isDisabled":null,"disabledNumber":null,"orderCode":null,"objectId":"10a5782e-67c5-4fc2-8767-9d353fdaa3be","customProperties":null,"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T14:18:28","modifiedBy":*********,"modifiedTime":"2024-06-07T14:21:09","stdIsDeleted":false},"recordInfoList":[{"userID":*********,"pObjectDataID":"10a5782e-67c5-4fc2-8767-9d353fdaa3be","oIdDepartment":1640440,"startDate":"2022-04-18T00:00:00","stopDate":"9999-12-31T00:00:00","jobNumber":"0680200007","entryDate":"2022-04-18T00:00:00","lastWorkDate":null,"regularizationDate":"2022-12-01T00:00:00","probation":6,"order":null,"employType":0,"serviceType":0,"serviceStatus":0,"approvalStatus":4,"employmentSource":null,"employmentForm":"7caba9fd-434b-42c4-a314-9c71a015f46a","isCharge":"0","oIdJobPost":"388216","oIdJobSequence":null,"oIdProfessionalLine":null,"oIdJobPosition":null,"oIdJobLevel":null,"oidJobGrade":null,"place":null,"employeeStatus":"3","employmentType":null,"employmentChangeID":"b056d5bc-a999-471a-8fb7-ddbda1daf27f","changedStatus":null,"pOIdEmpAdmin":*********,"pOIdEmpReserve2":null,"businessTypeOID":"1","changeTypeOID":"1","entryStatus":null,"isCurrentRecord":true,"lUOffer":null,"entryType":"1","staffID":"b29e2ba8-303b-43e8-a70f-71b91063e925","workYearBefore":null,"workYearGroupBefore":null,"workYearCompanyBefore":0,"workYearTotal":2.2,"workYearGroupTotal":null,"workYearCompanyTotal":2.2,"oIdOrganization":*********,"whereabouts":null,"blackStaffDesc":null,"blackListAddReason":null,"transitionTypeOID":null,"changeReason":null,"probationResult":"26","probationActualStopDate":"2022-11-30T00:00:00","probationStartDate":"2022-04-18T00:00:00","probationStopDate":"2022-10-17T00:00:00","isHaveProbation":"1","remarks":null,"addOrNotBlackList":null,"businessModifiedBy":*********,"businessModifiedTime":"2024-06-12T15:14:37","traineeStartDate":null,"dimension1":null,"dimension2":null,"dimension3":null,"dimension4":null,"dimension5":null,"objectId":"aac83d28-9bbe-40bd-baa1-9c50495c51e0","customProperties":{"extanquanjibie_611888_1712193916":40},"translateProperties":null,"sysMartionProperties":null,"createdBy":*********,"createdTime":"2024-06-03T14:18:29","modifiedBy":10000,"modifiedTime":"2024-06-30T02:45:03","stdIsDeleted":false}]}]

                // 员工主数据
                foreach ($rows as $row) {

//                    if ($row['employeeInfo']['name'] == '测试001' || $row['employeeInfo']['name'] == '测试002' || $row['employeeInfo']['name'] == '梅媛') {
//                        echo json_encode($row);
//                        echo "\n\n";
//                    }

                    $employeeInfo = $row['employeeInfo'];
                    $employeeInfo['originalId'] = $row['originalId'];

                    if (isset($employeeInfo['sysMartionProperties'])
                        && isset($employeeInfo['sysMartionProperties']['Name_en_US'])) {
                        $employeeInfo['Name_en_US'] = $employeeInfo['sysMartionProperties']['Name_en_US'];
                    } else {
                        $employeeInfo['Name_en_US'] = '';
                    }

                    unset($employeeInfo['customProperties']);
                    unset($employeeInfo['translateProperties']);
                    unset($employeeInfo['sysMartionProperties']);
                    unset($employeeInfo['mobilePhoneBackup']);
                    //unset($employeeInfo['givenNames']);

                    $employeeFile = array_keys($employeeInfo);
                    foreach ($employeeFile as $file){
                        if(!in_array($file,$beisenYuangongFillable)){
                            unset($employeeInfo[$file]);
                        }
                    }

                    $list[] = $employeeInfo;
                }

                BeisenYuangong::upsert(
                    $list,
                    ['objectId'],
                    $beisenYuangongFillable
                );

//                DB::table('t_beisen_yuangong')->insert($list);

                // 员工任职数据
                // recordInfoList/customProperties/extanquanjibie_611888_1712193916
                // recordInfoList/customProperties/extanquanjibienew_611888_633664368
                foreach ($rows as $row) {

                    if (isset($jianzhiCollectGroupByUserId[$row['employeeInfo']['userID']])) {

                        foreach ($jianzhiCollectGroupByUserId[$row['employeeInfo']['userID']] as $renzhiRow) {
                            $renzhiRow['originalId'] = $row['originalId'];

                            if (isset($renzhiRow['customProperties']) && isset($renzhiRow['customProperties']['extanquanjibienew_611888_633664368']) ) {
                                $renzhiRow['extanquanjibie'] = $renzhiRow['customProperties']['extanquanjibienew_611888_633664368'];
                            } else {
                                $renzhiRow['extanquanjibie'] = '';
                            }



                            if (isset($renzhiRow['customProperties']) && isset($renzhiRow['customProperties']['extrenzhijilu_611888_524808746']) ) {
                                $renzhiRow['feiyongchengdanzuzhi'] = $renzhiRow['customProperties']['extrenzhijilu_611888_524808746'];
                            } else {
                                $renzhiRow['feiyongchengdanzuzhi'] = '';
                            }


                            unset($renzhiRow['customProperties']);
                            unset($renzhiRow['translateProperties']);
                            unset($renzhiRow['sysMartionProperties']);
                            unset($renzhiRow['continuousContractStartDate']);
                            unset($renzhiRow['continuousContractWeeks']);
                            unset($renzhiRow['isContinuousContract']);


                            $renzhiFile = array_keys($renzhiRow);
                            foreach ($renzhiFile as $file){
                                if(!in_array($file,$beisenYuangongRenzhiFillable)){
                                    unset($renzhiRow[$file]);
                                }
                            }


                            BeisenYuangongRenzhi::upsert(
                                $renzhiRow,
                                ['pObjectDataID', 'objectId'],
                                $beisenYuangongRenzhiFillable
                            );
                        }
                    }



//                    if (isset($row['recordInfoList']) && !empty($row['recordInfoList'])) {
//                        foreach ($row['recordInfoList'] as $renzhiRow) {
//
////                            echo "\n";
////                            echo json_encode($renzhiRow);
////                            echo "\n";
//
//                            $renzhiRow['originalId'] = $row['originalId'];
//
//                            if (isset($renzhiRow['customProperties']) && isset($renzhiRow['customProperties']['extanquanjibienew_611888_633664368']) ) {
//                                $renzhiRow['extanquanjibie'] = $renzhiRow['customProperties']['extanquanjibienew_611888_633664368'];
//                            } else {
//                                $renzhiRow['extanquanjibie'] = '';
//                            }
//
//
//
//                            if (isset($renzhiRow['customProperties']) && isset($renzhiRow['customProperties']['extrenzhijilu_611888_524808746']) ) {
//                                $renzhiRow['feiyongchengdanzuzhi'] = $renzhiRow['customProperties']['extrenzhijilu_611888_524808746'];
//                            } else {
//                                $renzhiRow['feiyongchengdanzuzhi'] = '';
//                            }
//
//
//
//                            unset($renzhiRow['customProperties']);
//                            unset($renzhiRow['translateProperties']);
//                            unset($renzhiRow['sysMartionProperties']);
//
//                            BeisenYuangongRenzhi::upsert(
//                                $renzhiRow,
//                                ['pObjectDataID', 'objectId'],
//                                $beisenYuangongRenzhiFillable
//                            );
//
////                            DB::table('t_beisen_yuangong_renzhi')->insert($renzhiRow);
//
//                        }
//
//
//                    }




                }

            });

        } catch (Exception $e) {

            echo json_encode(
                [
                    'code' => $e->getCode(),
                    'message' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]
            );



            return Command::FAILURE;
        }

        echo "员工完成\n";


        echo "ok-" . time();

        return Command::SUCCESS;
    }
}
