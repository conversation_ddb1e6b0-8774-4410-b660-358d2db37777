<?php

namespace App\Console\Commands;

use App\Models\BeisenBumen;
use App\Models\BeisenYuangong;
use App\Models\Kdd\KddKehubiaoqian;
use App\Models\Qiwei\QiweiBumen;
use App\Models\Qiwei\QiweiXunibumenyuangong;
use App\Models\Qiwei\QiweiYuangong;
use App\Models\Qiwei\QiweiYuangongqun;
use App\Services\BeisenApi\BeisenService;
use App\Services\Qiwei\QiweiApiService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Query\Builder;

// 企微群同步
class QiweiQun extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:qiweiqun';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';
    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {


        $microtime = microtime(true);

        echo "begin-" . time() . "\n";
        $qiweiYuangongsModel = QiweiYuangong::where('enable',1)->where('qiweishanchu', 0)->where('shanchu', 0)->get();

        $qiweiApiService = new QiweiApiService();
        $qiweiYuangongqunModel = new \App\Models\Qiwei\QiweiYuangongqun();
        $qiweiYuangongqunMemberModel = new \App\Models\Qiwei\QiweiYuangongqunmember();

        $qiweiYuangongsModel->chunk(1)->each(function($items) use ($qiweiApiService,$qiweiYuangongqunModel,$qiweiYuangongqunMemberModel) {

            $userIds = $items->pluck("userid")->toArray();

            //$userIds = ['100002477'];
            //群列表
            $qunList = $qiweiApiService->getYuanGongQunList($userIds);
            if($qunList){
                foreach ($qunList as $key=>$val) {
                    $rowQun = $rowQunMember = [];
                    $rowQun['user_id'] = $userIds[0];
                    $rowQun['chat_id'] = $val['chat_id'] ;
                    $rowQun['status'] = $val['status'];
                    $memberList = $qiweiApiService->getYuanGongQunMemberList($val['chat_id']);1
                    if($memberList){
                        $rowQun['name'] = array_key_exists('name',$memberList) ? $memberList['name'] : '';
                        $rowQun['create_time'] = array_key_exists('create_time',$memberList) ? $memberList['create_time'] : 0;
                        $rowQun['notice'] = array_key_exists('notice',$memberList) ? $memberList['notice'] : '';
                        $memberHave = [];
                        if(is_array($memberList['member_list'])){
                            foreach($memberList['member_list'] as $memberInfo){
                                $rowQunMember['user_id'] = $userIds[0];
                                $rowQunMember['chat_id'] = $val['chat_id'];
                                $rowQunMember['member_id'] = $memberInfo['userid'];
                                $rowQunMember['type'] = $memberInfo['type'];
                                $rowQunMember['unionid'] = array_key_exists('unionid',$memberInfo)? $memberInfo['unionid'] : '';
                                $rowQunMember['join_time'] = $memberInfo['join_time'];
                                $rowQunMember['join_scene'] = $memberInfo['join_scene'];
                                $rowQunMember['invitor'] =  array_key_exists('invitor',$memberInfo) && $memberInfo['invitor'] ? json_encode($memberInfo['invitor']): '[]';
                                $rowQunMember['group_nickname'] = $memberInfo['group_nickname'] ? $memberInfo['group_nickname'] :'';
                                $rowQunMember['name'] = $memberInfo['name'];
                                $rowsQunMember[] = $rowQunMember;
                                $memberHave[] = $memberInfo['userid'];
                            }
                            //所有群成员信息
                           $allMemberInfo = \App\Models\Qiwei\QiweiYuangongqunmember::where([
                                'user_id' => $userIds[0],
                                'chat_id' => $val['chat_id']
                            ])->get();
                            $allMemberInfo = $allMemberInfo ? $allMemberInfo->toArray() : [];

                            $haveAllMember = array_column($allMemberInfo,'member_id');
                            $diffMember = array_diff($haveAllMember,$memberHave);
                            if($diffMember){
                                $allMemberInfo = \App\Models\Qiwei\QiweiYuangongqunmember::where([
                                    'user_id' => $userIds[0],
                                    'chat_id' => $val['chat_id']
                                ])->whereIn('member_id',$diffMember)->delete();
                            }
                            $rs = \App\Models\Qiwei\QiweiYuangongqunmember::upsert(
                                $rowsQunMember,
                                ['user_id', 'chat_id','member_id'],
                                $qiweiYuangongqunMemberModel->fillable
                            );
                        }
                    }
                    $rowsQun[] = $rowQun;
                }

                $rs = \App\Models\Qiwei\QiweiYuangongqun::upsert(
                    $rowsQun,
                    ['user_id', 'chat_id'],
                    $qiweiYuangongqunModel->fillable
                );
            }

        });

        echo "ok-" . time() . "\n";

        return Command::SUCCESS;
    }


}
