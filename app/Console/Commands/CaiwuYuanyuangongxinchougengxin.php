<?php

namespace App\Console\Commands;

use App\Models\BeisenBumen;
use App\Models\BeisenYuangong;
use App\Models\Caiwu\CaiwuYuangongxinchou;
use App\Models\Caiwu\CaiwuYuangongxinchousuoding;
use App\Models\Qiwei\QiweiBumen;
use App\Models\Qiwei\QiweiXunibumenyuangong;
use App\Models\Qiwei\QiweiYuangong;
use App\Services\BeisenApi\BeisenService;
use App\Services\Qiwei\QiweiApiService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Query\Builder;


// 企微组织架构同步
class CaiwuYuanyuangongxinchougengxin extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:caiwuyuanyuangongxinchougengxin';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $microtime = microtime(true);

        echo "begin-" . time() . "\n";

        $caiwuyuangongsuodingsModel = CaiwuYuangongxinchousuoding::where('suodingrenId', "=", "")->get();
        if (empty($caiwuyuangongsuodingsModel->toArray())) {
            echo "没有可以执行的年月,请检查年月锁定表是否已全部锁定或此表为空";
            return Command::SUCCESS;
        }



        try {

            // 开启事务
            DB::beginTransaction();



            // 锁定
            CaiwuYuangongxinchousuoding::where('yewutonglu', '园员工薪酬')->whereIn('nianyue', $caiwuyuangongsuodingsModel->pluck('nianyue')->toArray())->lockForUpdate()->get();



            // 上传的薪酬明细
            $xinchoumingxisModel = CaiwuYuangongxinchou::whereIn('发薪所属期', $caiwuyuangongsuodingsModel->pluck('nianyue')->toArray())->where('zhuangtai',0)->orderBy("发薪所属期")->get();
            $xinchoumingxisModelByRiqiYuangongbianma = $xinchoumingxisModel->groupBy(function($item, $key) {
                return $item->发薪所属期 . '-' . $item->员工编码;
            });

            // 北森员工任职信息
            $beisenService = new BeisenService();
            $beisenyuangongsModel = $beisenService->getBeisenYuangongModelWithLizhi();
            $beisenyuangongsModel = $beisenyuangongsModel->where('t_beisen_yuangong_renzhi.jobNumber', "<>", null);
            $beisenyuangongsModel = $beisenyuangongsModel->get();
            $beisenyuangongsModelGroupByJobNumber = $beisenyuangongsModel->groupBy('jobNumber');


            // 北森部门信息
            $beisenbumensModel = $beisenService->getBeisenBumenModel();
            $beisenbumensModel = $beisenbumensModel->get();
            $beisenbumensModelByOid = $beisenbumensModel->keyBy("oId");
            $oidMapTreeYewuxitongbianma = [];

            foreach ($beisenbumensModel as $bumenModel) {
                $treePaths = explode("/", $bumenModel['pOidOrgAdmin_TreePath']);
                $oidMapTreeYewuxitongbianma[$bumenModel['oId']] = [];
                foreach ($treePaths as $oid) {
                    if (!isset($beisenbumensModelByOid[$oid])) continue;
                    if (in_array($beisenbumensModelByOid[$oid]['extzuzhileixing'], ['8', '14', '20'])) {
                        $oidMapTreeYewuxitongbianma[$bumenModel['oId']][] = $beisenbumensModelByOid[$oid]['extyewuxitongbianma'];
                    }
                }
            }

//        是否在北森系统中		integer	北森员工编号匹配表格中员工编号
//        是否在北森系统中这家园所		integer	北森任职记录匹配表格中园所编号
//        是否和北森系统职位匹配		integer	北森职务匹配表格中职务
//        北森系统任职明细		text	北森系统任职明细
//        是否存在多家园所发薪		integer	表格中员工编号其他园所也存在


//            echo json_encode($xinchoumingxisModel);
//            exit();

            foreach ($xinchoumingxisModel as $yuangongmingxiModel) {
                $beisenYuangong = $beisenyuangongsModelGroupByJobNumber[$yuangongmingxiModel['员工编码']] ?? null;

                $suoshuqitime = strtotime($yuangongmingxiModel['发薪所属期']);

                $thismonthfirstday = date("Y-m-d H:i:s", mktime(0, 0, 0, date('m', $suoshuqitime),date('d', $suoshuqitime), date('Y', $suoshuqitime)));
                $thismonthlastday = date("Y-m-d H:i:s", mktime(0, 0, 0, date('m', $suoshuqitime)+1,date('d', $suoshuqitime), date('Y', $suoshuqitime))-1);


//                echo json_encode($beisenYuangong);
//                exit();


//                if ($yuangongmingxiModel['员工编码'] == '2520310110') {
//                    echo json_encode($beisenYuangong);
//                    echo "\n\n";
//                }

                $lastWorkDate = "";
                $lastWorkTime = 0;
                $shifoulizhi = -1;
                if (!empty($beisenYuangong)) {
                    $shifoulizhi = 0;
                    foreach ($beisenYuangong as $yuangongRenzhiRow) {

//                        if (!isset($yuangongRenzhiRow->serviceType)) {
//
//                            echo json_encode($beisenYuangong);
//                            exit();
//
//                        }

                        if ($yuangongRenzhiRow->serviceType == 0 && !empty($yuangongRenzhiRow->lastWorkDate)) {
                            $lastWorkDate = $yuangongRenzhiRow->lastWorkDate;
                            $lastWorkTime = strtotime($lastWorkDate);
                            $shifoulizhi = 1;
                            break;
                        }
                    }
                }
//                if ($yuangongmingxiModel['员工编码'] == '2520310110') {
//                    var_dump($lastWorkDate);
//                    var_dump($lastWorkTime);
//                    var_dump($shifoulizhi);
//
//                    echo "\n\n";
//                }


                // && ($lastWorkDate === null || ($lastWorkTime >= $thismonthfirstday))
                if (!empty($beisenYuangong) ) {
                    // 在北森系统中 在职or离职

                    $yuangongmingxiModel['是否在北森系统中'] = 1;

                    $renzhimingxi = [];
                    $多家园所发薪主职明细 = [];
                    $多家园所发薪主职职位明细 = [];
                    $多家园所发薪兼职明细 = [];
                    $多家园所发薪兼职职位明细 = [];

                    // 北森系统任职明细
                    foreach ($beisenYuangong as $yuangongRenzhiRow) {
                        $renzhimingxi[] = "部门:" . $yuangongRenzhiRow->changmingcheng . ",职务:" . $yuangongRenzhiRow->zhiwuname . "," . ($yuangongRenzhiRow->serviceType == 0 ? "主职" : "");

                        if ($yuangongRenzhiRow->serviceType == 0) {
                            $多家园所发薪主职明细[] = $yuangongRenzhiRow->changmingcheng;
                            $多家园所发薪主职职位明细[] = $yuangongRenzhiRow->zhiwuname;
                        } else {
                            $多家园所发薪兼职明细[] = $yuangongRenzhiRow->changmingcheng;
                            $多家园所发薪兼职职位明细[] = $yuangongRenzhiRow->zhiwuname;
                        }
                    }
                    $yuangongmingxiModel['北森系统任职明细'] = implode("\n", $renzhimingxi);

                    // 是否在北森系统中这家园所   是否和北森系统职位匹配
                    $是否在北森系统中这家园所 = 0;
                    $是否和北森系统职位匹配 = 0;
                    foreach ($beisenYuangong as $yuangongRenzhiRow) {
                        if (!isset($oidMapTreeYewuxitongbianma[$yuangongRenzhiRow->oIdDepartment])) continue;
                        if(in_array($yuangongmingxiModel['系统编码'], $oidMapTreeYewuxitongbianma[$yuangongRenzhiRow->oIdDepartment])) {
                            $是否在北森系统中这家园所 = 1;
                            if ($yuangongmingxiModel['职位名称'] == $yuangongRenzhiRow->zhiwuname) {
                                $是否和北森系统职位匹配 = 1;
                            }
                        }
                    }

                    $yuangongmingxiModel['是否在北森系统中这家园所'] = $是否在北森系统中这家园所;
                    $yuangongmingxiModel['是否和北森系统职位匹配'] = $是否和北森系统职位匹配;

                    $yuangongmingxiModel['多家园所发薪主职明细'] = $多家园所发薪主职明细;
                    $yuangongmingxiModel['多家园所发薪主职职位明细'] = $多家园所发薪主职职位明细;
                    $yuangongmingxiModel['多家园所发薪兼职明细'] = $多家园所发薪兼职明细;
                    $yuangongmingxiModel['多家园所发薪兼职职位明细'] = $多家园所发薪兼职职位明细;

                    $yuangongmingxiModel['是否北森离职'] = $shifoulizhi;
                    $yuangongmingxiModel['北森离职日期'] = $lastWorkDate;


                } else {
                    // 不在北森系统中
                    $yuangongmingxiModel['是否在北森系统中'] = 0;
                    $yuangongmingxiModel['北森系统任职明细'] = "";
                    $yuangongmingxiModel['是否在北森系统中这家园所'] = 0;
                    $yuangongmingxiModel['是否和北森系统职位匹配'] = 0;

                    $yuangongmingxiModel['多家园所发薪主职明细'] = [];
                    $yuangongmingxiModel['多家园所发薪主职职位明细'] = [];
                    $yuangongmingxiModel['多家园所发薪兼职明细'] = [];
                    $yuangongmingxiModel['多家园所发薪兼职职位明细'] = [];

                    $yuangongmingxiModel['是否北森离职'] = $shifoulizhi;
                    $yuangongmingxiModel['北森离职日期'] = "";

                }

                // 是否存在多家园所发薪
                $duoyuanfaxin = $xinchoumingxisModelByRiqiYuangongbianma[$yuangongmingxiModel['发薪所属期'] . '-' . $yuangongmingxiModel['员工编码']] ?? null;
                if (!empty($duoyuanfaxin)) {
                    $yuangongmingxiModel['是否存在多家园所发薪'] = 0;
                    foreach ($duoyuanfaxin as $duoyuanfaxinRow) {
                        if ($yuangongmingxiModel['系统编码'] != $duoyuanfaxinRow['系统编码']) {
                            $yuangongmingxiModel['是否存在多家园所发薪'] = 1;
                        }
                    }
                } else {
                    $yuangongmingxiModel['是否存在多家园所发薪'] = 0;
                }



                $yuangongmingxiModel['zhuangtai'] = 1;
                $yuangongmingxiModel->save();

            }



            // 提交事务
            DB::commit();
        } catch (\Exception $e) {

            // 发生错误，回滚事务
            DB::rollBack();


            var_dump($e->getMessage());

            var_dump($e->getTraceAsString());

            echo "\n\n";


//            return response()->json(
//                [
//                    'error' => 1002,
//                    'errorMsg'=>'',
//                    'success' => false,
//                    'data' => '',
//                    'message' => $e->getMessage(),
//                ]);
        }












        echo "ok-" . time() . "\n";

        return Command::SUCCESS;
    }


}
