<?php

namespace App\Console\Commands;

use App\Models\BeisenBumen;
use App\Models\BeisenYuangong;
use App\Models\Qiwei\QiweiBumen;
use App\Models\Qiwei\QiweiXunibumenyuangong;
use App\Models\Qiwei\QiweiYuangong;
use App\Services\BeisenApi\BeisenService;
use App\Services\Qiwei\QiweiApiService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Query\Builder;

// 企微组织架构同步
class QiweiTongbuTest extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:qiweitongbutest';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $microtime = microtime(true);

        echo "begin-" . time() . "\n";

        // select * from t_beisen_bumen where modifiedTime > '2024-09-01 00:00:00' or created_at >= '2024-09-01 00:00:00';
        $now = date("Y-m-d H:i:s");

        $qiweiApiService = new QiweiApiService();
        $beisenService = new BeisenService();
        $qiweiBumensModel = QiweiBumen::where("shanchu", 0)->orderByDesc('bumenchangmingcheng')->get();
        $qiweiBumensModelByChangmingcheng = $qiweiBumensModel->keyBy("bumenchangmingcheng");
        $qiweiBumensModelById = $qiweiBumensModel->keyBy("id");
        $qiweiBumensModelByBeisenOid = $qiweiBumensModel->where("beisenOid", "<>", "0")->keyBy("beisenOid");
        $beisenBumensModel = $beisenService->getBeisenBumenModel();
        $beisenBumensModel = $beisenBumensModel->whereNotIn("code", ['RootOrg', 'DefaultDept'])->get();
        $beisenBumensModelByOid = $beisenBumensModel->keyBy("oId");


        $baseId = 100000000;

        // 员工新增/修改
        $beisenYuangongsModel = $beisenService->getBeisenYuangongModel();

        $beisenYuangongsModel = $beisenYuangongsModel->where('t_beisen_yuangong_renzhi.jobNumber', '9202502053');//刘慧杰 9202502053  王艳阳 9202503031

        $beisenYuangongsModel = $beisenYuangongsModel->whereNotIn('t_beisen_bumen.code', ['RootOrg', 'DefaultDept']);

        $beisenYuangongsModel = $beisenYuangongsModel->get();
//        $beisenYuangongsModel = $beisenYuangongsModel->toSql();
//        print_r($beisenYuangongsModel);
//        die;

        $beisenYuangongsGroupByJobNumber = $beisenYuangongsModel->groupBy("jobNumber");
        //获取轻应用企微表中 员工数据
        $qiweiYuangongsModel = QiweiYuangong::where('qiweishanchu', 0)->get();
        $qiweiYuangongsModelByJobNumber = $qiweiYuangongsModel->keyBy("userid");
        //获取轻应用企微表中 虚拟部门数据
        $qiweiXunibumenyuangongModel = QiweiXunibumenyuangong::all();
        $qiweiXunibumenyuangongModelByUserId = $qiweiXunibumenyuangongModel->keyBy('userid');


//        print_r($beisenYuangongsGroupByJobNumber);die;

        $i = 0;
        foreach ($beisenYuangongsGroupByJobNumber as $jobNumber => $rows) {
            if ($jobNumber == '0110220001') continue;
            if ($jobNumber == '000000') continue;
            $qiweiYuangongModel = $qiweiYuangongsModelByJobNumber[$jobNumber] ?? null;
            if (!empty($qiweiYuangongModel) && ($qiweiYuangongModel['enable'] == 0
                                                || $qiweiYuangongModel['shanchu'] == 1)) {
                continue;
            }
            $zhuzhanghaoModel = $rows->where('serviceType', 0)->first();
            $data = [];

            $data['userid'] = $zhuzhanghaoModel->jobNumber;
            $data['name'] = $zhuzhanghaoModel->name;
            $data['mobile'] = $zhuzhanghaoModel->mobilePhone;
            $data['position'] = $zhuzhanghaoModel->zhiwuname;
            if ($zhuzhanghaoModel->gender) {
                $data['gender'] = intval($zhuzhanghaoModel->gender) + 1;
            }

            if (isset($qiweiBumensModelByBeisenOid[$zhuzhanghaoModel->oIdDepartment])) {
                $data['main_department'] = $qiweiBumensModelByBeisenOid[$zhuzhanghaoModel->oIdDepartment]->id;
            } else {
                $data['main_department'] = $zhuzhanghaoModel->oIdDepartment + $baseId;
            }


            $data['department'] = [];
            $data['is_leader_in_dept'] = [];
            foreach ($rows as $row) {

                if (isset($qiweiBumensModelByBeisenOid[$row->oIdDepartment])) {
                    $data['department'][] = $qiweiBumensModelByBeisenOid[$row->oIdDepartment]->id;
                } else {
                    $data['department'][] = $row->oIdDepartment + $baseId;
                }

                if (isset($beisenBumensModelByOid[$row->oIdDepartment])) {
                    if ($beisenBumensModelByOid[$row->oIdDepartment]['personInCharge'] == $zhuzhanghaoModel->userId) {
                        $data['is_leader_in_dept'][] = 1;
                    } else {
                        $data['is_leader_in_dept'][] = 0;
                    }
                } else {
                    $data['is_leader_in_dept'][] = 0;
                }
            }

            // 追加虚拟部门
            if (isset($qiweiXunibumenyuangongModelByUserId[$zhuzhanghaoModel->jobNumber])) {
                $data['department'][] = $qiweiXunibumenyuangongModelByUserId[$zhuzhanghaoModel->jobNumber]['qiweibumenid'];
            }

            $data['enable'] = 1;
//print_r($data);die;
            // 新增
            if (!$qiweiYuangongModel) {
//                die;
//                echo $zhuzhanghaoModel->jobNumber . "\n";
                $i++;
                // echo "1";
                echo "员工新增：" . $zhuzhanghaoModel->name . " - " . $zhuzhanghaoModel->jobNumber . "\n";
                echo json_encode($data) . "\n\n";
                $response = $qiweiApiService->yuangongCreate($data);
                echo json_encode($response) . "\n\n\n";

                // 如果qiweishanchu 为1则改变原有状态
//                if ($i>5) {
//                    exit();
//                }
            }

            // $data['department']

//            // 修改
//            if ($qiweiYuangongModel
//                && ($qiweiYuangongModel->main_department != $data['main_department']
//                    || count(array_intersect($qiweiYuangongModel->department, $data['department'])) != count($qiweiYuangongModel->department)
//                )) {
//
//                // 主部门或其他任职部门有修改
//
//                echo "员工修改：" . $zhuzhanghaoModel->name . " - " . $zhuzhanghaoModel->jobNumber .
//                    ", " . $qiweiYuangongModel->name . " = " .$zhuzhanghaoModel->name .
//                    ", " . $qiweiYuangongModel->main_department . " = " . $data['main_department'] .
//                    ", " . implode("/", $qiweiYuangongModel->department) . " = " . implode("/", $data['department']) .
//                    "\n";
////                $response = $qywxApi->userUpdate($data);
//                //
////                echo "2";
//            }
        }

        echo "ok-" . time() . "\n";
        return Command::SUCCESS;
    }


}
