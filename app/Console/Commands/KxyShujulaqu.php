<?php

namespace App\Console\Commands;

use App\Models\Kxy\KxyXuexixiangmu;
use App\Http\Controllers\Api\KxyController;
use Illuminate\Console\Command;

class KxyShujulaqu extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:kxyshujulaqu';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        //拉取学习项目数据
        $KxyCon = new KxyController();
        $KxyCon->xuexixiangmuliebiao('2024-01-01','2050-01-01',1,100);
        echo '学习项目数据拉取成功';

        //拉取项目下用户监控数据
        $xuexixiangmuModel = KxyXuexixiangmu::all();
        $xuexixiangmuModel->chunk(20)->each(function($xuexixiangmuItems){
            $xuexixiangmuList = $xuexixiangmuItems->toArray();

            foreach ($xuexixiangmuList as $xuexixiangmuVar){
                //拉取数据
                $KxyCon = new KxyController();
                $KxyCon->xuexixiangmujiankongOne($xuexixiangmuVar['course_id'],1,50);
            }
        });
        echo '项目下用户监控数据拉取完成';

        //拉取酷学院用户数据
        $KxyCon = new KxyController();
        $KxyCon->userslist(1,100);
        echo '员工数据拉取成功';
    }
}
