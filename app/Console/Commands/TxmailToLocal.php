<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Library\EduConst;
use think\Exception;

class TxmailToLocal extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:TxmailToLocal';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    const CORPID = "wm748ed7f8ba0e927f";
    // const SSO_SECRET = "oczm8_ZcKVl6WhKlrypmPZ237OuSH74Lv-4lsoZwKhYCzf6_OUU6fOX3ejArWoUO";
    const USER_SECRET = "nK5Yxkb60fLrr6JASqOxNz5m-llkevSUsklRfJd1Vzf6ClY3lqsUg3MogDmRc_Tp";


    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        echo time() . "_begin\n";

        $client = new \GuzzleHttp\Client();

        $get_access_token_url = 'https://api.exmail.qq.com/cgi-bin/gettoken?corpid='.self::CORPID.'&corpsecret='.self::USER_SECRET;
        $response = $client->get($get_access_token_url);
        //将获取到的数据转化为Json
        $access_token_json = json_decode($response->getBody()->getContents());
        //查询获取的状态，成功时：errmsg会返回 ok
        $state= $access_token_json->errmsg;
        if($state == "ok"){ //判断当前获取数据是否成功

            //获取access_token值
            $access_token = $access_token_json->access_token;
            $departmentUserListUrl = "https://api.exmail.qq.com/cgi-bin/user/list?access_token={$access_token}&department_id=1&fetch_child=1";

            $userDetailListResponse = $client->get($departmentUserListUrl);

            $userDetailContent = $userDetailListResponse->getBody()->getContents();
            $userDetailContent = json_decode($userDetailContent);

            if($userDetailContent->errmsg=="ok") {
                Cache::put('TXMAIL_USERDETAILLIST', json_encode($userDetailContent->userlist));
            }

        }


        echo time() . "_ok\n";

        return 0;

    }

}
