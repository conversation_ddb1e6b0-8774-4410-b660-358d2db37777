<?php

namespace App\Console\Commands;

use App\Models\BeisenBumen;
use App\Models\BeisenYuangong;
use App\Models\Kdd\KddKehubiaoqian;
use App\Models\Qiwei\QiweiBumen;
use App\Models\Qiwei\QiweiXunibumenyuangong;
use App\Models\Qiwei\QiweiYuangong;
use App\Services\BeisenApi\BeisenService;
use App\Services\Kdd\KddService;
use App\Services\Qiwei\QiweiApiService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Query\Builder;

// 企微组织架构同步
class QiweiTongbuBiaoqian extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:qiweitongbubiaoqian {startTime?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    public static $tagMap = [
            '高意向客户' => 'etlr_HBwAAQabYzhQwHUgP0vBthTcmVg',
            '无效客户' => 'etlr_HBwAAediM_6b9nqV0zWdM7CzpWQ',
            '已到访' => 'etlr_HBwAATMLTukk8hz-_TO2LQxFHug',
            '在读' => 'etlr_HBwAAmDjcZ02fBnLi2JPZzShh8g',
            '0-6月龄' => 'etlr_HBwAAWVJxS7fmq_D01KqledG1WA',
            '6-12月龄' => 'etlr_HBwAADp_qXtOP3B7zWfITYQKeLw',
            '12-18月龄' => 'etlr_HBwAAnT4mJ4803D0x9zDQgvkK5g',
            '18-36月龄' => 'etlr_HBwAAEYGrP3tyG1tHvDzHzv8Mnw',
            '36-48月龄' => 'etlr_HBwAAG-yc_BRnY1jsipp-9qMoXA',
            '48月龄+' => 'etlr_HBwAAu_4b8qqkBrEK0nJp9dAwPQ',
        ];

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $microtime = microtime(true);

        echo "begin-" . time() . "\n";

        $now = date("Y-m-d H:i:s");
        // 3天前的0时0分
        //$time3ago = mktime(0, 0, 0, date("m"), date("d")-20,   date("Y"));
        //20250227 连 当前时间前40分钟
        $startTime = $this->argument('startTime');
        $time3ago = $startTime ? $startTime : time()-2400;
        $kddApiService = new \App\Services\Kdd\KddApiService();
        $qiweiApiService = new QiweiApiService();


        $tongbuList = $kddApiService->getAllQiweibiaoqiantongbuliebiao(['starttime'=>$time3ago]);

        $kddKehubiaoqianModel = new KddKehubiaoqian();
        $kddKehubiaoqianModelFillable = $kddKehubiaoqianModel->fillable;
        $kddService = new \App\Services\Kdd\KddService();

        $nowDate = date("Y-m-d");

        //------------------------- 更新标签数据 ----------------------------
        foreach ($tongbuList as $row) {

            $row['gengxinzhuangtai'] = 1;
//            $row['nianlingzhuangtai'] = 0;
            $row['month'] = $kddService->dateDiffForMonth($row['birthday'], $nowDate);
            $rows = [$row];

            KddKehubiaoqian::upsert(
                    $rows,
                    ['cid'],
                    $kddKehubiaoqianModelFillable
                );
        }

        //------------------------- 同步企微 ----------------------------
        $kehubiaoqianModels = KddKehubiaoqian::where('gengxinzhuangtai', 1)->orWhere('nianlingzhuangtai', 1)->get();

        //获取所有标签
        //$tagList = $qiweiApiService->getTag();
        //echo json_encode($tagList);

        foreach ($kehubiaoqianModels as $model) {

            $kehuModels = \App\Models\Qiwei\QiweiKehu::where('externalUnionId', $model->wxunionid)->get();

            foreach ($kehuModels as $kehuModel) {


                // 同步备注
                $rs = $qiweiApiService->xiugaikehubeizhu(
                    [
                        'userid' => $kehuModel['userId'], //'0110210366',
                        'external_userid' => $kehuModel['externalUserId'], // 'wmlr_HBwAAgAIuxdxDKJ5gfWv7vNJ0BQ',
                        'remark' => $model['name'] ?? '',
//                        'description' => '',
//                        'remark_company' => '',
                        'remark_mobiles' => [
                            $model['mobile']
                        ]
                    ]
                );
//                public static $tagMap = [
//                    '高意向客户' => 'etlr_HBwAAQabYzhQwHUgP0vBthTcmVg',
//                    '无效客户' => 'etlr_HBwAAediM_6b9nqV0zWdM7CzpWQ',
//                    '已到访' => 'etlr_HBwAATMLTukk8hz-_TO2LQxFHug',
//                    '在读' => 'etlr_HBwAAmDjcZ02fBnLi2JPZzShh8g',
//                    '0-6月龄' => 'etlr_HBwAAWVJxS7fmq_D01KqledG1WA',
//                    '6-12月龄' => 'etlr_HBwAADp_qXtOP3B7zWfITYQKeLw',
//                    '12-18月龄' => 'etlr_HBwAAnT4mJ4803D0x9zDQgvkK5g',
//                    '18-36月龄' => 'etlr_HBwAAEYGrP3tyG1tHvDzHzv8Mnw',
//                    '36-48月龄' => 'etlr_HBwAAG-yc_BRnY1jsipp-9qMoXA',
//                    '48月龄+' => 'etlr_HBwAAu_4b8qqkBrEK0nJp9dAwPQ',
//                ];

                $addTag = [];
                $removeTag = [];

                // 意向
                if ($model['yixiang'] == 1) {
                    $addTag[] = self::$tagMap['高意向客户'];
                } else {
                    $removeTag[] = self::$tagMap['高意向客户'];
                }
                // 无效客户
                if ($model['wuxiao'] == 1) {
                    $addTag[] = self::$tagMap['无效客户'];
                } else {
                    $removeTag[] = self::$tagMap['无效客户'];
                }
                // 到访
                if ($model['yidaofang'] == 1) {
                    $addTag[] = self::$tagMap['已到访'];
                } else {
                    $removeTag[] = self::$tagMap['已到访'];
                }
                // 在读
                if ($model['zaidu'] == 1) {
                    $addTag[] = self::$tagMap['在读'];
                } else {
                    $removeTag[] = self::$tagMap['在读'];
                }

                if (0 <= $model['month'] && $model['month'] < 6) {
                    $addTag[] = self::$tagMap['0-6月龄'];
                } else {
                    $removeTag[] = self::$tagMap['0-6月龄'];
                }
                if (6 <= $model['month'] && $model['month'] < 12) {
                    $addTag[] = self::$tagMap['6-12月龄'];
                } else {
                    $removeTag[] = self::$tagMap['6-12月龄'];
                }
                if (12 <= $model['month'] && $model['month'] < 18) {
                    $addTag[] = self::$tagMap['12-18月龄'];
                } else {
                    $removeTag[] = self::$tagMap['12-18月龄'];
                }
                if (18 <= $model['month'] && $model['month'] < 36) {
                    $addTag[] = self::$tagMap['18-36月龄'];
                } else {
                    $removeTag[] = self::$tagMap['18-36月龄'];
                }
                if (36 <= $model['month'] && $model['month'] < 48) {
                    $addTag[] = self::$tagMap['36-48月龄'];
                } else {
                    $removeTag[] = self::$tagMap['36-48月龄'];
                }
                if (48 <= $model['month']) {
                    $addTag[] = self::$tagMap['48月龄+'];
                } else {
                    $removeTag[] = self::$tagMap['48月龄+'];
                }

                // 同步标签
                $rs = $qiweiApiService->xiugaikehubiaoqian([
                    'userid' =>  $kehuModel['userId'],
                    'external_userid' => $kehuModel['externalUserId'], //'wmlr_HBwAAgAIuxdxDKJ5gfWv7vNJ0BQ',
                    "add_tag" => $addTag, //["etlr_HBwAAWIpcnggJG682_Lw3LSXizA"],
                    'remove_tag'=> $removeTag,
                ]);
            }
            KddKehubiaoqian::where(['id' => $model->id])->update(['gengxinzhuangtai' => 0,'nianlingzhuangtai' =>0]);
            echo "用户" .$model->name . "更新成功\n";
        }


//        foreach ($tongbuList as $row) {
//
//
//            // $qiweiApiService
//
//            echo json_encode($row) ;
//
//            echo "\n";
//
//        }

////        ->where('userid','0110210366')
//        $qiweiYuangongsModel = QiweiYuangong::where('qiweishanchu','0')->where('shanchu','0')->where('enable','1')->get();
//
//        foreach ($qiweiYuangongsModel as $model) {
//            if ($model['chengyuanzhanghao'] != $model['userid']) {
//
//
//
//
//
//            $data = [];
//            $data['userid'] =$model['userid'];
//            $data['extattr']['attrs'] = [
//                [
//                    'type' => 0,
//                    'name' => '成员账号',
//                    'text' => [
//                        'value' => $model['userid'],
//                    ],
//                ],
//            ];
//
//
//            echo json_encode($data) . "\n\n";
//
//
//            $response = $qiweiApiService->yuangongUpdate($data);
//
//            echo json_encode($response) . "\n\n\n";
//
//
//
//
//            }
//        }

        echo "ok-" . date("Y-m-d H:i:s",time()) . "\n";

        return Command::SUCCESS;
    }


}
