<?php

namespace App\Console\Commands;

use AlibabaCloud\SDK\Dingtalk\Vfinance_1_0\Models\CreateSubInstitutionRequest\contactInfo;
use App\Models\BeisenBumen;
use App\Models\Dd\DdJuese;
use App\Models\Dd\DdJueseyuangong;
use App\Models\DdBumen;
use App\Models\DdYuangong;
use App\Services\BeisenApi\BeisenService;
use App\Services\Dd\DdApiService;
use Illuminate\Console\Command;
use Library\EduConst;

// 获取北森主数据
class DdJueseyuangongjisuan extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:ddjueseyuangongjisuan';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $microtime = microtime(true);
        $now = date("Y-m-d H:i:s");

        $beisenService = new BeisenService();


//        ->where('id', 5)->get(); //

        $juseListModel = DdJuese::with('guize')->where('roleid', "<>","")->get();
        $yuangongList = [];
        foreach ($juseListModel as $juese) {

//            if ($juese['id'] != 22) continue;
            //echo $juese['id'] . "\n";

//            if (!isset($juese['ddUserIds'])) continue;

            $renyuansModel = $juese->renyuan()->get();


//            if (isset($juese['ddUserIds'])) {
//                foreach ($juese['ddUserIds'] as $ddUserId) {
//                    $newYuangong = [
//                        'jueseid' => $juese->id,
//                        'beisenUserId' => '',
//                        'ddUserId' => $ddUserId,
//                    ];
//
//                    $yuangongList[] = $newYuangong;
//                }
//
//            }



            if (isset($renyuansModel)) {
                foreach ($renyuansModel as $ddUserId) {
                    $newYuangong = [
                        'jueseid' => $juese->id,
                        'beisenUserId' => '',
                        'ddUserId' => $ddUserId['ddUserId'],
                    ];
                    $yuangongList[] = $newYuangong;
                }
            }


//            echo json_encode($juese['guize']);
//            echo ":asdf";
//            exit();


            foreach ($juese['guize'] as $guize) {

                if (empty($guize['ddBumenIds'])) continue;
//                echo "12\n";

                // 获取钉钉部门列表
                $ddBumenListModel = DdBumen::where('shanchu', 0)->whereIn('dept_id', $guize['ddBumenIds'])->get();
                if (empty($ddBumenListModel)) continue;

                // 获取北森部门
                if ($guize['baohanxiajibumen'] == "1") {
                    // 包含下级部门
                    $beisenBumenListModel = $beisenService->getBeisenBumenModel($now);
                    $beisenBumenListModel = $beisenBumenListModel->where(function ($query) use ($ddBumenListModel) {
                        $changmingchengList = $ddBumenListModel->pluck('changmingcheng');
                        foreach ($changmingchengList as $where) {
                            $query->orWhere("changmingcheng", "like", "{$where}%");
                        }
                    });
                } else {
                    // 不包含
//                    echo "11\n";

                    $beisenBumenListModel = $beisenService->getBeisenBumenModel($now);

//                    echo json_encode($beisenBumenListModel->get());


                    $beisenBumenListModel = $beisenBumenListModel->whereIn('changmingcheng', $ddBumenListModel->pluck('changmingcheng'));
                }

//                echo json_encode($beisenBumenListModel->get());
//                exit();

                // 组织类型
                if (!empty($guize['beisenZuzhileixingIds'])) {
                    $beisenBumenListModel = $beisenBumenListModel->whereIn('extzuzhileixing', $guize['beisenZuzhileixingIds']);
                }



                $beisenBumenList = $beisenBumenListModel->get();
                if (empty($beisenBumenList)) continue;

                if ($guize['bumenfuzeren'] == "1") {

                    // 部门负责人
                    $personInChargeList = $beisenBumenList->where("personInCharge", "<>", "")->pluck("personInCharge");

                    $beisenYuangongModel = $beisenService->getBeisenYuangongModel($now);
                    $beisenYuangongModel = $beisenYuangongModel->whereIn('t_beisen_yuangong_renzhi.userId', $personInChargeList);

                } else {

                    // 非部门负责人
                    $beisenYuangongModel = $beisenService->getBeisenYuangongModel($now);
                    $beisenYuangongModel->whereIn('t_beisen_bumen.changmingcheng', $beisenBumenList->pluck('changmingcheng'));

                }

                // 职务筛选
                if (!empty($guize['ddZhiwuNames'])) {
                    $beisenYuangongModel->whereIn('t_beisen_zhiwu.name', $guize['ddZhiwuNames']);
                }



                // 获取北森员工数据
                $beisenYuangongList = $beisenYuangongModel->get();

                if (!empty($beisenYuangongList)) {

                    $ddYuangongListModel = DdYuangong::where("shanchu", 0)->whereIn("job_number", $beisenYuangongList->pluck("jobNumber"))->get();

                    foreach ($ddYuangongListModel as $ddYuangongModel) {

                        $newYuangong = [
                            'jueseid' => $juese->id,
                            'beisenUserId' => '',
                            'ddUserId' => $ddYuangongModel['userid'],
                        ];

                        $yuangongList[] = $newYuangong;
                    }
                }


            }
////
//            echo json_encode($yuangongList);
//            echo "\n\n";



            $yuangongCollect = collect($yuangongList);
            $yuangongCollect->chunk(100)->each(function($items) {

                $rows = $items->toArray();

                DdJueseyuangong::upsert(
                    $rows,
                    ['jueseid', 'ddUserId'],
                    ['jueseid', 'beisenUserId','ddUserId']
                );
            });

//
           // echo json_encode($yuangongCollect->pluck('ddUserId')->toArray());
           // echo "\n\n";

//            echo json_encode($juese->id);
//            echo "\n\n";



            DdJueseyuangong::where('jueseid', $juese->id)->whereNotIn("ddUserId", $yuangongCollect->pluck('ddUserId')->toArray())->delete();

//            echo json_encode($arr);
//            echo "\n\n";

        }




//        echo json_encode($yuangongList);
//        echo "\n\n\n";




        echo "ok-" . time() . "\n";

        return Command::SUCCESS;
    }
}
