<?php

namespace App\Console\Commands;


use App\Models\IccMendian;
use App\Models\Qiwei\QiweiQunfajilu;
use App\Models\Qiwei\QiweiQunfarenwujieguo;
use App\Models\Qiwei\QiweiQunfarenwuliebiao;
use App\Services\FanweiService;
use EasyWeChat\Work\Application;
use Illuminate\Console\Command;
use Library\EduConst;
use App\Facades\Fanwei;

// 测试矩阵同步
class QiweiQunfaShujulaqu extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:qiweiqunfashujulaqu';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $microtime = microtime(true);

        echo "begin-" . time() . "\n";


        $qiweiConfig = [
            'wxCorpId' => 'ww8977cb1b47249e00',   // 企业微信企业ID
            'wxAgentId' => '1000009',  // 企业微信应用ID
            'wxAgentSecret' => 'RpxeEXqoBspUnyxLS-nat6hsWgLU-Dqq8OqqAOPfJ9w',  // 企业微信应用秘钥
            'wxContactsSecret' => 'e7t6q01rNxU-HRcqDdPMFcLtzf6BhnlyjppZanyoW9w',   // 企业微信通讯录秘钥
            'wxToken' => 'easywechat',    // 企业微信token
            'wxAesKey' => '35d4687abb469072a29f1c242xx222113xxxx',   // 企业微信aesKey
        ];

        $readApp = new Application([
            'corp_id' => $qiweiConfig['wxCorpId'],
            'agent_id' => $qiweiConfig['wxAgentId'],
            'secret' => $qiweiConfig['wxAgentSecret'],
            'token' => $qiweiConfig['wxToken'],
            'aes_key' => $qiweiConfig['wxAesKey'],
            'http' => [
                'throw'  => true, // 状态码非 200、300 时是否抛出异常，默认为开启
                'timeout' => 5.0,
                'retry' => true, // 使用默认重试配置
            ]
        ]);

        $writeApp = new Application([
            'corp_id' => $qiweiConfig['wxCorpId'],
            // 'agent_id' => $weChatOptions['wxAgentId'],
            'secret' => $qiweiConfig['wxContactsSecret'],
            'token' => $qiweiConfig['wxToken'],
            'aes_key' => $qiweiConfig['wxAesKey'],
            'http' => [
                'throw'  => true, // 状态码非 200、300 时是否抛出异常，默认为开启
                'timeout' => 5.0,
                'retry' => true, // 使用默认重试配置
            ]
        ]);

        $readAppClient = $readApp->getClient();
        $writeAppClient = $writeApp->getClient();

        $time = time();

        $startTime = mktime(0, 0, 0, date("m", $time), date("d", $time)-7,   date("Y", $time));
        $endTime = $time;



        // 获取企业的全部群发记录
        $data = [
//            'chat_type' => 'single',
            'start_time' => $startTime,
            'end_time' => $endTime,
//            'limit' => 1,
//            'cursor' => 'Pgno9fZQM-7o38wiO3pxaIxNhOys3Vhsz8d36S-vu4I',
//            'cursor' => 'mHeU-JYPWoCjo5rSFDkbPEOrXVKwQ9qFrfgXDqy_w2Q',
        ];

        $qiweiQunfajiluModel = new QiweiQunfajilu();
        $qiweiQunfajiluFillable = $qiweiQunfajiluModel->fillable;
        $qunfaData = ['single' => [], 'group' => []];
        $chat_type = ['single', 'group'];

        foreach ($chat_type as $chatTypeName) {

            $data['chat_type'] = $chatTypeName;

            $tf = true;
            $i = 0;
            do {
                $i++;
                echo $i . "_" .$chatTypeName . "_群发记录获取" . "\n";
                $response = $readAppClient->postJson('/cgi-bin/externalcontact/get_groupmsg_list_v2', $data);
                $responseData = $response->toArray();
                if ($responseData['errcode'] == 0) {
                    if (empty($responseData['group_msg_list'])) {
                        $tf = false;
                    } else {
                        $data['cursor'] = $responseData['next_cursor'] ?? '';
                        $qunfaData[$chatTypeName] = array_merge($qunfaData[$chatTypeName], $responseData['group_msg_list']);
                    }
                    if (empty($responseData['next_cursor'])) {
                        $tf = false;
                    }
                } else {
                    $tf = false;
                }
            } while($tf && $i <= 100);
        }

        // echo json_encode($qunfaData);

        foreach ($qunfaData as $chatType => $rows) {
            if (empty($rows)) continue;
            $rowsCollect = collect($rows);
            $rowsCollect->chunk(100)->each(function($items, $key) use ($chatType, $qiweiQunfajiluFillable) {
                $rows = $items->toArray();
                foreach ($rows as $k=>$v) {
                    $rows[$k]['chat_type'] = $chatType;

                    $rows[$k]['text'] = $rows[$k]['text'] ?? (object)[];
                    $rows[$k]['attachments'] = $rows[$k]['attachments'] ?? (object)[];

                    $rows[$k]['text'] = json_encode($rows[$k]['text']);
                    $rows[$k]['attachments'] = json_encode($rows[$k]['attachments']);
                }
                QiweiQunfajilu::upsert(
                    $rows,
                    ['msgid'],
                    $qiweiQunfajiluFillable
                );
            });
        }




        // 获取群发成员发送任务列表
        $qunfajilurenwuData = [];
        $qiweiqunfarenwuLiebiaosModel = new QiweiQunfarenwuliebiao();
        $qiweiqunfarenwuliebaoFillable = $qiweiqunfarenwuLiebiaosModel->fillable;
        $qunfajilusModel = QiweiQunfajilu::where('create_time', '>=', $startTime)->where('create_time','<=', $endTime)->get();
        foreach ($qunfajilusModel as $jiluModel) {

            $data = [
                'msgid' => $jiluModel['msgid'],
                'limit' => 1000,
            ];

            $tf = true;
            $i = 0;
            do {
                $i++;
                echo $i . "_获取群发成员发送任务列表" . "\n";

                $response = $readAppClient->postJson('/cgi-bin/externalcontact/get_groupmsg_task', $data);
                $responseData = $response->toArray();

                if ($responseData['errcode'] == 0) {
                    if (empty($responseData['task_list'])) {
                        $tf = false;
                    } else {
                        $data['cursor'] = $responseData['next_cursor'] ?? '';

                        $taskList = $responseData['task_list'];
                        foreach ($taskList as $k=>$v) {
                            $taskList[$k]['msgid'] = $jiluModel['msgid'];
                            $taskList[$k]['send_time'] = $taskList[$k]['send_time'] ?? 0;
                        }

                        $qunfajilurenwuData = array_merge($qunfajilurenwuData, $taskList);
                    }
                    if (empty($responseData['next_cursor'])) {
                        $tf = false;
                    }
                } else {
                    $tf = false;
                }
            } while($tf && $i <= 100);
        }

        $qunfajilurenwuCollect = collect($qunfajilurenwuData);
        $qunfajilurenwuCollect->chunk(100)->each(function ($items, $key) use ($qiweiqunfarenwuliebaoFillable) {
            $rows = $items->toArray();

            QiweiQunfarenwuliebiao::upsert(
                $rows,
                ['msgid', 'userid'],
                $qiweiqunfarenwuliebaoFillable
            );
        });




        // 获取企业群发成员执行结果
        $qunfajilurenwujieguoData = [];
        $qiweiqunfarenwujieguoModel = new QiweiQunfarenwujieguo();
        $qiweiQunfarenwujieguoFillable = $qiweiqunfarenwujieguoModel->fillable;
        $qiweiqunfarenwuLiebiaosModel = QiweiQunfarenwuliebiao::all();
        foreach ($qiweiqunfarenwuLiebiaosModel as $liebiaoModel) {

            $data = [
                'msgid' => $liebiaoModel['msgid'],
                'userid' => $liebiaoModel['userid'],
                'limit' => 1000,
            ];



            $tf = true;
            $i = 0;
            do {
                $i++;
                echo $i . "_获取企业群发成员执行结果" . "\n";

                $response = $readAppClient->postJson('/cgi-bin/externalcontact/get_groupmsg_send_result', $data);
                $responseData = $response->toArray();

                // echo json_encode($responseData) . "\n\n\n";

                if ($responseData['errcode'] == 0) {
                    if (empty($responseData['send_list'])) {
                        $tf = false;
                    } else {
                        $data['cursor'] = $responseData['next_cursor'] ?? '';

                        $sendList = $responseData['send_list'];
                        foreach ($sendList as $k=>$v) {
                            $sendList[$k]['msgid'] = $liebiaoModel['msgid'];
                            $sendList[$k]['send_time'] = $sendList[$k]['send_time'] ?? 0;

                            $sendList[$k]['chat_id'] = $sendList[$k]['chat_id'] ?? '';
                            $sendList[$k]['external_userid'] = $sendList[$k]['external_userid'] ?? '';
                        }

                        $qunfajilurenwujieguoData = array_merge($qunfajilurenwujieguoData, $sendList);
                    }
                    if (empty($responseData['next_cursor'])) {
                        $tf = false;
                    }
                } else {
                    $tf = false;
                }
            } while($tf && $i <= 100);
        }

//        echo json_encode($qunfajilurenwujieguoData);
//        exit();

        $qunfajilurenwujieguoCollect = collect($qunfajilurenwujieguoData);
        $qunfajilurenwujieguoCollect->chunk(100)->each(function ($items, $key) use ($qiweiQunfarenwujieguoFillable) {
            $rows = $items->toArray();

            QiweiQunfarenwujieguo::upsert(
                $rows,
                ['msgid', 'userid', 'external_userid', 'chat_id'],
                $qiweiQunfarenwujieguoFillable
            );
        });













        echo "ok-" . time() . "\n";



        return Command::SUCCESS;
    }
}
