<?php

namespace App\Console\Commands;

use App\Models\BeisenBumen;
use App\Models\BeisenYuangong;
use App\Models\Kdd\KddKehubiaoqian;
use App\Models\Qiwei\QiweiBumen;
use App\Models\Qiwei\QiweiXunibumenyuangong;
use App\Models\Qiwei\QiweiYuangong;
use App\Services\BeisenApi\BeisenService;
use App\Services\Kdd\KddService;
use App\Services\Qiwei\QiweiApiService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Query\Builder;

// 企微同步标签更新生日
class QiweiTongbuBiaoqiangengxinshengri extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:qiweitongbubiaoqianshengri';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    public static $tagMap = [
            '高意向客户' => 'etlr_HBwAAQabYzhQwHUgP0vBthTcmVg',
            '无效客户' => 'etlr_HBwAAediM_6b9nqV0zWdM7CzpWQ',
            '已到访' => 'etlr_HBwAATMLTukk8hz-_TO2LQxFHug',
            '在读' => 'etlr_HBwAAmDjcZ02fBnLi2JPZzShh8g',
            '0-6月龄' => 'etlr_HBwAAWVJxS7fmq_D01KqledG1WA',
            '6-12月龄' => 'etlr_HBwAADp_qXtOP3B7zWfITYQKeLw',
            '12-18月龄' => 'etlr_HBwAAnT4mJ4803D0x9zDQgvkK5g',
            '18-36月龄' => 'etlr_HBwAAEYGrP3tyG1tHvDzHzv8Mnw',
            '36-48月龄' => 'etlr_HBwAAG-yc_BRnY1jsipp-9qMoXA',
            '48月龄+' => 'etlr_HBwAAu_4b8qqkBrEK0nJp9dAwPQ',
        ];

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $kddService = new \App\Services\Kdd\KddService();
        $nowDate = date("Y-m-d");
        $kehubiaoqianModels = KddKehubiaoqian::all();
        foreach ($kehubiaoqianModels as $model) {
            $month = $kddService->dateDiffForMonth($model->birthday, $nowDate);
            if ($month != $model->month) {
                $model->month = $month;
                $model->nianlingzhuangtai = 1;
                $model->save();
                echo "用户" .$model->name . "更新成功\n";
            }
        }

        echo "ok-" . time() . "\n";

        return Command::SUCCESS;
    }


}
