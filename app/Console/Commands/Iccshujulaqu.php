<?php

namespace App\Console\Commands;

use App\Models\BeisenBumen;
use App\Models\BeisenYuangong;
use App\Models\BeisenYuangongRenzhi;
use App\Models\BeisenZhiwu;
use App\Models\BeisenZhiji;
use App\Models\BeisenZhideng;
use App\Models\IccMendian;
use App\Models\IccMendianxiangqing;
use App\Models\IccRenwu;
use App\Models\IccRenwuResult;
use App\Services\BeisenApi\BeisenApiService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Library\EduConst;


class Iccshujulaqu extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:iccshujulaqu';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $microtime = microtime(true);

        $iccApiService = new \App\Services\Icc\IccApiService("1000066");

        $mendianModel = new IccMendian();
        $mengdianXiangqingModel = new IccMendianxiangqing();
        $renwuModel = new IccRenwu();
        $renwuJieguoModel = new IccRenwuResult();


        $storeFillable = $mendianModel->fillable;
        $mendianxiangqingFillable = $mengdianXiangqingModel->fillable;

        $storeList = $iccApiService->getStoreAll();
        $storeList = collect($storeList);
        $storeList->chunk(300)->each(function($items, $key) use ($iccApiService, $storeFillable, $mendianxiangqingFillable) {
            $rows = $items->toArray();

            foreach ($rows as $key=>$val) {
                $rows[$key]['tags'] = json_encode(($val['tags'] ?? []));
                $rows[$key]['fullTags'] = json_encode(($val['fullTags'] ?? []));
                $rows[$key]['businessDistrict'] = json_encode(($val['businessDistrict'] ?? []));

                foreach ($storeFillable as $fillKey) {
                    $rows[$key][$fillKey] = $rows[$key][$fillKey] ?? null;
                }
            }

            IccMendian::upsert(
                $rows,
                ['storeNo'],
                $storeFillable
            );

            // 门店详情
            foreach ($rows as $key=>$val) {
                if (!isset($val['storeNo']) || empty($val['storeNo'])) continue;

                $mendianxiangqing = $iccApiService->getStoreDetail(['storeNo' => $val['storeNo']]);

                $mendianxiangqing['wxUserIds'] = json_encode(($mendianxiangqing['wxUserIds'] ?? []));
                $mendianxiangqing['storeLabel'] = json_encode(($mendianxiangqing['storeLabel'] ?? []));
                $mendianxiangqing['businessDistrict'] = json_encode(($mendianxiangqing['businessDistrict'] ?? []));
                $mendianxiangqing['chatGroupIdPrefix'] = json_encode(($mendianxiangqing['chatGroupIdPrefix'] ?? []));
                $mendianxiangqing['chatGroupIds'] = json_encode(($mendianxiangqing['chatGroupIds'] ?? []));
                $mendianxiangqing['departmentPaths'] = json_encode(($mendianxiangqing['departmentPaths'] ?? []));

                IccMendianxiangqing::upsert(
                    $mendianxiangqing,
                    ['storeNo'],
                    $mendianxiangqingFillable
                );
            }

        });


        $renwuFillable = $renwuModel->fillable;
        $renwuJieguoFillable = $renwuJieguoModel->fillable;
        $taskList = $iccApiService->getTaskAll();
        $taskList = collect($taskList);

        $taskList->chunk(300)->each(function($items, $key) use ($iccApiService, $renwuFillable, $renwuJieguoFillable) {
            $rows = $items->toArray();

//            foreach ($rows as $key=>$val) {
//                $rows[$key]['tags'] = json_encode(($val['tags'] ?? []));
//                $rows[$key]['fullTags'] = json_encode(($val['fullTags'] ?? []));
//                $rows[$key]['businessDistrict'] = json_encode(($val['businessDistrict'] ?? []));
//
//                foreach ($storeFillable as $fillKey) {
//                    $rows[$key][$fillKey] = $rows[$key][$fillKey] ?? null;
//                }
//            }

            IccRenwu::upsert(
                $rows,
                ['taskId'],
                $renwuFillable
            );


            // 循环获取任务详情
            foreach ($rows as $key=>$val) {
                $renwu = $val;
                $renwujieguoList = $iccApiService->getTaskDetailAll(['taskId'=>$val['taskId']]);
                $renwujieguoList = collect($renwujieguoList);

                $renwujieguoList->chunk(100)->each(function ($jieguoItems) use($renwu, $renwuJieguoFillable) {
                    $jieguoItemsArr = $jieguoItems->toArray();

                    foreach ($jieguoItemsArr as $key=>$val) {
                        $jieguoItemsArr[$key]['taskId'] = $renwu['taskId'];
                        $jieguoItemsArr[$key]['storeNos'] = json_encode(($jieguoItemsArr[$key]['storeNos']??[]));
                    }

                    IccRenwuResult::upsert(
                        $jieguoItemsArr,
                        ['taskId', 'taskDate', 'wxUserId', 'externalUserId'],
                        $renwuJieguoFillable
                    );

                });
            }
        });

        
        IccRenwu::whereNotIn('taskId', $taskList->pluck('taskId'))->update(["shanchu" => 1]);


//        $zhijiCollect->chunk(300)->each(function($items, $key) use ($beisenZhijiFillable, $zhijiCollectByOid) {
//
//            $rows = $items->toArray();
//            foreach ($rows as $key=>$val) {
//                unset($rows[$key]['customProperties']);
//                unset($rows[$key]['translateProperties']);
//                unset($rows[$key]['sysMartionProperties']);
//            }
//
//            BeisenZhiji::upsert(
//                $rows,
//                ['objectId'],
//                $beisenZhijiFillable
//            );
//
////                DB::table('t_beisen_bumen')->insert($rows);
//        });


        echo "ok-" . time();

        return Command::SUCCESS;
    }
}
