<?php

namespace App\Console\Commands;



use AlibabaCloud\SDK\Dingtalk\Voauth2_1_0\Dingtalk;
use AlibabaCloud\SDK\Dingtalk\Voauth2_1_0\Models\GetAccessTokenRequest;
use AlibabaCloud\Tea\Exception\TeaError;
use AlibabaCloud\Tea\Utils\Utils;
use App\Models\BeisenBumen;
use App\Models\BeisenYuangong;
use App\Models\BeisenYuangongRenzhi;
use App\Models\BeisenZhiwu;
use App\Models\BeisenZhiji;
use App\Models\BeisenZhideng;
use App\Models\DdBumen;
use App\Models\DdYuangong;
use App\Models\IccMendian;
use App\Models\IccMendianxiangqing;
use App\Models\IccRenwu;
use App\Models\IccRenwuResult;
use App\Services\BeisenApi\BeisenApiService;
use App\Services\Dd\DdApiService;
use Darabonba\OpenApi\Models\Config;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Library\EduConst;

// 获取北森主数据
class Ddshujulaqu extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:ddshujulaqu';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';


    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $microtime = microtime(true);

        // include __DIR__ . "/../../../taobaosdk/TopSdk.php";

        $ddApiService = new DdApiService();
//        $accessToken = DdApiService::getAccessToken();

//        //仿照吴磊之前的 定时任务修改 -- 先注释了
        $deptList = $ddApiService->getAllDept();
        $deptCollect = collect($deptList);

        $ddBumen = new DdBumen();
        $ddBumenFillable = $ddBumen->fillable;

        $deptCollect->chunk(1)->each(function($items, $key) use($ddBumenFillable) {

            $rows = $items->toArray();
            foreach ($rows as $key=>$val) {
                $rows[$key]['shanchu'] = 0;
            }

            DdBumen::upsert(
                $rows,
                ['dept_id'],
                $ddBumenFillable
            );
        });

        DdBumen::whereNotIn("dept_id", $deptCollect->pluck("dept_id"))->update(['shanchu'=>1]);


        $jobNumberList = [];
//         $ddBumensModel = DdBumen::where('dept_id', '931049737')->get(); //all();
//         $ddBumensModel = DdBumen::where('dept_id', '931023751')->get(); //all();
//        print_r($ddBumensModel);
//die;
        $ddBumensModel = DdBumen::all();

        $ddYuangong = new DdYuangong();
        foreach ($ddBumensModel as $bumenRow) {
            $bumenUserResult = $ddApiService->getUserByBumenId($bumenRow['dept_id']);

//            print_r($bumenUserResult);die;

            if (empty($bumenUserResult)) continue;

            foreach ($bumenUserResult as $key=>$val) {
                $bumenUserResult[$key]['dept_id_list'] = json_encode(($val['dept_id_list'] ?? []));

                foreach ($ddYuangong->fillable as $k) {
                    if ($k == 'shanchu') continue;
                    if (!isset($bumenUserResult[$key][$k])
                        && !in_array($k, ['id']))  {
                        if (in_array($k, ['dept_order', 'hired_date', 'shanchu'])) {
                            $bumenUserResult[$key][$k] = 0;
                        } else {
                            $bumenUserResult[$key][$k] = "";
                        }
                    }
                }
                $bumenUserResult[$key]['shanchu'] = 0;

                $jobNumberList[] = $bumenUserResult[$key]['job_number'];
            }

            foreach ($bumenUserResult as $kk=>$vv) {
                $vv['disable_status'] = !empty($vv['disable_status']) ? $vv['disable_status'] : 0;
                DdYuangong::upsert(
                    [$vv],
                    ['userid'],
                    $ddYuangong->fillable
                );
            }

        }
        DdYuangong::whereNotIn('job_number', $jobNumberList)->update(['shanchu'=>1]);

        echo "ok-" . time();

        return Command::SUCCESS;
    }
}
