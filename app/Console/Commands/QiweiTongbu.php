<?php

namespace App\Console\Commands;

use App\Models\BeisenBumen;
use App\Models\BeisenYuangong;
use App\Models\Qiwei\QiweiBumen;
use App\Models\Qiwei\QiweiXunibumenyuangong;
use App\Models\Qiwei\QiweiYuangong;
use App\Services\BeisenApi\BeisenService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Query\Builder;

// 企微组织架构同步
class QiweiTongbu extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:qiweitongbu';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $microtime = microtime(true);

        echo "begin-" . time() . "\n";

        // select * from t_beisen_bumen where modifiedTime > '2024-09-01 00:00:00' or created_at >= '2024-09-01 00:00:00';
        $now = date("Y-m-d H:i:s");

        $beisenService = new BeisenService();
        $qiweiBumensModel = QiweiBumen::where("shanchu", 0)->orderByDesc('bumenchangmingcheng')->get();
        $qiweiBumensModelByChangmingcheng = $qiweiBumensModel->keyBy("bumenchangmingcheng");
        $qiweiBumensModelById = $qiweiBumensModel->keyBy("id");
        $qiweiBumensModelByBeisenOid = $qiweiBumensModel->where("beisenOid", "<>", "0")->keyBy("beisenOid");
        $beisenBumensModel = $beisenService->getBeisenBumenModel();
        $beisenBumensModel = $beisenBumensModel->whereNotIn("code", ['RootOrg', 'DefaultDept'])->get();
        $beisenBumensModelByOid = $beisenBumensModel->keyBy("oId");


        $baseId = 100000000;


        echo "部门新增修改\n";

        // 新增/修改
        foreach ($beisenBumensModel as $model) {

            // 屏蔽根目录
            if ($model['code'] == 'KID' || $model['pOIdOrgAdmin'] == '900611888') continue;

            $qiweiModel = null;
            $parentQishuiModel = null;

            // 企微部门model
            if (isset($qiweiBumensModelByBeisenOid[$model['oId']])) {
                $qiweiModel = $qiweiBumensModelByBeisenOid[$model['oId']];
            }
            if (!$qiweiModel && isset($qiweiBumensModelById[$model['oId'] + $baseId])) {
                $qiweiModel = $qiweiBumensModelById[$model['oId'] + $baseId];
            }

            // 北森上级部门model
            $parentModel = $beisenBumensModelByOid[$model['pOIdOrgAdmin']] ?? null;

            // 企微上级部门model
            if ($parentModel && isset($qiweiBumensModelByBeisenOid[$parentModel['oId']])) {
                $parentQishuiModel = $qiweiBumensModelByBeisenOid[$parentModel['oId']];
            }
            if ($parentModel && !$parentQishuiModel && isset($qiweiBumensModelById[$parentModel['oId'] + $baseId])) {
                $parentQishuiModel = $qiweiBumensModelById[$parentModel['oId'] + $baseId];
            }


            $data = [];

            // 新增
            if (!$qiweiModel) {

                $data['id'] = $model['oId'] + $baseId;
                $data['name'] = $model['name'];
                if ($model['pOIdOrgAdmin'] == 1640567) {
                    $data['parentid'] = 1;
                }
                if (!isset($data['parentid']) && $parentQishuiModel) {
                    $data['parentid'] = $parentQishuiModel['id'];
                }
                $data['op'] = '1';
            }

            // 修改
            if ($qiweiModel && $parentQishuiModel &&  ($qiweiModel['name'] != $model['name']
                                || $qiweiModel['parentid'] != $parentQishuiModel['id'])) {
                $data['id'] = $qiweiModel['id'];
                $data['name'] = $model['name'];
                if ($model['pOIdOrgAdmin'] == 1640567) {
                    $data['parentid'] = 1;
                }
                if (!isset($data['parentid']) && $parentQishuiModel) {
                    $data['parentid'] = $parentQishuiModel['id'];
                }
                $data['op'] = '2';
            }

            if (!empty($data)) {

                echo json_encode($data) . "\n";

                // 新增
                if ($data['op'] == 1) {

                }

                // 修改
                if ($data['op'] == 2) {

                }

            }
        }









        // 员工新增/修改

        // $date = date("Y-m-d H:i:s");

        $beisenYuangongsModel = $beisenService->getBeisenYuangongModel();
        $beisenYuangongsModel = $beisenYuangongsModel->whereNotIn('t_beisen_bumen.code', ['RootOrg', 'DefaultDept']);
        $beisenYuangongsModel = $beisenYuangongsModel->get();
        $beisenYuangongsGroupByJobNumber = $beisenYuangongsModel->groupBy("jobNumber");
        $qiweiYuangongsModel = QiweiYuangong::where('qiweishanchu', 0)->get();
        $qiweiYuangongsModelByJobNumber = $qiweiYuangongsModel->keyBy("userid");

        $qiweiXunibumenyuangongModel = QiweiXunibumenyuangong::all();
        $qiweiXunibumenyuangongModelByUserId = $qiweiXunibumenyuangongModel->keyBy('userid');


        // $beisenYuangongsModel->pluck('jobNumber')->toArray()


//        $i = 0;
        foreach ($beisenYuangongsGroupByJobNumber as $jobNumber => $rows) {

            if ($jobNumber == '0110220001') continue;
            if ($jobNumber == '000000') continue;

            $qiweiYuangongModel = $qiweiYuangongsModelByJobNumber[$jobNumber] ?? null;

            if (!empty($qiweiYuangongModel) && ($qiweiYuangongModel['enable'] == 0
                                                || $qiweiYuangongModel['shanchu'] == 1)) {
                continue;
            }


            $zhuzhanghaoModel = $rows->where('serviceType', 0)->first();


            $data = [];

            $data['userid'] = $zhuzhanghaoModel->jobNumber;
            $data['name'] = $zhuzhanghaoModel->name;
            $data['mobile'] = $zhuzhanghaoModel->mobilePhone;
            $data['position'] = $zhuzhanghaoModel->zhiwuname;
            if ($zhuzhanghaoModel->gender) {
                $data['gender'] = intval($zhuzhanghaoModel->gender) + 1;
            }

            if (isset($qiweiBumensModelByBeisenOid[$zhuzhanghaoModel->oIdDepartment])) {
                $data['main_department'] = $qiweiBumensModelByBeisenOid[$zhuzhanghaoModel->oIdDepartment]->id;
            } else {
                $data['main_department'] = $zhuzhanghaoModel->oIdDepartment + $baseId;
            }


            $data['department'] = [];
            $data['is_leader_in_dept'] = [];
            foreach ($rows as $row) {

                if (isset($qiweiBumensModelByBeisenOid[$row->oIdDepartment])) {
                    $data['department'][] = $qiweiBumensModelByBeisenOid[$row->oIdDepartment]->id;
                } else {
                    $data['department'][] = $row->oIdDepartment + $baseId;
                }

                if (isset($beisenBumensModelByOid[$row->oIdDepartment])) {
                    if ($beisenBumensModelByOid[$row->oIdDepartment]['personInCharge'] == $zhuzhanghaoModel->userId) {
                        $data['is_leader_in_dept'][] = 1;
                    } else {
                        $data['is_leader_in_dept'][] = 0;
                    }
                } else {
                    $data['is_leader_in_dept'][] = 0;
                }
            }

            // 追加虚拟部门
            if (isset($qiweiXunibumenyuangongModelByUserId[$zhuzhanghaoModel->jobNumber])) {
                $data['department'][] = $qiweiXunibumenyuangongModelByUserId[$zhuzhanghaoModel->jobNumber]['qiweibumenid'];
            }

            $data['enable'] = 1;




            // 新增
            if (!$qiweiYuangongModel) {

//                echo $zhuzhanghaoModel->jobNumber . "\n";

//                $i++;

                // echo "1";
                echo "员工新增：" . $zhuzhanghaoModel->name . " - " . $zhuzhanghaoModel->jobNumber . "\n";



                // 如果qiweishanchu 为1则改变原有状态

            }

            // $data['department']

            // 修改
            if ($qiweiYuangongModel
                && ($qiweiYuangongModel->name != $zhuzhanghaoModel->name
                    || $qiweiYuangongModel->main_department != $data['main_department']
                    || count(array_intersect($qiweiYuangongModel->department, $data['department'])) != count($qiweiYuangongModel->department)
                )) {

                // 主部门或其他任职部门有修改

                echo "员工修改：" . $zhuzhanghaoModel->name . " - " . $zhuzhanghaoModel->jobNumber .
                    ", " . $qiweiYuangongModel->name . " = " .$zhuzhanghaoModel->name .
                    ", " . $qiweiYuangongModel->main_department . " = " . $data['main_department'] .
                    ", " . implode("/", $qiweiYuangongModel->department) . " = " . implode("/", $data['department']) .
                    "\n";


                //
//                echo "2";

            }

        }


        // 员工删除
        $qiweiZaizhiYuangongsModel = $qiweiYuangongsModel->where('enable', 1);

        $qiweiZaizhiUserIds = $qiweiZaizhiYuangongsModel->pluck('userid')->toArray();

        $beisenLizhiYuangongModel = $beisenService->getBeisenLizhiYuangongModel();
        $beisenLizhiYuangongModel = $beisenLizhiYuangongModel->whereIn('jobNumber', $qiweiZaizhiUserIds);
        $beisenLizhiYuangongModel = $beisenLizhiYuangongModel->where('lastWorkDate', '>=', '2024-09-26 00:00:00')->get();

        foreach ($beisenLizhiYuangongModel as $model) {


            echo "员工删除：" . $model->jobNumber . "\n";

        }

        echo $beisenLizhiYuangongModel->count() . PHP_EOL;













        // 部门刪除
        echo "部门删除\n";
        foreach ($qiweiBumensModel as $model) {

            // 这些部门不做删除
            if (strstr($model['bumenchangmingcheng'], "虚拟部门") !== false
                || strstr($model['bumenchangmingcheng'], "合作伙伴") !== false
                || strstr($model['bumenchangmingcheng'], "台灣直營校") !== false
                || strstr($model['bumenchangmingcheng'], "台灣直營園") !== false
                || strstr($model['bumenchangmingcheng'], "台灣總公司") !== false
            ) {
                continue;
            }



            $bumenOid = $model['beisenOid'] ?? ($model['id'] - $baseId);

            if (!isset($beisenBumensModelByOid[$bumenOid])) {

                echo "删除部门：" . $model['name'] . " - " . $model['bumenchangmingcheng'] . "\n";

//                echo json_encode($model) . "\n";
            }
        }





        echo "ok-" . time() . "\n";

        return Command::SUCCESS;
    }


}
