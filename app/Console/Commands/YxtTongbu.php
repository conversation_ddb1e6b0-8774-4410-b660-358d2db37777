<?php

namespace App\Console\Commands;

use App\Models\BeisenBumen;
use App\Models\BeisenYuangong;
use App\Models\BeisenYuangongRenzhi;
use App\Models\BeisenZhiwu;
use App\Models\Jdb\YzjDept;
use App\Models\Qiwei\QiweiBumen;
use App\Models\Qiwei\QiweiYuangong;
use App\Models\Yxt\YxtBumenGuanlian;
use App\Services\BeisenApi\BeisenService;
use App\Services\Yxt\YxtApiService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Query\Builder;


// 企微组织架构同步
class YxtTongbu extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:yxttongbu';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // TODO 2024-10-20 日  部门删除没有操作其他已曹祖o

        $microtime = microtime(true);

        echo "begin-" . time() . "\n";

        $beisenService = new BeisenService();
        $yxtApiService = new YxtApiService();

        $beisenBumenListModel = $beisenService->getBeisenBumenModel();
        $beisenBumenListModelByChangmingcheng = $beisenBumenListModel->get()->keyBy('changmingcheng');
        $bumenGuanlianModel = new YxtBumenGuanlian();


        // 吉的堡教育集团_少儿事业群_大区一_上海4区_上海富荟广场校_行政部
        // 北森部门匹配云学堂部门
        echo "北森部门匹配云学堂部门\n";

        $guanlianList = [];
        $yzjDeptListModel = YzjDept::where('is_disable', 0)->get();
        foreach ($yzjDeptListModel as $yzjDept) {

            $changmingcheng = str_replace('\\','_', $yzjDept->deptLongName);
            $newChangmingcheng = "吉的堡教育集团_" . $changmingcheng;
            if ($newChangmingcheng == '吉的堡教育集团_上海吉的堡教育软件开发有限公司') $newChangmingcheng = '吉的堡教育集团';

            $guanlianList[] = [
                'yzjbumenid' => $yzjDept->orgId,
                'beisenbumenoid' => isset($beisenBumenListModelByChangmingcheng[$newChangmingcheng]) ? $beisenBumenListModelByChangmingcheng[$newChangmingcheng]['oId'] : '',
                'bumenchangmingcheng' => $newChangmingcheng,
            ];

            //echo $newChangmingcheng . "\n";
        }

        collect($guanlianList)->chunk(100)->each(function ($items) use ($bumenGuanlianModel) {
            $rows = $items->toArray();
            YxtBumenGuanlian::upsert(
                $rows,
                ['yzjbumenid'],
                $bumenGuanlianModel->fillable
            );
        });

        echo "匹配完成\n";




        echo "开始同步岗位\n";
        // 同步岗位
        $zhiwuListModel = BeisenZhiwu::all();
        $zhiwuBody = [];
        foreach ($zhiwuListModel as $model) {
            $zhiwuBody[] = [
                'pNames' => "吉的堡;" . $model->name,
                'pNo' => md5($model->name),
            ];
        }
        if (!empty($zhiwuBody)) {
            // 推送云学堂数据
            $rs = YxtApiService::syncPosition($zhiwuBody);
            echo json_encode($rs) . "\n";
            echo "\n";
        }

        echo "岗位同步完成\n";




        $bumenGuanlianListModel = YxtBumenGuanlian::all();
        $bumenGuanlianListModelByChangmingcheng = $bumenGuanlianListModel->keyBy('bumenchangmingcheng');
        $bumenGuanlianListModelByBeisenOid = $bumenGuanlianListModel->keyBy('beisenbumenoid');
        $beisenBumenListModelForYxtChuangjianXiugai = $beisenBumenListModel->where('modifiedTime', '>=', '2024-10-20 00:00:00')->get();

        // 同步部门
        echo "开始同步部门\n";
        $bumenDataList = [];
        $bumenShanchuDataList = [];
        foreach ($beisenBumenListModelForYxtChuangjianXiugai as $model) {

            if (in_array($model['code'], ['RootOrg', 'DefaultDept', 'KID'])) {
                continue;
            }

            // echo $model->code . "\n";
            $guanlian = $bumenGuanlianListModelByChangmingcheng[$model['changmingcheng']] ?? [];
            $parentGuanlian = $bumenGuanlianListModelByBeisenOid[$model['pOIdOrgAdmin']] ?? [];

            $data = [
                'id' => $guanlian['yzjbumenid'] ?? $model['oId'],
                'ouName' => $model['name'],
                'description' => '',
                'parentId' => $parentGuanlian['yzjbumenid'] ?? $model['pOIdOrgAdmin'],
            ];
            $bumenDataList[] = $data;

            echo json_encode($data) . "\n";
        }
        if (!empty($bumenDataList)) {
            // 执行部门增加
            $rs = YxtApiService::syncDept($bumenDataList);
            echo json_encode($rs) . "\n";
            echo "\n";
        }

        echo "同步部门完成\n";

        // 同步删除部门
        echo "开始同步删除部门\n";

        $beisenShanchusModel = BeisenBumen::whereNotIn('oId', $beisenBumenListModelByChangmingcheng->pluck('oId')->toArray())
            ->where('shanchu', 1)
            ->orderByDesc('pOIdOrgAdmin_TreePath')
            ->get();

        foreach ($beisenShanchusModel as $model) {
            $yxtBumenId = $bumenGuanlianListModelByBeisenOid[$model['oId']] ?? $model['oId'];

            if (isset($bumenGuanlianListModelByBeisenOid[$model['oId']])) {
                echo $bumenGuanlianListModelByBeisenOid[$model['oId']]['bumenchangmingcheng'] . "\n";
            } else {
                echo $model['changmingcheng'] . "\n";
            }
            echo "\n";
            $bumenShanchuDataList[] = $yxtBumenId;
        }
        if (!empty($bumenShanchuDataList)) {
            // 执行删除
            $rs = YxtApiService::syncDelDept($bumenShanchuDataList);
            echo json_encode($rs) . "\n";
            echo "\n";
        }

        echo "同步删除部门完成\n";






        // 员工添加
        echo "开始同步员工 \n";
        $beisenYuangongListModel = $beisenService->getBeisenYuangongModel();
        // 员工增加
        $beisenYuangongList = $beisenYuangongListModel->where('t_beisen_yuangong.latestEntryDate', ">=", '2024-10-20 00:00:00')->get();
        echo "增加员工数量：" . $beisenYuangongList->count() . "\n";
        $postData = [];
        foreach ($beisenYuangongList as $model) {
            echo $model->jobNumber . "\n";


            $guanlian = $bumenGuanlianListModelByBeisenOid[$model->oIdDepartment] ?? [];
            $orgId = $guanlian['yzjbumenid'] ?? $model->oIdDepartment;


            $updatePerson = [
                'id' => $model->jobNumber,
                'userName' => $model->jobNumber,
                'cnName' => $model->name,
                'password' => md5('Kidcastle20220128!@'),
                'encryptionType' => 'MD5',
                'sex' => ($model->gender == 0 ? "男" : "女"),
                'mobile' => $model->mobilePhone,
                'mail' => '',
                'orgOuCode' => $orgId, //$row->orgId,
                'postionNo' => ($model->zhiwuname ? md5($model->zhiwuname) : ''),
                'userNo' => $model->jobNumber,
                'entrytime' => explode(" ", $model->latestEntryDate)[0],
                'birthday' => explode(" ", $model->birthday)[0],
                'isEmailValidated' => 0,
                'isMobileValidated' => 1,
//                'spare1' => $row->ename
            ];

            $postData[] = $updatePerson;
        }

        if (!empty($postData)) {
            // 执行员工同步
            $rs = YxtApiService::syncUser($postData);
            echo json_encode($rs) . "\n";
            echo "\n";
        }


        $beisenYuangongLizhiList = BeisenYuangongRenzhi::whereNotIn('userID', $beisenYuangongListModel->pluck('t_beisen_yuangong.userId'))
            ->where('isCurrentRecord', 1)
            ->where('serviceType', 0)
            ->where('stdIsDeleted', 0)
            ->where('lastWorkDate', '>=', '2024-10-20 00:00:00')
            ->get();

        $delUser = [];
        foreach ($beisenYuangongLizhiList as $model) {
            $delUser[] = $model['jobNumber'];
            echo "删除员工：" . $model['jobNumber'] . "\n";
        }
        if (!empty($delUser)) {
            // 执行删除员工
            $rs = YxtApiService::syncDisableUser($delUser);
            echo json_encode($rs) . "\n";
            echo "\n";
        }

        // YxtApiService::syncDisableUser();

        echo "离职员工数量：" . $beisenYuangongLizhiList->count() . "\n";


        // $beisenYuangongListModel = $beisenService->getBeisenYuangongModel();




        echo "ok-" . time() . "\n";

        return Command::SUCCESS;

    }

}

