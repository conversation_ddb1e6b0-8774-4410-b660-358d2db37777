<?php
namespace App\Console\Commands;

use Illuminate\Console\Command;
use Library\EduConst;
use \App\Services\Fanwei\FanweiApiService;
use \App\Services\Fanwei\FanweiService;

// 泛微校区主管台账
class FanweiXiaoquzhuguantaizhang extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:fanweixiaoquzhuguantaizhang';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $microtime = microtime(true);

        $client = new \GuzzleHttp\Client();



        $fanweiApiService = new FanweiApiService();
        $fanweiService = new FanweiService();

        $taizhangList = $fanweiApiService->getXiaoquzhuguantaizhangAll();
        $beisentaizhangList = $fanweiService->getBeisenXiaoquzhuguan();



        echo json_encode($taizhangList);
        echo "\n\n";


         echo json_encode($beisentaizhangList);

        echo "\n\n";

//        exit();


        $taizhangCollect = collect($taizhangList);

        $beisentaizhangCollect = collect($beisentaizhangList);


        $beisentaizhangCollect = $beisentaizhangCollect->whereIn('yewuxitongmingcheng', ['校_校']);



        echo $taizhangCollect->count();
        echo "\n\n";
        echo $beisentaizhangCollect->count();
        echo "\n\n";


        $beisentaizhangCollect->chunk(100)->each(function($items, $key) use ($taizhangCollect, $fanweiApiService) {

            $table = [];
            foreach ($items as $item) {
                $existsRow = null;
                foreach($taizhangCollect as $taizhangRow) {
                    if ($taizhangRow['xxmc'] == $item['bumenid']) {
                        $existsRow = $taizhangRow;
                        break;
                    }
                }

//                var_dump($item['md5id']);
//                var_dump($existsRow);

                //* name: "南昌世纪校",
                //* md5id: "f80ea78d88d1df2faea07006833176a9",
                //* beisenid: "472e41ff-0621-4e04-8ff7-14e16e820dd4",
                //* xiaozhangUserId: 619268437
                //* quxiaozhangUserId: 619268700,
                //* dudaoUserId: 619268783,
                //* xiaozhangFanweiId: 4438,
                //* quxiaozhangFanweiId: 7639,
                //* dudaoFanweiId: 1195,

                $updateData = [
                    'operationinfo' => [
                        'operator' => \App\Services\Fanwei\FanweiApiService::$operator,
                    ],
                    "mainTable" => [
                        "xxmc"=> $item['bumenid'], //$item['md5id'], //"fe2917d7bd0c35e5509ea93689b513a9",
                        "xc"=> $item['xiaozhangFanweiId'], //"5367",
                        "qxc" => $item['quxiaozhangFanweiId'], //"5367",
                        "dd" => $item['dudaoFanweiId'], //"5367",
                        // 教学基地主管
                        "jxjdzg" => $item['xiaozhangFanweiId'],
                        // 教学校长
                        "jxxc" => $item['xiaozhangFanweiId'],
                        // 招生校长
                        "zsxc"=> $item['xiaozhangFanweiId'],
                        // 是否自动更新教学基地主管
                        "sfzdgxjxjdzg" => FanweiApiService::$xiaoquzhuguantaizhangShi, //"28",
                        // 是否自动更新教学校长
                        "sfzdgxjxxc" => FanweiApiService::$xiaoquzhuguantaizhangShi, //"28",
                        // 是否自动更新招生校长
                        "sfzdgxzsxc"=> FanweiApiService::$xiaoquzhuguantaizhangShi, //"28",
                    ]
                ];

                if ($existsRow) {
                    $updateData['mainTable']['id'] = $existsRow['id'];

                    if ($existsRow['sfzdgxjxjdzg'] != '是') {
                        $updateData['mainTable']['jxjdzg'] = $existsRow['jxjdzg'];
                    }

                    if ($existsRow['sfzdgxjxxc'] != '是') {
                        $updateData['mainTable']['jxxc'] = $existsRow['jxxc'];
                    }

                    if ($existsRow['sfzdgxzsxc'] != '是') {
                        $updateData['mainTable']['zsxc'] = $existsRow['zsxc'];
                    }

                    unset($updateData['mainTable']['sfzdgxjxjdzg']);
                    unset($updateData['mainTable']['sfzdgxjxxc']);
                    unset($updateData['mainTable']['sfzdgxzsxc']);
                }


                $table[] = $updateData;
            }


            $body = [
                'data' => $table,
            ];

            $rs = $fanweiApiService->updateXiaoquzhuguantaizhang($body);
            echo json_encode($rs);

            echo "\n\n";
        });




        // 删除历史
        $taizhangCollect->each(function($item, $key) use ($beisentaizhangCollect, $fanweiApiService) {

            $delete = true;
            foreach ($beisentaizhangCollect as $beisentaizhangRow) {
                if ($beisentaizhangRow['bumenid'] == $item['xxmc']) {
                    $delete = false;
                    break;
                }
            }
            if ($delete) {

                // 删除
                $body = [
                    'mainTable' => [
                        'id' => $item['id'],
                    ]
                ];
                $rs = $fanweiApiService->deleteXiaoquzhuguantaizhang($body);

                echo json_encode($rs);
                echo "\n\n";
            }
        });




        echo "ok-" . time();

        return Command::SUCCESS;
    }
}
