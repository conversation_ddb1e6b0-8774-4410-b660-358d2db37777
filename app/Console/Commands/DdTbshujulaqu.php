<?php

namespace App\Console\Commands;

use App\Models\Dd\TbDduserid;
use App\Models\Dd\TbXiangmurenwu;
use App\Models\Dd\TbXiangmurenwuMember;
use App\Models\Dd\TbXiangmurenwuTag;
use App\Models\DdBumen;
use App\Models\DdDakajilu;
use App\Models\DdKaoqinzu;
use App\Models\DdKaoqinzuYuangong;
use App\Models\DdYuangong;
use App\Models\Dd\TbXiangmu;
use App\Services\Dd\DdApiService;
use Illuminate\Console\Command;
use Library\EduConst;

// 获取北森主数据
class DdTbshujulaqu extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:ddtbshujulaqu';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';


    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $microtime = microtime(true);

//        include __DIR__ . "/../../../taobaosdk/TopSdk.php";

        echo time() . " begin\n";

       $tbApiService = new \App\Services\Dd\DdTbApiService();

        $i = 0;



        // 获取tb用户和钉钉用户关联1
        echo "获取tb用户和钉钉用户关联\n";
        $ddYuangongsModel = DdYuangong::where("shanchu", '0')->get();
        $ddYuangongsModel->chunk(50)->each(function($items, $key) use ($tbApiService) {

//            echo implode(",", $items->pluck('userid')->toArray()) . "\n";

            $tbUserIdResponse = $tbApiService->getTbUserId($items->pluck('userid')->toArray());

            if ($tbUserIdResponse['code'] == 200) {

                $tbUserIdList = $tbUserIdResponse['result'];
                $rows = $tbUserIdList;
                TbDduserid::upsert(
                    $rows,
                    ['dingtalkUserId', 'tbUserId'],
                    ['dingtalkUserId', 'tbUserId']
                );
            }
        });



        // 获取项目
        echo "获取项目\n";
        $tbXiangmuModel = new TbXiangmu();
        $tbXiangmuList = $tbApiService->getXiangmuAll();
        $tbXiangmuCollect = collect($tbXiangmuList);
        $tbXiangmuCollect->chunk(1)->each(function($items, $key) use ($tbApiService, $tbXiangmuModel) {
            $rows = $items->toArray();
            foreach ($rows as $key=>$val) {
                $rows[$key]['customfields'] = json_encode($rows[$key]['customfields'] ?? []);
            }
            TbXiangmu::upsert(
                    $rows,
                    ['id'],
                    $tbXiangmuModel->fillable
                );
        });
        TbXiangmu::whereNotIn('id', $tbXiangmuCollect->pluck('id')->toArray())->update(['shanchu'=>1]);

        // 获取项目任务
        echo "获取项目任务\n";
        $tbXiangmurenwuModel = new TbXiangmurenwu();
        $tbXiangmurenwuMemberModel = new TbXiangmurenwuMember();
        $tbxiangmurenwuTagModel = new TbXiangmurenwuTag();

        //
//        $tbXiangmusModel = TbXiangmu::where('id', '6707db6a7130d6b60d6363b0')->get(); //all();
        // 吉的堡专案管理总控表
        // $tbXiangmusModel = TbXiangmu::where('id', '67480c1b4ca487d08559a54b')->get(); //all();
        $tbXiangmusModel = TbXiangmu::all();


        foreach ($tbXiangmusModel as $xiangmuModel) {
            $xiangmuId = $xiangmuModel->id;

            $tbXiangmurenwuList = $tbApiService->getXiangmurenwuAll($xiangmuId);
            echo "项目ID：{$xiangmuId}\n";


            $tbXiangmurenwuCollect = collect($tbXiangmurenwuList);
            $tbXiangmurenwuCollectById = $tbXiangmurenwuCollect->keyBy('id');

//            $zidingyiidList = [];

            $tbXiangmurenwuCollect->chunk(1)->each(function($items, $key) use ($tbApiService, $tbXiangmurenwuModel, $tbXiangmurenwuMemberModel, $tbxiangmurenwuTagModel) {
                $rows = $items->toArray();
                foreach ($rows as $key=>$val) {


//                    foreach ($val as $k => $v) {
//                        echo $k . "-" . strlen($k) . "\n";
//                        if (strlen($k) == 24) {
//                            echo $val['projectId'] . " -- " . $k . "\n";
//                        }
//                    }

                    if ($val['content'] == '資金閉環') {
                        echo json_encode($val) . "\n";
//                        exit();
                    }


//                    $rows[$key]['6707dc78bb947285139350b6'] = '';
//                    $rows[$key]['6707ddfc9182bad257d564c5'] = '';
//                    $rows[$key]['6707de9215b8491f08b8b2a1'] = '';
//                    $rows[$key]['670dcf6daf067da79b714852'] = '';
//                    $rows[$key]['6707eb37a9e50dfb35616a40'] = '';


                    // 公司/园/校专案会议管理系统总控表
                    if ($val['projectId'] == '6707db6a7130d6b60d6363b0') {
                        $rows[$key]['extbumen'] = '';
                        $rows[$key]['exttonglu'] = '';
                        $rows[$key]['extguidangzhuangtai'] = '';
                        $rows[$key]['extrenwujindu'] = '';

                    }


                    if (isset($rows[$key]['customfields'])) {
                        $customfields = collect($rows[$key]['customfields']);

//                        echo json_encode($customfields) . "\n";


                        $customfieldsById = $customfields->keyBy('cfId');

//                        foreach ($customfieldsById as $k=>$v) {
//                            // echo $k . "-" . json_encode($v) . "\n";
//                            $zidingyiidList[$k] = json_decode(json_encode($v), true);
////                            echo json_encode($zidingyiidList);
////                            exit();
//                        }

                        // 公司/园/校专案会议管理系统总控表
                        // priority优先级就是星级，-10 3星，0 4星，1 5星，2 6星
                        // 670dcf6daf067da79b714852 部门
                        // 6707de9215b8491f08b8b2a1 通路
                        // 674d94060ebc0693ffd01aa7 归档状态
                        // 6707db6ad4bf472e8ed2a218 任务进度
                        // 67592e8a1198d780f17bb48d 序号
                        // 67592ec1a6f55c6127c5881a 交办次数


                        // 公司/园/校专案会议管理系统总控表
                        if ($val['projectId'] == '6707db6a7130d6b60d6363b0') {

                            $exttonglu = [];
                            if (isset($customfieldsById['6707de9215b8491f08b8b2a1']['value']) && !empty($customfieldsById['6707de9215b8491f08b8b2a1']['value'])) {
                                foreach ($customfieldsById['6707de9215b8491f08b8b2a1']['value'] as $tongluVal) {
                                    $exttonglu[] = $tongluVal['title'] ?? "";
                                        //$customfieldsById['6707de9215b8491f08b8b2a1']['value']

                                }
                            }


                            $rows[$key]['extbumen'] = $customfieldsById['670dcf6daf067da79b714852']['value'][0]['title'] ?? '';
                            $rows[$key]['exttonglu'] = implode("/", $exttonglu); // $customfieldsById['6707de9215b8491f08b8b2a1']['value'][0]['title'] ?? '';
                            $rows[$key]['extguidangzhuangtai'] = $customfieldsById['674d94060ebc0693ffd01aa7']['value'][0]['title'] ?? '';
                            $rows[$key]['extrenwujindu'] = $customfieldsById['6707db6ad4bf472e8ed2a218']['value'][0]['title'] ?? '';
                            $rows[$key]['extxuhao'] = $customfieldsById['67592e8a1198d780f17bb48d']['value'][0]['title'] ?? '';
                            $rows[$key]['extjiaobancishu'] = $customfieldsById['67592ec1a6f55c6127c5881a']['value'][0]['title'] ?? '';
                            //todo-97-补充字段
                            if(isset($customfieldsById['5ebdfef293d6d37a74f35a5e']['value'][0]['metaString']) && !empty($customfieldsById['5ebdfef293d6d37a74f35a5e']['value'][0]['metaString'])) {
                               // if ($customfieldsById['5ebdfef293d6d37a74f35a5e']['value'][0]['title'] && $customfieldsById['5ebdfef293d6d37a74f35a5e']['value'][0]['metaString']) {
                                    $metaarr = json_decode($customfieldsById['5ebdfef293d6d37a74f35a5e']['value'][0]['metaString'], true);
                                    $rows[$key]['jinzhanxiangqing'] = $metaarr['content'] ?? '';
                               // }
                            }
                        }

                    /*
                        $rows[$key]['6707dc78bb947285139350b6'] = $customfieldsById['6707dc78bb947285139350b6']['value'][0]['title'] ?? '';
                        $rows[$key]['6707ddfc9182bad257d564c5'] = $customfieldsById['6707ddfc9182bad257d564c5']['value'][0]['title'] ?? '';
                        $rows[$key]['6707de9215b8491f08b8b2a1'] = $customfieldsById['6707de9215b8491f08b8b2a1']['value'][0]['title'] ?? ''; // 通路
                        $rows[$key]['670dcf6daf067da79b714852'] = $customfieldsById['670dcf6daf067da79b714852']['value'][0]['title'] ?? ''; // 部门
                        $rows[$key]['6707eb37a9e50dfb35616a40'] = $customfieldsById['6707eb37a9e50dfb35616a40']['value'][0]['title'] ?? '';
                    */

                    }

                    // 关联任务人员
                    $involveMembers = $rows[$key]['involveMembers'] ?? [];
                    if ($involveMembers) {
                        $memberRows = [];
                        foreach ($involveMembers as $memberRow) {
                            $newRow = [
                                'projectId' => $rows[$key]['projectId'],
                                'taskId' => $rows[$key]['id'],
                                'memberId' => $memberRow,
                                'shanchu' => 0,
                            ];
                            $memberRows[] = $newRow;
                        }
                        TbXiangmurenwuMember::upsert(
                            $memberRows,
                            ['projectId', 'taskId', 'memberId'],
                            $tbXiangmurenwuMemberModel->fillable
                        );
                    }

                    TbXiangmurenwuMember::where('projectId', $rows[$key]['projectId'])
                        ->where('taskId', $rows[$key]['id'])
                        ->whereNotIn('memberId', $involveMembers)
                        ->update(['shanchu' => 1]);

                    // 关联项目标签
                    $tagIds = $rows[$key]['tagIds'] ?? [];
                    if ($tagIds) {
                        $tagRows = [];
                        foreach ($tagIds as $tagRow) {
                            $newRow = [
                                'projectId' => $rows[$key]['projectId'],
                                'taskId' => $rows[$key]['id'],
                                'tagId' => $tagRow,
                                'shanchu' => 0,
                            ];
                            $tagRows[] = $newRow;
                        }
                        TbXiangmurenwuTag::upsert(
                            $tagRows,
                            ['projectId', 'taskId', 'tagId'],
                            $tbxiangmurenwuTagModel->fillable
                        );
                    }

                    TbXiangmurenwuTag::where('projectId', $rows[$key]['projectId'])
                        ->where('taskId', $rows[$key]['id'])
                        ->whereNotIn('tagId', $tagIds)
                        ->update(['shanchu' => 1]);


                    $rows[$key]['customfields'] = json_encode($rows[$key]['customfields'] ?? []);
                    $rows[$key]['ancestorIds'] = json_encode($rows[$key]['ancestorIds'] ?? []);
                    $rows[$key]['involveMembers'] = json_encode($rows[$key]['involveMembers'] ?? []);
                    $rows[$key]['tagIds'] = json_encode($rows[$key]['tagIds'] ?? []);
                    $rows[$key]['recurrence'] = json_encode($rows[$key]['recurrence'] ?? []);
                }
                TbXiangmurenwu::upsert(
                    $rows,
                    ['id'],
                    $tbXiangmurenwuModel->fillable
                );
            });



//            echo json_encode($zidingyiidList) . "\n\n";


            TbXiangmurenwu::where('projectId', $xiangmuId)->whereNotIn('id', $tbXiangmurenwuCollect->pluck('id')->toArray())->update(['shanchu'=>1]);
        }





        echo "ok-" . time();

        return Command::SUCCESS;
    }
}
