<?php

namespace App\Console\Commands;


use App\Models\IccMendian;
use App\Models\Qiwei\QiweiPyqfabiaojilu;
use App\Models\Qiwei\QiweiPyqfabiaokejianfanwei;
use App\Models\Qiwei\QiweiPyqfabiaokejiankehu;
use App\Models\Qiwei\QiweiPyqhudong;
use App\Models\Qiwei\QiweiPyqquanbu;
use App\Models\Qiwei\QiweiQunfajilu;
use App\Models\Qiwei\QiweiQunfarenwujieguo;
use App\Models\Qiwei\QiweiQunfarenwuliebiao;
use App\Services\FanweiService;
use EasyWeChat\Work\Application;
use Illuminate\Console\Command;
use Library\EduConst;
use App\Facades\Fanwei;

// 测试矩阵同步
class QiweiPyqShujulaqu extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:qiweipyqshujulaqu';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $microtime = microtime(true);

        echo "begin-" . time() . "\n";


        $qiweiConfig = [
            'wxCorpId' => 'ww8977cb1b47249e00',   // 企业微信企业ID
            'wxAgentId' => '1000009',  // 企业微信应用ID
            'wxAgentSecret' => 'RpxeEXqoBspUnyxLS-nat6hsWgLU-Dqq8OqqAOPfJ9w',  // 企业微信应用秘钥
            'wxContactsSecret' => 'e7t6q01rNxU-HRcqDdPMFcLtzf6BhnlyjppZanyoW9w',   // 企业微信通讯录秘钥
            'wxToken' => 'easywechat',    // 企业微信token
            'wxAesKey' => '35d4687abb469072a29f1c242xx222113xxxx',   // 企业微信aesKey
        ];

        $readApp = new Application([
            'corp_id' => $qiweiConfig['wxCorpId'],
            'agent_id' => $qiweiConfig['wxAgentId'],
            'secret' => $qiweiConfig['wxAgentSecret'],
            'token' => $qiweiConfig['wxToken'],
            'aes_key' => $qiweiConfig['wxAesKey'],
            'http' => [
                'throw'  => true, // 状态码非 200、300 时是否抛出异常，默认为开启
                'timeout' => 5.0,
                'retry' => true, // 使用默认重试配置
            ]
        ]);

        $writeApp = new Application([
            'corp_id' => $qiweiConfig['wxCorpId'],
            // 'agent_id' => $weChatOptions['wxAgentId'],
            'secret' => $qiweiConfig['wxContactsSecret'],
            'token' => $qiweiConfig['wxToken'],
            'aes_key' => $qiweiConfig['wxAesKey'],
            'http' => [
                'throw'  => true, // 状态码非 200、300 时是否抛出异常，默认为开启
                'timeout' => 5.0,
                'retry' => true, // 使用默认重试配置
            ]
        ]);

        $readAppClient = $readApp->getClient();
        $writeAppClient = $writeApp->getClient();

        $time = time();

        $startTime = mktime(0, 0, 0, date("m", $time), date("d", $time)-7,   date("Y", $time));
        $endTime = $time;




        // 获取企业全部的发表列表
        $data = [
            'start_time' => $startTime,
            'end_time' => $endTime,
        ];

        $pyqData = [];
        $qiweiPyqquanbuModel = new QiweiPyqquanbu();
        $qiweiPyqquanbuFillable = $qiweiPyqquanbuModel->fillable;

        $tf = true;
        $i = 0;
        do {
            $i++;
            echo $i . "_获取企业全部的发表列表" . "\n";

            $response = $readAppClient->postJson('/cgi-bin/externalcontact/get_moment_list', $data);
            $responseData = $response->toArray();



            if ($responseData['errcode'] == 0) {
                if (empty($responseData['moment_list'])) {
                    $tf = false;
                } else {
                    $data['cursor'] = $responseData['next_cursor'] ?? '';

                    $momentList = $responseData['moment_list'];
                    foreach ($momentList as $k=>$v) {
                        $momentList[$k]['text'] = json_encode(($momentList[$k]['text'] ?? (object)[]));
                        $momentList[$k]['image'] = json_encode(($momentList[$k]['image'] ?? (object)[]));
                        $momentList[$k]['video'] = json_encode(($momentList[$k]['video'] ?? (object)[]));
                        $momentList[$k]['link'] = json_encode(($momentList[$k]['link'] ?? (object)[]));
                        $momentList[$k]['location'] = json_encode(($momentList[$k]['location'] ?? (object)[]));
                    }
                    $pyqData = array_merge($pyqData, $momentList);
                }
                if (empty($responseData['next_cursor'])) {
                    $tf = false;
                }
            } else {

                echo "朋友圈全部failed：" . json_encode($responseData) ."\n";
                $tf = false;
            }
        } while($tf && $i <= 100);

        if (!empty($pyqData)) {
            $pyqCollect = collect($pyqData);
            $pyqCollect->chunk(100)->each(function($items, $key) use($qiweiPyqquanbuFillable) {

                $rows = $items->toArray();


                $aa = QiweiPyqquanbu::upsert(
                    $rows,
                    ['moment_id'],
                    $qiweiPyqquanbuFillable
                );


            });
        }



        // 获取客户朋友圈企业发表的列表
        $fabiaoliebiaoData = [];
        $fabiaojiluModel = new QiweiPyqfabiaojilu();
        $fabiaojiluFillable = $fabiaojiluModel->fillable;
        $pyqsModel = QiweiPyqquanbu::where('create_type', 0)->where('create_time', '>=', $startTime)->where('create_time','<=', $endTime)->get();
        foreach ($pyqsModel as $pyqModel) {
            $data = [
                'moment_id' => $pyqModel['moment_id'],
                'limit' => 1000,
            ];

            //echo json_encode($data) . "\n\n";

            $tf = true;
            $i = 0;
            do {
                $i++;
                echo $i . "_获取客户朋友圈企业发表的列表" . "\n";

                $response = $readAppClient->postJson('/cgi-bin/externalcontact/get_moment_task?debug=1', $data);
                $responseData = $response->toArray();

                //echo json_encode($responseData). "\n";

                if ($responseData['errcode'] == 0) {
                    if (empty($responseData['task_list'])) {
                        $tf = false;
                    } else {
                        $data['cursor'] = $responseData['next_cursor'] ?? '';

                        $taskList = $responseData['task_list'];
                        foreach ($taskList as $k=>$v) {
                            $taskList[$k]['moment_id'] = $pyqModel['moment_id'];
                        }

                        $fabiaoliebiaoData = array_merge($fabiaoliebiaoData, $taskList);
                    }
                    if (empty($responseData['next_cursor'])) {
                        $tf = false;
                    }
                } else {

                    echo "朋友圈列表failed：" . json_encode($responseData) ."\n";

                    $tf = false;
                }

            } while($tf && $i <= 100);
        }

        $fabiaoliebiaoCollect = collect($fabiaoliebiaoData);
        $fabiaoliebiaoCollect->chunk(100)->each(function ($items, $key) use ($fabiaojiluFillable) {
            $rows = $items->toArray();

            QiweiPyqfabiaojilu::upsert(
                $rows,
                ['moment_id', 'userid'],
                $fabiaojiluFillable
            );
        });


        $pyqsModel = QiweiPyqquanbu::where('create_type', 1)->where('create_time', '>=', $startTime)->where('create_time','<=', $endTime)->get();
        $pyqfabiaojilusModel = QiweiPyqfabiaojilu::all();

        $jiluList = [];

        foreach ($pyqsModel as $row) {
            $jiluList[] = ['moment_id' => $row['moment_id'], 'userid' => $row['creator']];
        }
        foreach ($pyqfabiaojilusModel as $row) {
            $jiluList[] = ['moment_id' => $row['moment_id'], 'userid' => $row['userid']];
        }

        $jiluCollect = collect($jiluList);


        $kejianfanweiModel = new QiweiPyqfabiaokejianfanwei();
        $kejiankehuModel = new  QiweiPyqfabiaokejiankehu();
        $hudongModel = new QiweiPyqhudong();

        $kejianfanweiFillable = $kejianfanweiModel->fillable;
        $kejiankehuFillable = $kejiankehuModel->fillable;
        $hudongFillable = $hudongModel->fillable;

        $jiluCollect->each(function($items, $key) use ($readAppClient, $kejianfanweiFillable, $kejiankehuFillable, $hudongFillable) {

            $kejianfanweiData = [];
            $kejiankehuData = [];
            $hudongData = [];

            // 获取客户朋友圈发表时选择的可见范围

            $data = [
                'moment_id' => $items['moment_id'],
                'userid' => $items['userid'],
                'limit' => 1000,
            ];

//            echo json_encode($data) . "\n";


            $tf = true;
            $i = 0;
            do {
                $i++;
                echo $i . "_获取客户朋友圈发表时选择的可见范围" . "\n";

                $response = $readAppClient->postJson('/cgi-bin/externalcontact/get_moment_customer_list', $data);
                $responseData = $response->toArray();

                //echo json_encode($responseData). "\n";

                if ($responseData['errcode'] == 0) {
                    if (empty($responseData['customer_list'])) {
                        $tf = false;
                    } else {
                        $data['cursor'] = $responseData['next_cursor'] ?? '';

                        $taskList = $responseData['customer_list'];
                        foreach ($taskList as $k=>$v) {
                            $taskList[$k]['moment_id'] = $items['moment_id'];
                            $taskList[$k]['userid'] = $items['userid'];
                        }

                        $kejianfanweiData = array_merge($kejianfanweiData, $taskList);
                    }
                    if (empty($responseData['next_cursor'])) {
                        $tf = false;
                    }
                } else {

                    echo "朋友圈列表failed：" . json_encode($responseData) ."\n";

                    $tf = false;
                }

            } while($tf && $i <= 100);





            // 获取客户朋友圈发表后的可见客户列表
            $tf = true;
            $i = 0;
            do {
                $i++;
                echo $i . "_获取客户朋友圈发表后的可见客户列表" . "\n";

                $response = $readAppClient->postJson('/cgi-bin/externalcontact/get_moment_send_result', $data);
                $responseData = $response->toArray();

                //echo json_encode($responseData). "\n";

                if ($responseData['errcode'] == 0) {
                    if (empty($responseData['customer_list'])) {
                        $tf = false;
                    } else {
                        $data['cursor'] = $responseData['next_cursor'] ?? '';

                        $taskList = $responseData['customer_list'];
                        foreach ($taskList as $k=>$v) {
                            $taskList[$k]['moment_id'] = $items['moment_id'];
                            $taskList[$k]['userid'] = $items['userid'];
                        }

                        $kejiankehuData = array_merge($kejiankehuData, $taskList);
                    }
                    if (empty($responseData['next_cursor'])) {
                        $tf = false;
                    }
                } else {

                    echo "朋友圈列表failed：" . json_encode($responseData) ."\n";

                    $tf = false;
                }

            } while($tf && $i <= 100);



            // 获取客户朋友圈的互动数据
            $tf = true;
            $i = 0;
            do {
                $i++;
                echo $i . "_获取客户朋友圈的互动数据" . "\n";

                $response = $readAppClient->postJson('/cgi-bin/externalcontact/get_moment_comments', $data);
                $responseData = $response->toArray();

                // echo json_encode($responseData). "\n";

                if ($responseData['errcode'] == 0) {
                    if (empty($responseData['comment_list']) || empty($responseData['like_list'])) {
                        $tf = false;
                    } else {
                        $data['cursor'] = $responseData['next_cursor'] ?? '';

                        $newData = [
                            'moment_id' => $items['moment_id'],
                            'userid' => $items['userid'],
                            'comment_list' => json_encode(($responseData['comment_list'] ?? [])),
                            'like_list' => json_encode(($responseData['like_list'] ?? [])),
                        ];

                        $hudongList = [$newData];
                        $hudongData = array_merge($hudongData, $hudongList);
                    }
                    if (empty($responseData['next_cursor'])) {
                        $tf = false;
                    }
                } else {

                    echo "朋友圈列表failed：" . json_encode($responseData) ."\n";

                    $tf = false;
                }

            } while($tf && $i <= 100);



            if (!empty($kejianfanweiData)) {
                $kejianfanweiCollect = collect($kejianfanweiData);
                $kejianfanweiCollect->chunk(100)->each(function($items2, $key2) use ($kejianfanweiFillable) {
                    $rows = $items2->toArray();
                    QiweiPyqfabiaokejianfanwei::upsert(
                        $rows,
                        ['moment_id', 'userid', 'external_userid'],
                        $kejianfanweiFillable
                    );
                });
            }

            if (!empty($kejiankehuData)) {
                $kejiankehuCollect = collect($kejiankehuData);
                $kejiankehuCollect->chunk(100)->each(function($items2, $key2) use ($kejiankehuFillable) {
                    $rows = $items2->toArray();

                    QiweiPyqfabiaokejiankehu::upsert(
                        $rows,
                        ['moment_id', 'userid', 'external_userid'],
                        $kejiankehuFillable
                    );
                });
            }

            if (!empty($hudongData)) {
                $hudongCollect = collect($hudongData);
                $hudongCollect->chunk(100)->each(function($items2, $key2) use ($hudongFillable) {
                    $rows = $items2->toArray();
                    QiweiPyqhudong::upsert(
                        $rows,
                        ['moment_id', 'userid'],
                        $hudongFillable
                    );
                });
            }

        });







        echo "ok-" . time() . "\n";



        return Command::SUCCESS;
    }
}
