<?php

namespace App\Console\Commands;

use App\Models\Jdb\YzjDept;
use App\Models\Jdb\YzjPerson;
use App\Models\Jdb\YzjPersonJob;
use App\Models\YzjJueseTongbuEntry;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Library\EduConst;
use Illuminate\Support\Arr;


// 法务用印提醒
class YzjJueseTongbu extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:YzjJuesetongbu';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $microtime = microtime(true);

        $client = new \GuzzleHttp\Client();


        // 获取accessToken
        try {

            $response = $client->request('POST', 'https://yunzhijia.com/gateway/oauth2/token/getAccessToken', [
                    'form_params' => [
                        'timestamp' => intval($microtime * 1000),
                        'appId' => '500672631',
                        'secret' => '5KjqKaUlWH9tjmIRAPe59CQc2OsXgZpC',
                        'scope' => 'resGroupSecret',
                        'eid' => '17916472',
                    ]
                ]
            );

            $tokenContent = $response->getBody()->getContents();

            $tokenContent = json_decode($tokenContent, true);
            if (isset($tokenContent['success'])) {
                $accessToken = $tokenContent['data']['accessToken'];
            }

            if (empty($accessToken)) throw new \Exception('accessToken failed');

        } catch (Exception $e) {

//            Log::warning(json_encode([
//                'c' => 'YzjShenpiYongyintixing',
//                'a' => 'handle',
//                's' => 'getAccessToken',
//                'errMsg' => $e->getMessage(),
//                'errCode' => $e->getCode(),
//                'form_params' => [
//                    'timestamp' => intval($microtime * 1000),
//                    'appId' => '500672***',
//                    'secret' => '7QHu5vGdx488lBq9h***',
//                    'scope' => 'team',
//                    'eid' => '17916472',
//                ]
//            ]));
//
//            return Command::FAILURE;
        }

        // 获取全部标签
        $roleTagResponse = $client->request('POST', 'https://yunzhijia.com/gateway/openimport/open/roletag/getCompanyRoleTag?accessToken=' . $accessToken, [
            'headers' => ['Content-Type' => 'application/x-www-form-urlencoded'],
            'form_params' => [
                'eid' => '17916472',
                'data' => json_encode(['eid' => '17916472'])
            ]
        ]);

        $roleTagContent = $roleTagResponse->getBody()->getContents();
        $roleTagContent = json_decode($roleTagContent, true);

        $yzjRoleTagList = [];
        foreach ($roleTagContent['data'] as $row) {
            if (mb_stripos( $row['rolename'], '轻应用同步-') !== false) {
                $yzjRoleTagList[] = $row;
            }
        }

        $yzjRoleTagList = collect($yzjRoleTagList);
        $yzjRoleTagList = $yzjRoleTagList->keyBy('rolename');

        $yzjJuseTongbuVoList = \App\Models\YzjJueseTongbu::all();

        // 增加角色
        foreach ($yzjJuseTongbuVoList as $tongbuRow) {
            if (!isset($yzjRoleTagList['轻应用同步-' .  $tongbuRow['title']])) {
                $roleResponse = $client->request('POST', 'https://yunzhijia.com/gateway/openimport/open/roletag/addRoleTag?accessToken=' . $accessToken, [
                    'headers' => ['Content-Type' => 'application/x-www-form-urlencoded'],
                    'form_params' => [
                        'nonce'=> time() . mt_rand(10000,99999),
                        'eid' => '17916472',
                        'data' => json_encode(['eid' => '17916472', 'roleName' => '轻应用同步-' .  $tongbuRow['title']])
                    ]
                ]);

                $roleContent = $roleResponse->getBody()->getContents();
                $roleContent = json_decode($roleContent, true);

		//echo json_encode($roleContent);
		//exit();

                $tongbuRow['roleId'] = $roleContent['data']['id'];
                $tongbuRow->save();

                $yzjRoleTagList['轻应用同步-' .  $tongbuRow['title']] = [
                    'rolename' => '轻应用同步-' .  $tongbuRow['title'],
                    'id' => $roleContent['data']['id'],
                ];
            }
        }

//        echo json_encode($yzjRoleTagList);
//        echo "\n";
//        echo "\n";


        // 更新角色中的人员
        foreach ($yzjJuseTongbuVoList as $tongbuRow) {

            if (isset($yzjRoleTagList['轻应用同步-' .  $tongbuRow['title']])) {

                $yzjRoleId = $yzjRoleTagList['轻应用同步-' .  $tongbuRow['title']]['id'];

                $rolePersonResponse = $client->request('POST', 'https://yunzhijia.com/gateway/openimport/open/roletag/getPersonByRole?accessToken=' . $accessToken, [
                    'headers' => ['Content-Type' => 'application/x-www-form-urlencoded'],
                    'form_params' => [
                        'nonce'=> time() . mt_rand(10000,99999),
                        'eid' => '17916472',
                        'data' => json_encode(['eid' => '17916472', 'roleId' => $yzjRoleId])
                    ]
                ]);

                // {"success":true,"error":"","errorCode":100,"data":[{"orgIds":"","openId":"60e26896e4b01596045c9c38"}]}
                $rolePersonContent = $rolePersonResponse->getBody()->getContents();
                $rolePersonContent = json_decode($rolePersonContent, true);

                $jusePersonList = \App\Models\YzjJueseTongbu::userList($tongbuRow['id']);
                $jusePersonList = collect($jusePersonList);
                $personOrgs = [];
                // 增加人员
                foreach ($jusePersonList as $userRow) {
                    $newPersonOrg =  [
                        'openId' => $userRow['openId'],
                        'orgids' => [],
		    ];
		    if (isset($userRow['yewufanweiArr'])) {
                    foreach ($userRow['yewufanweiArr'] as $yewufanweiRow) {
                        $newPersonOrg['orgids'][] = $yewufanweiRow['orgId'];
		    }
		    }
                    $personOrgs[] = $newPersonOrg;
                }

                if (!empty($personOrgs)) {
                    $form_params = [
                        'nonce'=> time() . mt_rand(10000,99999),
                        'eid' => '17916472',
                        'data' => json_encode(['eid' => '17916472', 'operate' => 'insert', 'roleTags' => [
                            [
                                'roleId' => $yzjRoleId,
                                'personOrgs' => $personOrgs
                            ]
                        ]])
                    ];

                    $rolePersonUpdateResponse = $client->request('POST', 'https://yunzhijia.com/gateway/openimport/open/roletag/batchSetPersonRoleTag_other?accessToken=' . $accessToken, [
                        'headers' => ['Content-Type' => 'application/x-www-form-urlencoded'],
                        'form_params' =>$form_params
                    ]);

                    $rolePersonUpdateResponse = $rolePersonUpdateResponse->getBody()->getContents();
                }

                $jusePersonByOpenIdList = $jusePersonList->keyBy('openId');


                // 删除人员
                $personOrgs = [];
                foreach ($rolePersonContent['data'] as $yzjRolePersonRow) {
                    if (!isset($jusePersonByOpenIdList[$yzjRolePersonRow['openId']])) {
                        $newPersonOrg =  [
                            'openId' => $yzjRolePersonRow['openId'],
                            'orgids' => [],
                        ];
//                        foreach ($userRow['yewufanweiArr'] as $yewufanweiRow) {
//                            $newPersonOrg['orgids'][] = $yewufanweiRow['orgId'];
//                        }
                        $personOrgs[] = $newPersonOrg;
                    }
                }

                if (!empty($personOrgs)) {
                    $form_params = [
                        'nonce'=> time() . mt_rand(10000,99999),
                        'eid' => '17916472',
                        'data' => json_encode(['eid' => '17916472', 'operate' => 'delete', 'roleTags' => [
                            [
                                'roleId' => $yzjRoleId,
                                'personOrgs' => $personOrgs
                            ]
                        ]])
                    ];

                    $rolePersonDeleteResponse = $client->request('POST', 'https://yunzhijia.com/gateway/openimport/open/roletag/batchSetPersonRoleTag_other?accessToken=' . $accessToken, [
                        'headers' => ['Content-Type' => 'application/x-www-form-urlencoded'],
                        'form_params' =>$form_params
                    ]);

                    $rolePersonDeleteResponse = $rolePersonDeleteResponse->getBody()->getContents();
                }
            }
        }



        return Command::SUCCESS;
    }
}
