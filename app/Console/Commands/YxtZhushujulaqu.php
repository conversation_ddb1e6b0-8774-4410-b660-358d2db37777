<?php

namespace App\Console\Commands;

use App\Models\BeisenBumen;
use App\Models\BeisenYuangongRenzhi;
use App\Models\Qiwei\QiweiYuangong;
use App\Models\Qiwei\QiweiBumen;
use App\Models\Yxt\YxtBumen;
use App\Models\Yxt\YxtYuangong;
use Illuminate\Console\Command;
use Library\EduConst;

// 云学堂主数据拉取
class YxtZhushujulaqu extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:yxtzhushujulaqu';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $microtime = microtime(true);

        echo "begin-" . time() . "\n";

        $yxtBumenListRep = \App\Services\Yxt\YxtApiService::getBumenList();

        if ($yxtBumenListRep['code'] == 0) {

            $yxtBumenModel = new YxtBumen();

            echo count($yxtBumenListRep['data']);
            echo "\n\n";

            collect($yxtBumenListRep['data'])->chunk(100)->each(function($items) use ($yxtBumenModel) {
                $rows = $items->toArray();

                foreach ($rows as $row) {
                    if ($row['ouName'] == '测试部门') {
                        echo json_encode($row);
                    }
                }

//                echo json_encode($rows);
//                echo "\n\n\n";

                YxtBumen::upsert(
                    $rows,
                    ['id'],
                    $yxtBumenModel->fillable
                );
            });
        }

        $yxtYuangongListRep = \App\Services\Yxt\YxtApiService::getYuangongList();
        if ($yxtYuangongListRep['code'] == 0) {
            $yxtYuangongModel = new YxtYuangong();
            collect($yxtYuangongListRep['data'])->chunk(100)->each(function($items) use ($yxtYuangongModel) {
                $rows = $items->toArray();
                YxtYuangong::upsert(
                    $rows,
                    ['id'],
                    $yxtYuangongModel->fillable
                );
            });
        }



        echo "\n";



        echo "ok-" . time() . "\n";



        return Command::SUCCESS;

    }
}
