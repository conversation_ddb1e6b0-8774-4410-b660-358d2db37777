<?php

namespace App\Exports;

use App\Http\Resources\Yzj\YzjCaiwuyuan\XueshengtaizhangCollection;
use App\Models\Dtb\DtbJuese;
//use App\Models\User;
use App\Models\Jdb\YzjCaiwuyuanXueshengtaizhang;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Request;
use Maatwebsite\Excel\Concerns\FromCollection;

class UsersExport implements FromCollection
{
//    public function properties(): array
//    {
//        return [
//            'id'        => 'ID',
//            'cloud编码' => 'cloud编码',
////            'title'          => 'Invoices Export',
////            'description'    => 'Latest Invoices',
////            'subject'        => 'Invoices',
////            'keywords'       => 'invoices,export,spreadsheet',
////            'category'       => 'Invoices',
////            'manager'        => 'Patrick Brouwers',
////            'company'        => 'Maatwebsite',
//        ];
//    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        $list = YzjCaiwuyuanXueshengtaizhang::limit(100)->get();

        $list = $list->toarray();




        $xueshengtaizhangCollection = new XueshengtaizhangCollection($list);


        $xueshengtaizhangCollection = $xueshengtaizhangCollection->toArray();



        $columnsHeader = [];
        if (count($xueshengtaizhangCollection) > 0) {
//            echo json_encode($xueshengtaizhangCollection[0]);
//            exit();

            foreach ($xueshengtaizhangCollection[0] as $key=>$val) {
                foreach ($val as $key2=>$val2) {
                    $columnsHeader[$key2] = $key2;
                }
//                echo json_encode($val) . "<br />";
            }
        }



        array_unshift($xueshengtaizhangCollection, $columnsHeader);

        return new Collection($xueshengtaizhangCollection);


        //        echo json_encode($list);
//
//        exit();


        return new Collection($list);
//        echo json_encode($list);
//        exit();




        return $xueshengtaizhangCollection->additional(['meta' => [
            'sum' => 0, //sprintf("%.2f", array_sum($sumArr->toArray())),
            'sumCaizhengbutie' => 0, //sprintf("%.2f", array_sum($caizhengbutieArr->toArray())),
        ]]);



//        return new Collection([
//            [1,2,3],
//            [4,5,6],
//        ]);


//        return User::all();
    }
}
