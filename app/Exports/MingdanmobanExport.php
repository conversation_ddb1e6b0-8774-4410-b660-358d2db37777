<?php

namespace App\Exports;

use App\Services\CaiwuyuanYusuanService;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use PhpOffice\PhpSpreadsheet\Style\Border;
use \PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class MingdanmobanExport implements FromCollection, WithColumnFormatting, WithHeadings, WithStyles
{
    /**
     * 导出数据
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
//        $request = Request();
//        $yzjUser = $request->session()->get('yzjUser');
//
////        $quanxian = YzjCaiwuyuanShangchuanquanxian::getQuanxian($yzjUser['jobNo'], '预算');
//
//        $caiwuyuanYusuanService = new CaiwuyuanYusuanService();
//        $quanxian = $caiwuyuanYusuanService->getQuanxian($yzjUser['jobNo'], "预算");
//
//        $list = [];
//
//        if (isset($quanxian['jobNo']) && !empty($quanxian['jobNo'])) {
//
//            foreach ($quanxian['xiaoquList'] as $xiaoquRow) {
//
//                foreach ($xiaoquRow['yusuankemuList'] as $kemu) {
//                    $list[] = [
//                        'cloud编码' => (string)$xiaoquRow['code'],
//                        '园所名称' => (string)$xiaoquRow['title'],
//                        '项目类别' => $kemu['项目类别'],
//                        '项目类别编码' => $kemu['项目类别编码'],
//                        '预算项目' => $kemu['预算项目'],
//                        '预算项目编码' => $kemu['预算项目编码'],
//                        '预算数' => '',
//                        '备注' => '',
//                    ];
//                }
//            }
//        }
        $list = [];
        return new Collection($list);
    }

    // 校区编码格式
    public function columnFormats(): array
    {
        return [
            'A' => NumberFormat::FORMAT_TEXT,
            'B' => NumberFormat::FORMAT_TEXT,
        ];
    }

    public function styles(Worksheet $sheet)
    {
//        $sheet->getStyle('A1')->applyFromArray(
//            [
//                'font' => ['bold' => true, 'color'=>['rgb' => 'FF0000']],
//            ]
//        );
//        $sheet->getStyle('C1')->applyFromArray(
//            [
//                'font' => ['bold' => true, 'color'=>['rgb' => 'FF0000']],
//            ]
//        );
//        $sheet->getStyle('B1')->applyFromArray(
//            [
//                'font' => ['bold' => true, 'color'=>['rgb' => '0000FF']],
//                'borders' => [
//                    'bottom' => [
//                        'borderStyle' => Border::BORDER_THICK,
//                        'color' => [
//                            'rgb' => 'FF0000'
//                        ]
//                    ],
//                    'top' => [
//                        'borderStyle' => Border::BORDER_THICK,
//                        'color' => [
//                            'rgb' => 'FF0000'
//                        ]
//                    ],
//                    'left' => [
//                        'borderStyle' => Border::BORDER_THICK,
//                        'color' => [
//                            'rgb' => 'FF0000'
//                        ]
//                    ],
//                    'right' => [
//                        'borderStyle' => Border::BORDER_THICK,
//                        'color' => [
//                            'rgb' => 'FF0000'
//                        ]
//                    ]
//                ]
//            ]
//            );
//        $sheet->getStyle('D1')->applyFromArray(
//            [
//                'font' => ['bold' => true, 'color'=>['rgb' => '0000FF']],
//
//                'borders' => [
//                    'bottom' => [
//                        'borderStyle' => Border::BORDER_THICK,
//                        'color' => [
//                            'rgb' => 'FF0000'
//                        ]
//                    ],
//                    'top' => [
//                        'borderStyle' => Border::BORDER_THICK,
//                        'color' => [
//                            'rgb' => 'FF0000'
//                        ]
//                    ],
//                    'left' => [
//                        'borderStyle' => Border::BORDER_THICK,
//                        'color' => [
//                            'rgb' => 'FF0000'
//                        ]
//                    ],
//                    'right' => [
//                        'borderStyle' => Border::BORDER_THICK,
//                        'color' => [
//                            'rgb' => 'FF0000'
//                        ]
//                    ]
//                ]
//            ]
//        );
    }

    // 表头
    public function headings(): array
    {
        $head = [
//            [
//                "预算年度",
//                "!!!请输入!!!",
//                "月份",
//                "!!!请输入!!!"
//            ],
            [
                '名称' => '名称',
                '手机号' => '手机号',

            ],
        ];

        return $head;
    }

}
