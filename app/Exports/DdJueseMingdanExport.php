<?php

namespace App\Exports;

use App\Models\Caiwu\CaiwuYuangongxinchousuoding;
use App\Services\BeisenApi\BeisenService;
use App\Services\CaiwuyuanYusuanService;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use PhpOffice\PhpSpreadsheet\Style\Border;
use \PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

//FromCollection,
class DdJueseMingdanExport implements  WithColumnFormatting, WithHeadings, WithStyles
{
    /**
     * 导出数据
    * @return \Illuminate\Support\Collection
    */
//    public function collection()
//    {
//        $request = Request();
//
//        $rowtpl = [
//            '园所名称'=>'',
//            '员工编码'=>'',
//            '员工姓名'=>'',
//            '区域'=>'',
//            '职位名称'=>'',
//
//            '基本工资'=>'0',
//            '职位工资'=>'0',
//            '工龄工资'=>'0',
//            '学历补贴'=>'0',
//            '证书补贴'=>'0',
//            '英语等级补贴'=>'0',
//            '岗位津贴'=>'0',
//            '交通津贴'=>'0',
//            '住房津贴'=>'0',
//            '带园津贴'=>'0',
//            '其他津贴'=>'0',
//            '园内考核奖'=>'0',
//            '集团考核奖'=>'0',
//            '全勤奖'=>'0',
//            '满园奖金'=>'0',
//            '满班奖金'=>'0',
//            '人头奖金'=>'0',
//            '招生奖金'=>'0',
//            '地推奖金'=>'0',
//            '转介绍奖金'=>'0',
//            '转制奖金'=>'0',
//            '其他奖金或临时补贴'=>'0',
//            '兴趣班工资'=>'0',
//            '延时课工资'=>'0',
//            '标准计薪天数'=>'0',
//            '迟到天数'=>'0',
//            '事假天数'=>'0',
//            '病假天数'=>'0',
//            '薪酬考勤天数'=>'0',
//            '考勤扣款'=>'0',
//            '其他扣款'=>'0',
//            '应付工资'=>'0',
//            '社保个人'=>'0',
//            '公积金个人'=>'0',
//            '个税'=>'0',
//            '税后扣款'=>'0',
//            '实发工资'=>'0',
//            '备注'=>'',
//        ];
//
//        $xiaoqubianhao = $request->input('xiaoqubianhao', '');
//
//
//        $list = [];
//
//        if (!empty($xiaoqubianhao)) {
//
//            $userIds = [];
//            $beisenService = new BeisenService();
//
//            $yuangongsModel = $beisenService->getBeisenYuangongModel();
//            $bumensModel = $beisenService->getBeisenBumenModel();
//            $xiaoqubumenModel =  $beisenService->getBeisenBumenModel()->where('extyewuxitongbianma', $xiaoqubianhao)->first();
//            $xiaoqubumensModel = $bumensModel->where('changmingcheng', "like", $xiaoqubumenModel->changmingcheng . "%")->get();
//
//            if (!empty($xiaoqubumenModel) && !empty($xiaoqubumensModel)) {
//
//                $yuangongList = $yuangongsModel->whereIn('t_beisen_yuangong_renzhi.oIdDepartment', $xiaoqubumensModel->pluck('oId')->toArray())->get();
//
//                foreach ($yuangongList as $yuangongModel) {
//
//                    if (isset($userIds[$yuangongModel->jobNumber])) continue;
//
//                    $userIds[$yuangongModel->jobNumber] = 1;
//
//                    $newRowtpl = $rowtpl;
//
//                    $newRowtpl['园所名称'] = $xiaoqubumenModel->name;
//                    $newRowtpl['员工编码'] = $yuangongModel->jobNumber;
//                    $newRowtpl['员工姓名'] = $yuangongModel->name;
//                    $newRowtpl['区域'] = "";
//                    $newRowtpl['职位名称'] = $yuangongModel->zhiwuname;
//
//                    $list[] = $newRowtpl;
//                }
//            }
//        }
//
//
//
//        return new Collection($list);
//    }

    // 校区编码格式
    public function columnFormats(): array
    {
//        '核算年份'=>'核算年份',
//                '核算月份'=>'核算月份',
//                '系统编码'=>'系统编码',
//                // '编制单位'=>'编制单位',
//                '园所名称'=>'园所名称',
//                '员工编码'=>'员工编码',
//                '员工姓名'=>'员工姓名',
        return [
//            'A' => NumberFormat::FORMAT_TEXT,
//            'B' => NumberFormat::FORMAT_TEXT,
//            'C' => NumberFormat::FORMAT_TEXT,
//            'E' => NumberFormat::FORMAT_TEXT,
//            'F' => NumberFormat::FORMAT_TEXT,
        ];
    }

    public function styles(Worksheet $sheet)
    {
//        $sheet->getStyle('A1')->applyFromArray(
//            [
//                'font' => ['bold' => true, 'color'=>['rgb' => 'FF0000']],
//            ]
//        );
//        $sheet->getStyle('C1')->applyFromArray(
//            [
//                'font' => ['bold' => true, 'color'=>['rgb' => 'FF0000']],
//            ]
//        );
//        $sheet->getStyle('E1')->applyFromArray(
//            [
//                'font' => ['bold' => true, 'color'=>['rgb' => 'FF0000']],
//            ]
//        );
//        $sheet->getStyle('B1')->applyFromArray(
//            [
//                'font' => ['bold' => true, 'color'=>['rgb' => '0000FF']],
//                'borders' => [
//                    'bottom' => [
//                        'borderStyle' => Border::BORDER_THICK,
//                        'color' => [
//                            'rgb' => 'FF0000'
//                        ]
//                    ],
//                    'top' => [
//                        'borderStyle' => Border::BORDER_THICK,
//                        'color' => [
//                            'rgb' => 'FF0000'
//                        ]
//                    ],
//                    'left' => [
//                        'borderStyle' => Border::BORDER_THICK,
//                        'color' => [
//                            'rgb' => 'FF0000'
//                        ]
//                    ],
//                    'right' => [
//                        'borderStyle' => Border::BORDER_THICK,
//                        'color' => [
//                            'rgb' => 'FF0000'
//                        ]
//                    ]
//                ]
//            ]
//            );
//        $sheet->getStyle('D1')->applyFromArray(
//            [
//                'font' => ['bold' => true, 'color'=>['rgb' => '0000FF']],
//
//                'borders' => [
//                    'bottom' => [
//                        'borderStyle' => Border::BORDER_THICK,
//                        'color' => [
//                            'rgb' => 'FF0000'
//                        ]
//                    ],
//                    'top' => [
//                        'borderStyle' => Border::BORDER_THICK,
//                        'color' => [
//                            'rgb' => 'FF0000'
//                        ]
//                    ],
//                    'left' => [
//                        'borderStyle' => Border::BORDER_THICK,
//                        'color' => [
//                            'rgb' => 'FF0000'
//                        ]
//                    ],
//                    'right' => [
//                        'borderStyle' => Border::BORDER_THICK,
//                        'color' => [
//                            'rgb' => 'FF0000'
//                        ]
//                    ]
//                ]
//            ]
//        );
//        $sheet->getStyle('F1')->applyFromArray(
//            [
//                'font' => ['bold' => true, 'color'=>['rgb' => '0000FF']],
//
//                'borders' => [
//                    'bottom' => [
//                        'borderStyle' => Border::BORDER_THICK,
//                        'color' => [
//                            'rgb' => 'FF0000'
//                        ]
//                    ],
//                    'top' => [
//                        'borderStyle' => Border::BORDER_THICK,
//                        'color' => [
//                            'rgb' => 'FF0000'
//                        ]
//                    ],
//                    'left' => [
//                        'borderStyle' => Border::BORDER_THICK,
//                        'color' => [
//                            'rgb' => 'FF0000'
//                        ]
//                    ],
//                    'right' => [
//                        'borderStyle' => Border::BORDER_THICK,
//                        'color' => [
//                            'rgb' => 'FF0000'
//                        ]
//                    ]
//                ]
//            ]
//        );
    }

    // 表头
    public function headings(): array
    {
//        $request = Request();
//        $xiaoqubianhao = $request->input('xiaoqubianhao', '!!!请输入!!!');
//
//        $caiwuyuansuodingriqi = CaiwuYuangongxinchousuoding::select(['nianyue','nian', 'yue'])->where("yewutonglu", '园员工薪酬')->where('suodingrenId', "")->first();
//        $nian = '!!!请输入!!!';
//        $yue = '!!!请输入!!!';
//        if (!empty($caiwuyuansuodingriqi)) {
//            $nian = $caiwuyuansuodingriqi['nian'];
//            $yue = $caiwuyuansuodingriqi['yue'];
//        }

        $head = [
//            [
//                "请填写员工编号",
////                $nian,
////                "核算月份",
////                $yue,
////                "系统编码",
////                $xiaoqubianhao
//            ],
            [
                '员工编号'=>'员工编号',

//                '员工编码'=>'员工编码',
//                '员工姓名'=>'员工姓名',
//                '区域'=>'区域',
//                '职位名称'=>'职位名称',
//
//                '基本工资'=>'基本工资',
//                '职位工资'=>'职位工资',
//                '工龄工资'=>'工龄工资',
//                '学历补贴'=>'学历补贴',
//                '证书补贴'=>'证书补贴',
//                '英语等级补贴'=>'英语等级补贴',
//                '岗位津贴'=>'岗位津贴',
//                '交通津贴'=>'交通津贴',
//                '住房津贴'=>'住房津贴',
//                '带园津贴'=>'带园津贴',
//                '其他津贴'=>'其他津贴',
//                '园内考核奖'=>'园内考核奖',
//                '集团考核奖'=>'集团考核奖',
//                '全勤奖'=>'全勤奖',
//                '满园奖金'=>'满园奖金',
//                '满班奖金'=>'满班奖金',
//                '人头奖金'=>'人头奖金',
//                '招生奖金'=>'招生奖金',
//                '地推奖金'=>'地推奖金',
//                '转介绍奖金'=>'转介绍奖金',
//                '转制奖金'=>'转制奖金',
//                '其他奖金或临时补贴'=>'其他奖金或临时补贴',
//                '兴趣班工资'=>'兴趣班工资',
//                '延时课工资'=>'延时课工资',
//                '标准计薪天数'=>'标准计薪天数',
//                '迟到天数'=>'迟到天数',
//                '事假天数'=>'事假天数',
//                '病假天数'=>'病假天数',
//                '薪酬考勤天数'=>'薪酬考勤天数',
//                '考勤扣款'=>'考勤扣款',
//                '其他扣款'=>'其他扣款',
//                '应付工资'=>'应付工资',
//                '社保个人'=>'社保个人',
//                '公积金个人'=>'公积金个人',
//                '个税'=>'个税',
//                '税后扣款'=>'税后扣款',
//                '实发工资'=>'实发工资',
//                '备注'=>'备注',
            ],
        ];

        return $head;
    }

}
