<?php

namespace App\Exports;

use App\Http\Resources\Yzj\YzjCaiwuyuan\XueshengtaizhangCollection;
use App\Http\Resources\Yzj\YzjCaiwuyuan\XueshengtaizhangResource;
use App\Models\Jdb\YzjCaiwuyuanShangchuanquanxian;
use App\Models\Jdb\YzjCaiwuyuanXueshengtaizhang;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Request;
use Maatwebsite\Excel\Concerns\FromCollection;
use Illuminate\Database\Eloquent\Builder;

class XueshengtaizhangExport implements FromCollection
{

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        $request = Request();
        $yzjUser = $request->session()->get('yzjUser');
        $quanxian = YzjCaiwuyuanShangchuanquanxian::getQuanxian($yzjUser['jobNo']);



        $riqi = $request->input('riqi', "");
        $xiaoquList = $request->input('codeList', []);
        $年级 = $request->input('年级', []);
        $班级 = $request->input('班级', []);
        $幼儿姓名 = $request->input('幼儿姓名', '');
        $年 = $request->input('年');
        $月 = $request->input('月');
        $日 = $request->input('日');
        $收费组织 = $request->input('收费组织');
        $所属年份 = $request->input('所属年份');
        $所属学期 = $request->input('所属学期');


        $list = YzjCaiwuyuanXueshengtaizhang::where("id", "<>", "");

        if (empty($quanxian['list'])) {

            $list = $list->where('id','0');
        } else {
            $codeList = array_column($quanxian['list'], 'code');
            $list = $list->whereIn('cloud编码', $codeList);
        }
        if (!empty($xiaoquList)) {
            $list = $list->whereIn('cloud编码', $xiaoquList);
        }
        if (!empty($年级)) {
            $list = $list->whereIn('年级', $年级);
        }
        if (!empty($班级)) {
            $list = $list->whereIn('班级', $班级);
        }
        if (!empty($幼儿姓名)) {
            $list = $list->where('幼儿姓名', "LIKE" , "%{$幼儿姓名}%");
        }

//        if (!empty($年)) {
//            $list = $list->where('年', $年);
//        }
        if (!empty($年)) {

//            $list = $list->where('年', $年);


            $list = $list->where(function(Builder $query) use ($年){
                if (strlen($年) > 2) {
                    $query->where('年', substr($年, -2))->orWhere('年', $年);;
                } else {
                    $query->where('年', "20{$年}")->orWhere('年', $年);
                }
            });
        }
        if (!empty($月)) {
            $list = $list->where('月', $月);
        }
        if (!empty($日)) {
            $list = $list->where('日', $日);
        }
        if (!empty($收费组织)) {
            $list = $list->where('收费组织', $收费组织);
        }
        if (!empty($所属年份)) {
            $list = $list->where('费用所属年份', $所属年份);
        }
        if (!empty($所属学期)) {
            $list = $list->where('费用所属学期', $所属学期);
        }

        if ($riqi) {




            $startTime = strtotime($riqi[0]);
            $endTime = strtotime($riqi[1]);

            $startNian = substr(date("Y", $startTime), 2);
            $startYue = intval(date("m", $startTime));
            $startRi = intval(date("d", $startTime));

            $endNian = substr(date("Y", $endTime), 2);
            $endYue = intval(date("m", $endTime));
            $endRi = intval(date("d", $endTime));

//            $list = $list->whereBetween('年', [$startNian, $endNian]);
//            $list = $list->whereBetween('月', [$startYue, $endYue]);
//            $list = $list->whereBetween('日', [$startRi, $endRi]);

            $list = $list->where(function(Builder $query) use ($startNian, $endNian){
                $query->whereBetween('年', [$startNian, $endNian])->orWhereBetween('年', ["20{$startNian}", "20{$endNian}"]);
            });

            $list = $list->whereBetween('月', [$startYue, $endYue]);
            $list = $list->whereBetween('日', [$startRi, $endRi]);
        }

        $list = $list->limit(5000)->get();


//        var_dump($list);
//        exit();

//        $list = YzjCaiwuyuanXueshengtaizhang::limit(100)->get();

        $columnsHeader = [];
        $columns = new XueshengtaizhangResource($list[0]);
        $columns = json_decode($columns->toJson(), true);
        foreach ($columns as $key=>$val) {
            $columnsHeader[$key] = $key;
        }

        $list = $list->toArray();

        $xueshengtaizhangCollection = new XueshengtaizhangCollection($list);

        $xueshengtaizhangCollection = $xueshengtaizhangCollection->collection->toArray();


        array_unshift($xueshengtaizhangCollection, $columnsHeader);

        return new Collection($xueshengtaizhangCollection);
    }
}
