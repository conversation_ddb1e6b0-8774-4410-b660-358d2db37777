<?php

namespace App\Exports;

use App\Models\Jdb\YzjCaiwuyuanYusuan;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use \PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use Maatwebsite\Excel\Concerns\WithHeadings;

class CaiwuyuanYusuanXuanzhongExport implements FromCollection, WithColumnFormatting, WithHeadings
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        $request = Request();
        $yzjUser = $request->session()->get('yzjUser');
        $yusuanIds = $request->input("ids", []);
        $yusuanIds = explode(",", $yusuanIds);


        $yusuansModel = YzjCaiwuyuanYusuan::whereIn('id', $yusuanIds)->get();

        $list = [];
        foreach ($yusuansModel as $row) {
            $list[] = [
                'id' => $row['id'],
                'cloud编码' => $row['cloud编码'],
                '园所名称' => $row['园所名称'],
                '项目类别' => $row['项目类别'],
                '项目类别编码' => $row['项目类别编码'],
                '预算项目' => $row['预算项目'],
                '预算项目编码' => $row['预算项目编码'],
                '年' => $row['年'],
                '月' => $row['月'],
                '决算数' => 0,
                '备注' => '',
            ];
        }

        return new Collection($list);
    }

    public function columnFormats(): array
    {
        return [
            'A' => NumberFormat::FORMAT_TEXT,
            'B' => NumberFormat::FORMAT_TEXT,
        ];
    }

    // 表头
    public function headings(): array
    {
        $head = [
            [
                'id',
                'cloud编码' ,
                '园所名称' ,
                '项目类别',
                '项目类别编码',
                '预算项目' ,
                '预算项目编码',
                '年' ,
                '月' ,
                '决算数' ,
                '备注' ,
            ],
        ];

        return $head;
    }

}
