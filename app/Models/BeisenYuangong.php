<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class BeisenYuangong extends Model
{
    use HasFactory;

    protected $table = 't_beisen_yuangong';

    /**
     * 设置当前模型使用的数据库连接名。
     *
     * @var string
     */
    protected $connection = 'mysql';

    public $fillable = [

//        'id',

        'originalId',
        'userID',
        'name',
        '_Name',
        'phoneticOfXing',
        'phoneticOfMing',
        'gender',
        'email',
        'iDType',
        'iDNumber',
        'timeZone',
        'isLongTermCertificate',
        'certificateStartDate',
        'certificateValidityTerm',
        'birthday',
        'workDate',
        'homeAddress',
        'mobilePhone',
        'weiXin',
        'iDPhoto',
        'smallIDPhoto',
        'iDPortraitSide',
        'iDCountryEmblemSide',
        'allowToLoginIn',
        'personalHomepage',
        'speciality',
        'major',
        'postalCode',
        'passportNumber',
        'constellation',
        'bloodType',
        'residenceAddress',
        'joinPartyDate',
        'domicileType',
        'emergencyContact',
        'emergencyContactRelationship',
        'emergencyContactPhone',
        'qQ',
        'aboutMe',
        'homePhone',
        'graduateDate',
        'marryCategory',
        'politicalStatus',
        'nationality',
        'nation',
        'birthplace',
        'registAddress',
        'educationLevel',
        'lastSchool',
        'engName',
        'firstname',
        'lastname',
        'officeTel',
        'businessAddress',
        'backupMail',
        'applicantId',
        'applyIdV6',
        'applicantIdV6',
        'age',
        'businessModifiedBy',
        'businessModifiedTime',
        'sourceType',
        'firstEntryDate',
        'latestEntryDate',
        'tutorNew',
        'iDFront',
        'iDBehind',
        'preRetireDate',
        'actualRetireDate',
        'isConfirmRetireDate',
        'activationState',
        'issuingAuthority',
        'isDisabled',
        'disabledNumber',
        'orderCode',
        'objectId',
        'createdBy',
        'createdTime',
        'modifiedBy',
        'modifiedTime',
        'stdIsDeleted',

        'shanchu',
        'Name_en_US',

    ];

    // 员工任职
    public function renzhi(): HasMany
    {
        return $this->hasMany(BeisenYuangongRenzhi::class, 'userId', 'userId');
    }

}
