<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BeisenYuangongRenzhi extends Model
{
    use HasFactory;

    protected $table = 't_beisen_yuangong_renzhi';

    /**
     * 设置当前模型使用的数据库连接名。
     *
     * @var string
     */
    protected $connection = 'mysql';

    public $fillable = [

//        'id',

        'originalId',
        'userID',
        'pObjectDataID',
        'oIdDepartment',
        'startDate',
        'stopDate',
        'jobNumber',
        'entryDate',
        'lastWorkDate',
        'regularizationDate',
        'probation',
        'order',
        'employType',
        'serviceType',
        'serviceStatus',
        'approvalStatus',
        'employmentSource',
        'employmentForm',
        'isCharge',
        'oIdJobPost',
        'oIdJobSequence',
        'oIdProfessionalLine',
        'oIdJobPosition',
        'oIdJobLevel',
        'oidJobGrade',
        'place',
        'employeeStatus',
        'employmentType',
        'employmentChangeID',
        'changedStatus',
        'pOIdEmpAdmin',
        'pOIdEmpReserve2',
        'businessTypeOID',
        'changeTypeOID',
        'entryStatus',
        'isCurrentRecord',
        'lUOffer',
        'entryType',
        'staffID',
        'workYearBefore',
        'workYearGroupBefore',
        'workYearCompanyBefore',
        'workYearTotal',
        'workYearGroupTotal',
        'workYearCompanyTotal',
        'oIdOrganization',
        'whereabouts',
        'blackStaffDesc',
        'blackListAddReason',
        'transitionTypeOID',
        'changeReason',
        'probationResult',
        'probationActualStopDate',
        'probationStartDate',
        'probationStopDate',
        'isHaveProbation',
        'remarks',
        'addOrNotBlackList',
	'addBlackExpireDate',
        'businessModifiedBy',
        'businessModifiedTime',
        'traineeStartDate',
        'dimension1',
        'dimension2',
        'dimension3',
        'dimension4',
        'dimension5',
        'objectId',
        'createdBy',
        'createdTime',
        'modifiedBy',
        'modifiedTime',
        'stdIsDeleted',

        'extanquanjibie',
        'feiyongchengdanzuzhi',

        'handoverPerson',
        'workFlowProcessId'


    ];

}
