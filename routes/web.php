<?php

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Enums\Pt;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('test', [\App\Http\Controllers\LianController::class, 'index'])->name('lian');

// ------------- 97 调试部分  开始--------------
Route::match(['get'],'/api/jqtset/ddkaoqin', [\App\Http\Controllers\Api\JqTestController::class, 'ddkaoqin'])->name('api.jqtset.ddkaoqin');

// -------------- 97 调试部分 结束 --------------

// 钉钉登录跳转
//Route::prefix('auth')->group(function () {
//    Route::match(['get'],'dingding', '\App\Http\Controllers\Auth\DingdingController@index')->name('auth.dingding.index');
//});


Route::get('/auth/loginweixindingding/failedlogin', [\App\Http\Controllers\Auth\LoginWeixinDingdingController::class, 'failedlogin'])->name('auth.loginweixindingding.failedlogin');
Route::get('/auth/loginweixindingding/failedshipei', [\App\Http\Controllers\Auth\LoginWeixinDingdingController::class, 'failedshipei'])->name('auth.loginweixindingding.failedshipei');
Route::get('/auth/loginweixindingding/dingding', [\App\Http\Controllers\Auth\LoginWeixinDingdingController::class, 'dingding'])->name('auth.loginweixindingding.dingding');
Route::get('/auth/loginweixindingding/dingdingdev', [\App\Http\Controllers\Auth\LoginWeixinDingdingController::class, 'dingdingdev'])->name('auth.loginweixindingding.dingdingdev');

Route::get('/app/zongcaizhuanan', [\App\Http\Controllers\AppZongcaizhuanan\IndexController::class, 'index'])->name('app.zongcaizhuanan');




// 轻应用-科技中心-ok
//Route::match(['get'],'/app/kejizhongxin', [\App\Http\Controllers\IndexController::class, 'index'])->name('app.kejizhongxin');




// 钉钉微信免登中间件
Route::middleware('auth.weixindingding')->group(function () {
Route::match(['get'],'/app/kejizhongxin', [\App\Http\Controllers\IndexController::class, 'index'])->name('app.kejizhongxin');



//    Route::match(['get'],'/dashboard', '\App\Http\Controllers\IndexController@index')->name('dashboard');





    // 轻应用-数据上传
    Route::match(['get'],'/app/shujushangchuan', '\App\Http\Controllers\AppShujushangchuan\IndexController@index')->name('app.shujushangchuan');
    // 轻应用-园薪酬上传
    Route::match(['get'],'/app/yuanxinchoushangchuan', [\App\Http\Controllers\YzjCaiwuyuan\YuanxinchouController::class, 'index'])->name('app.yuanxinchoushangchuan');
    // 轻应用-开票信息
    Route::match(['get'],'/app/kaipiaoxinxi', '\App\Http\Controllers\AppKaipiaoxinxi\IndexController@index')->name('app.kaipiaoxinxi');



    // 轻应用-考勤数据上传
    Route::match(['get'],'/app/kaoqinshujushangchuan', [\App\Http\Controllers\YzjQiandaoHuichuanController::class, 'index'])->name('app.kaoqinshujushangchuan');
    Route::match(['get'],'/app/kaoqinshujushangchuan/m', [\App\Http\Controllers\M\YzjQiandaoHuichuanController::class, 'index'])->name('app.kaoqinshujushangchuan.m');



    // 轻应用-行政美丽校园上报（每周）
    Route::match(['get'],'/app/xingzhengshangchuan', [\App\Http\Controllers\YzjQiandaoHuichuanXingzhengController::class, 'index'])->name('app.xingzhengshangchuan');
    Route::match(['get'],'/app/xingzhengshangchuan/m', [\App\Http\Controllers\M\YzjQiandaoHuichuanXingzhengController::class, 'index'])->name('app.xingzhengshangchuan.m');






    // 轻应用-亲子外呼
    Route::match(['get'],'/app/qinziwaihu', [\App\Http\Controllers\AppQinziwaihu\IndexController::class, 'index'])->name('app.qinziwaihu');
    // 轻应用-TMK外呼
    Route::match(['get'],'/app/tmkwaihu', [\App\Http\Controllers\AppTmkwaihu\IndexController::class, 'index'])->name('app.tmkwaihu');
    // 轻应用-呼叫中心DEMO
    Route::match(['get'],'/app/hujiaozhongxin', [\App\Http\Controllers\AppHujiaozhongxin\IndexController::class, 'index'])->name('app.hujiaozhongxin');


    // 轻应用-单点-课叮铛校务系统-ok
    Route::match(['get'],'/app/kddxiaowu', [\App\Http\Controllers\Sso\KddController::class, 'index'])->name('app.kddxiaowu');


    Route::match(['get'],'/app/kddxiaowuto', [\App\Http\Controllers\Sso\KddController::class, 'general'])->name('app.kddxiaowuto');


    // 轻应用-单点-课叮铛园CRM-ok
    Route::match(['get'],'/app/kidkddcrm', [\App\Http\Controllers\Sso\KidKddController::class, 'index'])->name('app.kidkddcrm');
    // 轻应用-单点-课叮铛小红鸟-ok
    Route::match(['get'],'/app/kidkddredbird', [\App\Http\Controllers\Sso\KidKddController::class, 'kidkddredbird'])->name('app.kidkddredbird');
    // 轻应用-单点-课叮铛教学教务-ok
    Route::match(['get'],'/app/kddjiaoxuejiaowu', [\App\Http\Controllers\Sso\KddJiaoxuejiaowuController::class, 'index'])->name('app.kddjiaoxuejiaowu');
    // 轻应用-单点-地推堡（堡贝约课）-ok
    Route::match(['get'],'/app/dtb', [\App\Http\Controllers\Sso\DtbController::class, 'index'])->name('app.dtb');
    // 轻应用-单点-监控平台-ok
    Route::match(['get'],'/app/jiankongpingtai', [\App\Http\Controllers\AppJiankongpingtai\IndexController::class, 'index'])->name('app.jiankongpingtai');
    // 轻应用-单点-腾讯邮箱
    Route::match(['get'],'/app/txyouxiang', [\App\Http\Controllers\AppTxyouxiang\IndexController::class, 'index'])->name('app.txyouxiang');

    // 轻应用-单点-笃学
    Route::match(['get'],'/app/duxuebao', [\App\Http\Controllers\Sso\YxtController::class, 'index'])->name('app.duxuebao');
    Route::match(['get'],'/app/duxuebaoh5', [\App\Http\Controllers\Sso\YxtController::class, 'h5'])->name('app.duxuebaoh5');
    Route::match(['get'],'/app/duxuebao/callbackmsgjump', [\App\Http\Controllers\AppDuxuebao\CallbackController::class, 'msgjump'])->name('app.duxuebao.callback.msgjump');
    Route::match(['get'],'/app/duxuebao/qrcode/qrcodejump', [\App\Http\Controllers\AppDuxuebao\QrcodeController::class, 'qrcodejump'])->name('app.duxuebao.qrcode.qrcodejump');



    // 轻应用-单点-法务合同归档
    Route::match(['get'],'/app/fawuhetongguidang', [\App\Http\Controllers\YzjShenpiController::class, 'yongyinGrid'])->name('app.fawuhetongguidang');

    // 轻应用-单点-法务行事历
    Route::match(['get'],'/app/fawuxingshili', [\App\Http\Controllers\AppFawuxingshili\IndexController::class, 'index'])->name('app.fawuxingshili');

    // 轻应用-IT服务中心
    Route::match(['get'],'/app/itfuwuzhongxin', [\App\Http\Controllers\ItFuwuzhongxin\IndexController::class, 'index'])->name('app.itfuwuzhongxin');

    // 轻应用-市场检核表
    Route::match(['get'], '/app/shichangjianhe', [\App\Http\Controllers\Marketing\JianheController::class, 'index'])->name('app.shichangjianhe');

    // 轻应用-K3单点登录
    Route::match(['get'], '/app/k3', [\App\Http\Controllers\AppK3\IndexController::class, 'index'])->name('app.k3');

    // 轻应用-读书检核巡检
    Route::match(['get'], '/app/dushujianhexunjian', [\App\Http\Controllers\AppDushujianhexunjian\IndexController::class, 'index'])->name('app.dushujianhexunjian');
    // 轻应用-读书检核巡检 -- 台湾版本
    Route::match(['get'], '/app/dushujianhexunjiantw', [\App\Http\Controllers\AppDushujianhexunjian\IndexController::class, 'xunjiantw'])->name('app.dushujianhexunjiantw');
    // 轻应用- 考试测评系统
    Route::match(['get'], '/app/kaoshiceping', [\App\Http\Controllers\AppDushujianhexunjian\IndexController::class, 'kaoshiceping'])->name('app.kaoshiceping');


    // 轻应用-数据堡
    Route::match(['get'], '/app/shujubao', [\App\Http\Controllers\AppShujubao\IndexController::class, 'index'])->name('app.shujubao');

    // 轻应用-轻应用适配管理
    Route::match(['get'], '/app/ddshipei', [\App\Http\Controllers\AppDdshipei\IndexController::class, 'index'])->name('app.ddshipei');
    Route::match(['get'],'/app/ddshipei/list', [\App\Http\Controllers\AppDdshipei\IndexController::class, 'list'])->name('app.ddshipei.list');
    Route::match(['post'],'/app/ddshipei/store', [\App\Http\Controllers\AppDdshipei\IndexController::class, 'store'])->name('app.ddshipei.store');
    Route::match(['put'],'/app/ddshipei/update', [\App\Http\Controllers\AppDdshipei\IndexController::class, 'update'])->name('app.ddshipei.update');
    Route::match(['delete'],'/app/ddshipei/destroy', [\App\Http\Controllers\AppDdshipei\IndexController::class, 'destroy'])->name('app.ddshipei.destroy');

    //

//    // 单点登录
//    Route::match(['get'],'/sso/kdd', [\App\Http\Controllers\Sso\KddController::class, 'index'])->name('yzj.sso.kdd');
//    Route::match(['get'],'/sso/kddedu', [\App\Http\Controllers\Sso\KddEduController::class, 'index'])->name('yzj.sso.kddedu');
//    Route::match(['get'],'/sso/kidkdd', [\App\Http\Controllers\Sso\KidKddController::class, 'index'])->name('yzj.sso.kidkdd');
//    Route::match(['get'],'/sso/yxt', [\App\Http\Controllers\Sso\YxtController::class, 'index'])->name('yzj.sso.yxt');
//    Route::match(['get'],'/sso/yxth5', [\App\Http\Controllers\Sso\YxtController::class, 'h5'])->name('yzj.sso.yxth5');
//
//    Route::match(['get'],'/sso/dtb', [\App\Http\Controllers\Sso\DtbController::class, 'index'])->name('yzj.sso.dtb');
//    Route::match(['get'],'/sso/dtbtest', [\App\Http\Controllers\Sso\DtbController::class, 'test'])->name('yzj.sso.dtbtest');
//    Route::match(['get', 'post'],'/sso/jobnoencode', [\App\Http\Controllers\Sso\DtbController::class, 'jobNoEncode'])->name('yzj.sso.jobnoencode');
//    Route::match(['get'],'/sso/gy', [\App\Http\Controllers\Sso\GyController::class, 'index'])->name('yzj.sso.gy');





    // 轻应用-科技中心-财务-园员工薪酬
    Route::match(['get'],'/app/appcaiwu/yuanyuangongxinchou', [\App\Http\Controllers\AppCaiwu\YuanyuangongxinchouController::class, 'index'])->name('app.appcaiwu.yuanyuangongxinchou');
    Route::match(['get'],'/app/appcaiwu/yuanyuangongxinchou/list', [\App\Http\Controllers\AppCaiwu\YuanyuangongxinchouController::class, 'list'])->name('app.appcaiwu.yuanyuangongxinchou.list');
    Route::match(['post'],'/app/appcaiwu/yuanyuangongxinchou/store', [\App\Http\Controllers\AppCaiwu\YuanyuangongxinchouController::class, 'store'])->name('app.appcaiwu.yuanyuangongxinchou.store');
    Route::match(['put'],'/app/appcaiwu/yuanyuangongxinchou/update', [\App\Http\Controllers\AppCaiwu\YuanyuangongxinchouController::class, 'update'])->name('app.appcaiwu.yuanyuangongxinchou.update');
    Route::match(['delete'],'/app/appcaiwu/yuanyuangongxinchou/destroy', [\App\Http\Controllers\AppCaiwu\YuanyuangongxinchouController::class, 'destroy'])->name('app.appcaiwu.yuanyuangongxinchou.destroy');
    Route::match(['get'],'/app/appcaiwu/yuanyuangongxinchou/tpldownload', [\App\Http\Controllers\AppCaiwu\YuanyuangongxinchouController::class, 'tpldownload'])->name('app.appcaiwu.yuanyuangongxinchou.tpldownload');
    Route::match(['post'],'/app/appcaiwu/yuanyuangongxinchou/tplimport', [\App\Http\Controllers\AppCaiwu\YuanyuangongxinchouController::class, 'tplimport'])->name('app.appcaiwu.yuanyuangongxinchou.tplimport');

    Route::match(['get'],'/app/appcaiwu/yuanyuangongxinchousuoding/list', [\App\Http\Controllers\AppCaiwu\YuanyuangongxinchouController::class, 'suodingList'])->name('app.appcaiwu.yuanyuangongxinchousuoding.list');
    Route::match(['put'],'/app/appcaiwu/yuanyuangongxinchousuoding/update', [\App\Http\Controllers\AppCaiwu\YuanyuangongxinchouController::class, 'suodingUpdate'])->name('app.appcaiwu.yuanyuangongxinchousuoding.update');







// 轻应用-科技中心-dd-角色同步
    Route::match(['get'],'/app/kejizhongxin/dd/juese', [\App\Http\Controllers\AppKejizhongxin\Dd\Juese\IndexController::class, 'index'])->name('app.kejizhongxin.dd.juese');
    Route::match(['get'],'/app/kejizhongxin/dd/juese/list', [\App\Http\Controllers\AppKejizhongxin\Dd\Juese\IndexController::class, 'list'])->name('app.kejizhongxin.dd.juese.list');
    Route::match(['post'],'/app/kejizhongxin/dd/juese/store', [\App\Http\Controllers\AppKejizhongxin\Dd\Juese\IndexController::class, 'store'])->name('app.kejizhongxin.dd.juese.store');
    Route::match(['put'],'/app/kejizhongxin/dd/juese/{id}', [\App\Http\Controllers\AppKejizhongxin\Dd\Juese\IndexController::class, 'update'])->name('app.kejizhongxin.dd.juese.update');
    Route::match(['delete'],'/app/kejizhongxin/dd/juese/{id}', [\App\Http\Controllers\AppKejizhongxin\Dd\Juese\IndexController::class, 'destroy'])->name('app.kejizhongxin.dd.juese.destroy');
//Route::match(['get'],'/app/kejizhongxin/dd/juese/{id}/edit', [\App\Http\Controllers\AppKejizhongxin\Dd\Juese\IndexController::class, 'index'])->name('app.kejizhongxin.dd.juese');


// 轻应用-科技中心-dd-角色详情
    Route::match(['get'],'/app/kejizhongxin/dd/juesexiangqing/{id}/mingdan', [\App\Http\Controllers\AppKejizhongxin\Dd\Juese\XiangqingController::class, 'mingdan'])->name('app.kejizhongxin.dd.juesexiangqing.mingdan');
    Route::match(['get'],'/app/kejizhongxin/dd/juesexiangqing/{id}/mingdanlist', [\App\Http\Controllers\AppKejizhongxin\Dd\Juese\XiangqingController::class, 'mingdanlist'])->name('app.kejizhongxin.dd.juesexiangqing.mingdanlist');
    Route::match(['get'],'/app/kejizhongxin/dd/juesexiangqing/{id}/renyuanlist', [\App\Http\Controllers\AppKejizhongxin\Dd\Juese\XiangqingController::class, 'renyuanlist'])->name('app.kejizhongxin.dd.juesexiangqing.renyuanlist');
    Route::match(['get'],'/app/kejizhongxin/dd/juesexiangqing/{id}/renyuantpldown', [\App\Http\Controllers\AppKejizhongxin\Dd\Juese\XiangqingController::class, 'renyuantpldown'])->name('app.kejizhongxin.dd.juesexiangqing.renyuantpldown');
    Route::match(['get', 'post'],'/app/kejizhongxin/dd/juesexiangqing/{id}/renyuanupload', [\App\Http\Controllers\AppKejizhongxin\Dd\Juese\XiangqingController::class, 'renyuanupload'])->name('app.kejizhongxin.dd.juesexiangqing.renyuanupload');
    Route::match(['get', 'post'],'/app/kejizhongxin/dd/juesexiangqing/{id}/mingdanstore', [\App\Http\Controllers\AppKejizhongxin\Dd\Juese\XiangqingController::class, 'mingdanstore'])->name('app.kejizhongxin.dd.juesexiangqing.mingdanstore');
    Route::match(['get', 'post', 'delete'],'/app/kejizhongxin/dd/juesexiangqing/{id}/mingdandestroy', [\App\Http\Controllers\AppKejizhongxin\Dd\Juese\XiangqingController::class, 'mingdandestroy'])->name('app.kejizhongxin.dd.juesexiangqing.mingdandestroy');

// 轻应用-科技中心-dd-角色详情更新
    Route::match(['put'],'/app/kejizhongxin/dd/juesexiangqing/{id}', [\App\Http\Controllers\AppKejizhongxin\Dd\Juese\XiangqingController::class, 'update'])->name('app.kejizhongxin.dd.juesexiangqing.update');
// 轻应用-科技中心-dd-角色同步-条件编辑页面
    Route::match(['get'],'/app/kejizhongxin/dd/juesexiangqing/{id}/edit', [\App\Http\Controllers\AppKejizhongxin\Dd\Juese\XiangqingController::class, 'edit'])->name('app.kejizhongxin.dd.juesexiangqing');



// 轻应用-科技中心-dd-组织架构
    Route::match(['get'],'/app/kejizhongxin/dd/zuzhijiagou/bumen/list', [\App\Http\Controllers\AppKejizhongxin\Dd\Zuzhijiagou\BumenController::class, 'list'])->name('app.kejizhongxin.dd.zuzhijiagou.bumen.list');
    Route::match(['get'],'/app/kejizhongxin/dd/zuzhijiagou/bumen/tree', [\App\Http\Controllers\AppKejizhongxin\Dd\Zuzhijiagou\BumenController::class, 'tree'])->name('app.kejizhongxin.dd.zuzhijiagou.bumen.tree');
    Route::match(['get'],'/app/kejizhongxin/dd/zuzhijiagou/bumen/lookup', [\App\Http\Controllers\AppKejizhongxin\Dd\Zuzhijiagou\BumenController::class, 'lookup'])->name('app.kejizhongxin.dd.zuzhijiagou.bumen.lookup');

    Route::match(['get'],'/app/kejizhongxin/dd/zuzhijiagou/yuangong/list', [\App\Http\Controllers\AppKejizhongxin\Dd\Zuzhijiagou\YuangongController::class, 'list'])->name('app.kejizhongxin.dd.zuzhijiagou.yuangong.list');
    Route::match(['get'],'/app/kejizhongxin/dd/zuzhijiagou/yuangong/lookup', [\App\Http\Controllers\AppKejizhongxin\Dd\Zuzhijiagou\YuangongController::class, 'lookup'])->name('app.kejizhongxin.dd.zuzhijiagou.yuangong.lookup');
    Route::match(['get'],'/app/kejizhongxin/dd/zuzhijiagou/zhiwu/list', [\App\Http\Controllers\AppKejizhongxin\Dd\Zuzhijiagou\ZhiwuController::class, 'list'])->name('app.kejizhongxin.dd.zuzhijiagou.zhiwu.list');



    Route::match(['get'],'/app/kejizhongxin/dd/zuzhijiagou/bumen/list', [\App\Http\Controllers\AppKejizhongxin\Dd\Zuzhijiagou\BumenController::class, 'list'])->name('app.kejizhongxin.dd.zuzhijiagou.bumen.list');
    Route::match(['get'],'/app/kejizhongxin/dd/zuzhijiagou/bumen/tree', [\App\Http\Controllers\AppKejizhongxin\Dd\Zuzhijiagou\BumenController::class, 'tree'])->name('app.kejizhongxin.dd.zuzhijiagou.bumen.tree');
    Route::match(['get'],'/app/kejizhongxin/dd/zuzhijiagou/bumen/lookup', [\App\Http\Controllers\AppKejizhongxin\Dd\Zuzhijiagou\BumenController::class, 'lookup'])->name('app.kejizhongxin.dd.zuzhijiagou.bumen.lookup');









    // 轻应用-科技中心-企微欢迎语
    Route::match(['get'],'/app/kejizhongxin/qiweihuanyingyu', [\App\Http\Controllers\AppKejizhongxin\Qiweihuanyingyu\IndexController::class, 'index'])->name('app.kejizhongxin.qiweihuanyingyu.index');
    Route::match(['get'],'/app/kejizhongxin/qiweihuanyingyu/list', [\App\Http\Controllers\AppKejizhongxin\Qiweihuanyingyu\IndexController::class, 'list'])->name('app.kejizhongxin.qiweihuanyingyu.list');
    Route::match(['post'],'/app/kejizhongxin/qiweihuanyingyu/store', [\App\Http\Controllers\AppKejizhongxin\Qiweihuanyingyu\IndexController::class, 'store'])->name('app.kejizhongxin.qiweihuanyingyu.store');
    Route::match(['put'],'/app/kejizhongxin/qiweihuanyingyu/{id}', [\App\Http\Controllers\AppKejizhongxin\Qiweihuanyingyu\IndexController::class, 'update'])->name('app.kejizhongxin.qiweihuanyingyu.update');
    Route::match(['delete'],'/app/kejizhongxin/qiweihuanyingyu/{id}/destroy', [\App\Http\Controllers\AppKejizhongxin\Qiweihuanyingyu\IndexController::class, 'destroy'])->name('app.kejizhongxin.qiweihuanyingyu.destroy');
    Route::match(['get'],'/app/kejizhongxin/qiweihuanyingyu/huanyingyutuisonglist', [\App\Http\Controllers\AppKejizhongxin\Qiweihuanyingyu\IndexController::class, 'huanyingyutuisonglist'])->name('app.kejizhongxin.qiweihuanyingyu.huanyingyutuisonglist');











});














//Route::match(['get'],'/app/tb/sso', [\App\Http\Controllers\AppTb\SsoController::class, 'index'])->name('app.tb.sso');













// 轻应用-科技中心-钉钉-考勤补单
Route::match(['get'],'/app/kejizhongxin/beisen/kaoqin', [\App\Http\Controllers\AppKejizhongxin\Beisen\Kaoqin\IndexController::class, 'index'])->name('app.kejizhongxin.beisen.kaoqin');
Route::match(['get'],'/app/kejizhongxin/beisen/kaoqin/list', [\App\Http\Controllers\AppKejizhongxin\Beisen\Kaoqin\IndexController::class, 'list'])->name('app.kejizhongxin.beisen.kaoqin.list');


//// 轻应用-数据堡-页面
//Route::match(['get'], '/app/shujubao/ceogongzuotai', [\App\Http\Controllers\AppShujubao\CeoGongzuotaiController::class, 'index'])->name('app.shujubao.ceogongzuotai');



//Route::name("banji.")->prefix('banji')->group(function() {
//    Route::get('/', [\App\Http\Controllers\Jiaowu\BanjiController::class, 'index'])->name('index');
//    Route::get('/list', [\App\Http\Controllers\Jiaowu\BanjiController::class, 'list'])->name('list');
//    Route::post('/store', [\App\Http\Controllers\Jiaowu\BanjiController::class, 'store'])->name('store');
//    Route::put('/{id}', [\App\Http\Controllers\Jiaowu\BanjiController::class, 'update'])->name('update');
//    Route::delete('/{id}', [\App\Http\Controllers\Jiaowu\BanjiController::class, 'destroy'])->name('destroy');
//    Route::get('/{id}/edit', [\App\Http\Controllers\Jiaowu\BanjiController::class, 'edit'])->name('edit');
//});




// 轻应用-笃学堡-消息回调
Route::match(['post'],'/app/duxuebao/callback', [\App\Http\Controllers\AppDuxuebao\CallbackController::class, 'index'])->name('app.duxuebao.callback');



Route::match(['get'],'/app/duxuebao/qrcode', [\App\Http\Controllers\AppDuxuebao\QrcodeController::class, 'index'])->name('app.duxuebao.qrcode');





//Route::prefix('{pt}')->group(function () {
//
//    Route::match(['get'],'/dashboard', '\App\Http\Controllers\Wechat\DemoController@index')->name('dash123board');
//
//});





//Route::prefix('/hello')->group(function () {
//
//    Route::match(['get'],'/', '\App\Http\Controllers\DemoController@helloworld')->name('demo.helloworld');
//
//});

//Route::match(['get'],'/sso/kddjiaoxuejiaowu', [\App\Http\Controllers\Sso\KddJiaoxuejiaowuController::class, 'index'])->name('yzj.sso.kddjiaoxuejiaowu');




Route::middleware('authYunzhijia')->group(function () {


    // index
    Route::match(['get'],'/dashboard', '\App\Http\Controllers\IndexController@index')->name('dashboard');



    // 轻应用-短信发送计划
    Route::match(['get'], '/app/duanxinfasongjihua', [\App\Http\Controllers\AppDuanxinfasongjihua\IndexController::class, 'index'])->name('app.duanxinfasongjihua');
    Route::match(['get'],'/app/duanxinfasongjihua/list', [\App\Http\Controllers\AppDuanxinfasongjihua\IndexController::class, 'list'])->name('app.duanxinfasongjihua.list');
    Route::match(['post'],'/app/duanxinfasongjihua/store', [\App\Http\Controllers\AppDuanxinfasongjihua\IndexController::class, 'store'])->name('app.duanxinfasongjihua.store');
    Route::match(['put'],'/app/duanxinfasongjihua/update', [\App\Http\Controllers\AppDuanxinfasongjihua\IndexController::class, 'update'])->name('app.duanxinfasongjihua.update');
    Route::match(['delete'],'/app/duanxinfasongjihua/destroy', [\App\Http\Controllers\AppDuanxinfasongjihua\IndexController::class, 'destroy'])->name('app.duanxinfasongjihua.destroy');
    Route::match(['put'],'/app/duanxinfasongjihua/fasong', [\App\Http\Controllers\AppDuanxinfasongjihua\IndexController::class, 'fasong'])->name('app.duanxinfasongjihua.fasong');

    // 短信发送计划名单
    Route::match(['get'], '/app/duanxinfasongjihuamingdan/{id}', [\App\Http\Controllers\AppDuanxinfasongjihua\MingdanController::class, 'index'])->name('app.duanxinfasongjihuamingdan');
    Route::match(['get'],'/app/duanxinfasongjihuamingdan/{id}/list', [\App\Http\Controllers\AppDuanxinfasongjihua\MingdanController::class, 'list'])->name('app.duanxinfasongjihuamingdan.list');
    Route::match(['post'],'/app/duanxinfasongjihuamingdan/{id}/store', [\App\Http\Controllers\AppDuanxinfasongjihua\MingdanController::class, 'store'])->name('app.duanxinfasongjihuamingdan.store');
    Route::match(['put'],'/app/duanxinfasongjihuamingdan/{id}/update', [\App\Http\Controllers\AppDuanxinfasongjihua\MingdanController::class, 'update'])->name('app.duanxinfasongjihuamingdan.update');
    Route::match(['delete'],'/app/duanxinfasongjihuamingdan/{id}/destroy', [\App\Http\Controllers\AppDuanxinfasongjihua\MingdanController::class, 'destroy'])->name('app.duanxinfasongjihuamingdan.destroy');


    Route::match(['get'], '/app/duanxinfasongjihuamingdan/{id}/mingdanmobanexport', [\App\Http\Controllers\AppDuanxinfasongjihua\MingdanController::class, 'mingdanmobanExport'])->name('app.duanxinfasongjihuamingdan.mingdanmobanexport');
    Route::match(['post'], '/app/duanxinfasongjihuamingdan/{id}/mingdanmobanimport', [\App\Http\Controllers\AppDuanxinfasongjihua\MingdanController::class, 'mingdanmobanImport'])->name('app.duanxinfasongjihuamingdan.mingdanmobanimport');





//    Route::match(['get'],'/yzjjuesetongbu/juesetongbuuserlist/{tongbuId?}


//    // 轻应用-钉钉适配
//    Route::match(['get'], '/app/ddshipei', [\App\Http\Controllers\AppDdshipei\IndexController::class, 'index'])->name('app.ddshipei');
//    Route::match(['get'],'/app/ddshipei/list', [\App\Http\Controllers\AppDdshipei\IndexController::class, 'list'])->name('app.ddshipei.list');
//    Route::match(['post'],'/app/ddshipei/store', [\App\Http\Controllers\AppDdshipei\IndexController::class, 'store'])->name('app.ddshipei.store');
//    Route::match(['put'],'/app/ddshipei/update', [\App\Http\Controllers\AppDdshipei\IndexController::class, 'update'])->name('app.ddshipei.update');
//    Route::match(['delete'],'/app/ddshipei/destroy', [\App\Http\Controllers\AppDdshipei\IndexController::class, 'destroy'])->name('app.ddshipei.destroy');
//    Route::match(['get'],'/app/ddshipei/quanxianlist', [\App\Http\Controllers\AppDdshipei\IndexController::class, 'quanxianList'])->name('app.ddshipei.quanxianlist');




    // 轻应用-员工花名册
    Route::match(['get'], '/app/yuangonghuamingce', [\App\Http\Controllers\AppYuangonghuamingce\IndexController::class, 'index'])->name('app.yuangonghuamingce');
    Route::match(['get'],'/app/yuangonghuamingce/list', [\App\Http\Controllers\AppYuangonghuamingce\IndexController::class, 'list'])->name('app.yuangonghuamingce.list');
    Route::match(['post'],'/app/yuangonghuamingce/store', [\App\Http\Controllers\AppYuangonghuamingce\IndexController::class, 'store'])->name('app.yuangonghuamingce.store');
    Route::match(['put'],'/app/yuangonghuamingce/update', [\App\Http\Controllers\AppYuangonghuamingce\IndexController::class, 'update'])->name('app.yuangonghuamingce.update');
    Route::match(['delete'],'/app/yuangonghuamingce/destroy', [\App\Http\Controllers\AppYuangonghuamingce\IndexController::class, 'destroy'])->name('app.yuangonghuamingce.destroy');
    Route::match(['get'],'/app/yuangonghuamingce/quanxianlist', [\App\Http\Controllers\AppYuangonghuamingce\IndexController::class, 'quanxianList'])->name('app.yuangonghuamingce.quanxianlist');

//    Route::match(['post', 'delete', 'put'],'/yzjyxtbaoming/baomingstore', [\App\Http\Controllers\YzjYxtBaomingController::class, 'baomingStore'])->name('yzj.yxtbaoming.baomingstore');




    // 法务用印流程列表
    Route::match(['get'],'/yzjshenpi/yongyingrid', '\App\Http\Controllers\YzjShenpiController@yongyinGrid')->name('yzjshenpi.yongyingrid');
    Route::match(['get'],'/yzjshenpi/yongyinjson', '\App\Http\Controllers\YzjShenpiController@yongyinJson')->name('yzjshenpi.yongyinjson');

    // 部门与校区关联设置
    Route::match(['get'],'/yzjdept/index', '\App\Http\Controllers\YzjDeptController@index')->name('yzjdept.index');
    Route::match(['get'],'/yzjdept/deptlist', '\App\Http\Controllers\YzjDeptController@deptList')->name('yzjdept.deptList');
    Route::match(['post'],'/yzjdept/changeisschool', '\App\Http\Controllers\YzjDeptController@changeIsSchool')->name('yzjdept.changeisschool');
    Route::match(['post'],'/yzjdept/updateschoolcode', '\App\Http\Controllers\YzjDeptController@updateSchoolCode')->name('yzjdept.updateschoolcode');

    // 校财务考勤回传
    Route::get('/m/yzjqiandaohuichuan/index', [\App\Http\Controllers\M\YzjQiandaoHuichuanController::class, 'index'])->name('yzj.m.qiandaohuichuan');
    Route::post('/m/yzjqiandaohuichuan/store', [\App\Http\Controllers\M\YzjQiandaoHuichuanController::class, 'store'])->name('yzj.m.qiandaohuichuan.store');
    Route::match(['post', 'delete'],'/m/yzjqiandaohuichuan/picstore', [\App\Http\Controllers\M\YzjQiandaoHuichuanController::class, 'picstore'])->name('yzj.m.qiandaohuichuan.picstore');
    Route::match(['get'],'/m/yzjqiandaohuichuan/schoolclassdata', [\App\Http\Controllers\M\YzjQiandaoHuichuanController::class, 'schoolClassData'])->name('yzj.m.qiandaohuichuan.schoolclassdata');
    Route::get('/yzjqiandaohuichuan/index', [\App\Http\Controllers\YzjQiandaoHuichuanController::class, 'index'])->name('yzj.qiandaohuichuan');
    Route::get('/yzjqiandaohuichuan/list', [\App\Http\Controllers\YzjQiandaoHuichuanController::class, 'huichuanList'])->name('yzj.qiandaohuichuan.list');
    Route::match(['post', 'delete'],'/yzjqiandaohuichuan/store', [\App\Http\Controllers\YzjQiandaoHuichuanController::class, 'store'])->name('yzj.qiandaohuichuan.store');
    Route::match(['post', 'delete'],'/yzjqiandaohuichuan/picstore', [\App\Http\Controllers\YzjQiandaoHuichuanController::class, 'picstore'])->name('yzj.qiandaohuichuan.picstore');
    Route::match(['get'],'/yzjqiandaohuichuan/schoolclassdata', [\App\Http\Controllers\YzjQiandaoHuichuanController::class, 'schoolClassData'])->name('yzj.qiandaohuichuan.schoolclassdata');
    Route::match(['get'],'/yzjqiandaohuichuan/noquanxian', [\App\Http\Controllers\YzjQiandaoHuichuanController::class, 'noquanxian'])->name('yzj.qiandaohuichuan.noquanxian');


    // 行政回传
    Route::get('/yzjqiandaohuichuanxingzheng/index', [\App\Http\Controllers\YzjQiandaoHuichuanXingzhengController::class, 'index'])->name('yzj.qiandaohuichuanxingzheng');
    Route::get('/yzjqiandaohuichuanxingzheng/list', [\App\Http\Controllers\YzjQiandaoHuichuanXingzhengController::class, 'huichuanList'])->name('yzj.qiandaohuichuanxingzheng.list');
    Route::match(['get'],'/yzjqiandaohuichuanxingzheng/noquanxian', [\App\Http\Controllers\YzjQiandaoHuichuanXingzhengController::class, 'noquanxian'])->name('yzj.qiandaohuichuanxingzheng.noquanxian');
    Route::match(['get'],'/yzjqiandaohuichuanxingzheng/schoolclassdata', [\App\Http\Controllers\YzjQiandaoHuichuanXingzhengController::class, 'schoolClassData'])->name('yzj.qiandaohuichuanxingzheng.schoolclassdata');
    Route::match(['post', 'delete'],'/yzjqiandaohuichuanxingzheng/picstore', [\App\Http\Controllers\YzjQiandaoHuichuanXingzhengController::class, 'picstore'])->name('yzj.qiandaohuichuanxingzheng.picstore');
    Route::get('/m/yzjqiandaohuichuanxingzheng/index', [\App\Http\Controllers\M\YzjQiandaoHuichuanXingzhengController::class, 'index'])->name('yzj.m.qiandaohuichuanxingzheng');
    Route::post('/m/yzjqiandaohuichuanxingzheng/store', [\App\Http\Controllers\M\YzjQiandaoHuichuanXingzhengController::class, 'store'])->name('yzj.m.qiandaohuichuanxingzheng.store');
    Route::match(['post', 'delete'],'/m/yzjqiandaohuichuanxingzheng/picstore', [\App\Http\Controllers\M\YzjQiandaoHuichuanXingzhengController::class, 'picstore'])->name('yzj.m.qiandaohuichuanxingzheng.picstore');
    Route::match(['get'],'/m/yzjqiandaohuichuanxingzheng/schoolclassdata', [\App\Http\Controllers\M\YzjQiandaoHuichuanXingzhengController::class, 'schoolClassData'])->name('yzj.m.qiandaohuichuanxingzheng.schoolclassdata');



    // 单点登录
    Route::match(['get'],'/sso/kdd', [\App\Http\Controllers\Sso\KddController::class, 'index'])->name('yzj.sso.kdd');
    Route::match(['get'],'/sso/kddedu', [\App\Http\Controllers\Sso\KddEduController::class, 'index'])->name('yzj.sso.kddedu');
    //Route::match(['get'],'/sso/kddjiaoxuejiaowu', [\App\Http\Controllers\Sso\KddJiaoxuejiaowuController::class, 'index'])->name('yzj.sso.kddjiaoxuejiaowu');

    Route::match(['get'],'/sso/kidkdd', [\App\Http\Controllers\Sso\KidKddController::class, 'index'])->name('yzj.sso.kidkdd');
    Route::match(['get'],'/sso/yxt', [\App\Http\Controllers\Sso\YxtController::class, 'index'])->name('yzj.sso.yxt');
    Route::match(['get'],'/sso/yxth5', [\App\Http\Controllers\Sso\YxtController::class, 'h5'])->name('yzj.sso.yxth5');

    Route::match(['get'],'/sso/dtb', [\App\Http\Controllers\Sso\DtbController::class, 'index'])->name('yzj.sso.dtb');
    Route::match(['get'],'/sso/dtbtest', [\App\Http\Controllers\Sso\DtbController::class, 'test'])->name('yzj.sso.dtbtest');
    Route::match(['get', 'post'],'/sso/jobnoencode', [\App\Http\Controllers\Sso\DtbController::class, 'jobNoEncode'])->name('yzj.sso.jobnoencode');
    Route::match(['get'],'/sso/gy', [\App\Http\Controllers\Sso\GyController::class, 'index'])->name('yzj.sso.gy');




    // 云之家角色自动同步
    Route::match(['get'],'/yzjjuesetongbu/index', [\App\Http\Controllers\YzjJueseTongbuController::class, 'index'])->name('yzj.juesetongbu.index');
    Route::match(['get'],'/yzjjuesetongbu/juesetongbulist', [\App\Http\Controllers\YzjJueseTongbuController::class, 'juesetongbuList'])->name('yzj.juesetongbu.juesetongbulist');
    Route::match(['get'],'/yzjjuesetongbu/juesetongbu/{tongbuId?}', [\App\Http\Controllers\YzjJueseTongbuController::class, 'jueseTongbu'])->name('yzj.juesetongbu.juesetongbu');
    Route::match(['get'],'/yzjjuesetongbu/juesetongbuuserlist/{tongbuId?}', [\App\Http\Controllers\YzjJueseTongbuController::class, 'juesetongbuUserList'])->name('yzj.juesetongbu.juesetongbuuserlist');
    Route::match(['post', 'delete'],'/yzjjuesetongbu/juesetongbustore', [\App\Http\Controllers\YzjJueseTongbuController::class, 'jueseTongbuStore'])->name('yzj.juesetongbu.juesetongbustore');
    Route::match(['get'],'/yzjjuesetongbu/userlist', [\App\Http\Controllers\YzjJueseTongbuController::class, 'userList'])->name('yzj.juesetongbu.userlist');
    Route::match(['get'],'/yzjjuesetongbu/userlistlookup', [\App\Http\Controllers\YzjJueseTongbuController::class, 'userListLookUp'])->name('yzj.juesetongbu.userlistlookup');
    Route::match(['get'],'/yzjjuesetongbu/deptlist', [\App\Http\Controllers\YzjJueseTongbuController::class, 'deptList'])->name('yzj.juesetongbu.deptlist');
    Route::match(['post', 'delete'],'/yzjjuesetongbu/juesetongbuentrystore', [\App\Http\Controllers\YzjJueseTongbuController::class, 'jueseTongbuEntryStore'])->name('yzj.juesetongbu.juesetongbuentrystore');



    // 云学堂报名
    Route::match(['get'],'/yzjyxtbaoming/index', [\App\Http\Controllers\YzjYxtBaomingController::class, 'index'])->name('yzj.yxtbaoming.index');
    Route::match(['get'],'/yzjyxtbaoming/baoming/{baomingId?}', [\App\Http\Controllers\YzjYxtBaomingController::class, 'baoming'])->name('yzj.yxtbaoming.baoming');
    Route::match(['get'],'/yzjyxtbaoming/baominglist', [\App\Http\Controllers\YzjYxtBaomingController::class, 'baomingList'])->name('yzj.yxtbaoming.baominglist');
    Route::match(['post', 'delete', 'put'],'/yzjyxtbaoming/baomingstore', [\App\Http\Controllers\YzjYxtBaomingController::class, 'baomingStore'])->name('yzj.yxtbaoming.baomingstore');
    Route::match(['get'],'/yzjyxtbaoming/yxtbaoming', [\App\Http\Controllers\YzjYxtBaomingController::class, 'yxtBaoming'])->name('yzj.yxtbaoming.yxtbaoming');
    Route::match(['get'],'/yzjyxtbaoming/yxtJihualist', [\App\Http\Controllers\YzjYxtBaomingController::class, 'yxtJihuaList'])->name('yzj.yxtbaoming.yxtjihualist');
    Route::match(['post', 'delete'],'/yzjyxtbaoming/yxtbaomingstore', [\App\Http\Controllers\YzjYxtBaomingController::class, 'yxtBaomingStore'])->name('yzj.yxtbaoming.yxtbaomingstore');
    Route::match(['get'],'/m/yzjyxtbaoming/yxtbaoming', [\App\Http\Controllers\M\YzjYxtBaomingController::class, 'yxtBaoming'])->name('yzj.m.yxtbaoming.yxtbaoming');
    Route::match(['get'],'/m/yzjyxtbaoming/yxtJihualist', [\App\Http\Controllers\M\YzjYxtBaomingController::class, 'yxtJihuaList'])->name('yzj.m.yxtbaoming.yxtjihualist');
    Route::match(['post', 'delete'],'/m/yzjyxtbaoming/yxtbaomingstore', [\App\Http\Controllers\M\YzjYxtBaomingController::class, 'yxtBaomingStore'])->name('yzj.m.yxtbaoming.yxtbaomingstore');


    // 地推堡-角色
    Route::match(['get'],'/dtb/juese/index', [\App\Http\Controllers\Dtb\JueseController::class, 'index'])->name('dtb.juese.index');
    Route::match(['get'],'/dtb/juese/jueselist', [\App\Http\Controllers\Dtb\JueseController::class, 'jueseList'])->name('dtb.juese.jueselist');
    Route::match(['post', 'delete', 'put'],'/dtb/juese/store', [\App\Http\Controllers\Dtb\JueseController::class, 'store'])->name('dtb.juese.store');
    Route::match(['get'],'/dtb/juese/personlist', [\App\Http\Controllers\Dtb\JueseController::class, 'personList'])->name('dtb.juese.personlist');


    // 市场广告线下投放管理
    Route::match(['get'],'/marketing/xianxiatoufang/index', [\App\Http\Controllers\Marketing\XianxiatoufangController::class, 'index'])->name('marketing.xianxiatoufang.index');
    Route::match(['get'],'/marketing/xianxiatoufang/gongyingshanglist', [\App\Http\Controllers\Marketing\XianxiatoufangController::class, 'gongyingshangList'])->name('marketing.xianxiatoufang.gongyingshanglist');
    Route::match(['post', 'delete', 'put'],'/marketing/xianxiatoufang/gongyingshangstore', [\App\Http\Controllers\Marketing\XianxiatoufangController::class, 'gongyingshangStore'])->name('marketing.xianxiatoufang.gongyingshangstore');
    Route::match(['get'],'/marketing/xianxiatoufang/guanggaoxingzhilist', [\App\Http\Controllers\Marketing\XianxiatoufangController::class, 'guanggaoxingzhiList'])->name('marketing.xianxiatoufang.guanggaoxingzhilist');
    Route::match(['post', 'delete', 'put'],'/marketing/xianxiatoufang/guanggaoxingzhistore', [\App\Http\Controllers\Marketing\XianxiatoufangController::class, 'guanggaoxingzhiStore'])->name('marketing.xianxiatoufang.guanggaoxingzhistore');
    Route::match(['get'],'/marketing/xianxiatoufang/toufangxingshilist', [\App\Http\Controllers\Marketing\XianxiatoufangController::class, 'toufangxingshiList'])->name('marketing.xianxiatoufang.toufangxingshilist');
    Route::match(['post', 'delete', 'put'],'/marketing/xianxiatoufang/toufangxingshistore', [\App\Http\Controllers\Marketing\XianxiatoufangController::class, 'toufangxingshiStore'])->name('marketing.xianxiatoufang.toufangxingshistore');
    Route::match(['get'],'/marketing/xianxiatoufang/huamianzhutilist', [\App\Http\Controllers\Marketing\XianxiatoufangController::class, 'huamianzhutiList'])->name('marketing.xianxiatoufang.huamianzhutilist');
    Route::match(['post', 'delete', 'put'],'/marketing/xianxiatoufang/huamianzhutistore', [\App\Http\Controllers\Marketing\XianxiatoufangController::class, 'huamianzhutiStore'])->name('marketing.xianxiatoufang.huamianzhutistore');
    Route::match(['get'],'/marketing/xianxiatoufang/lujunziyuan', [\App\Http\Controllers\Marketing\XianxiatoufangController::class, 'lujunziyuanList'])->name('marketing.xianxiatoufang.lujunziyuanlist');
    Route::match(['post', 'delete', 'put'],'/marketing/xianxiatoufang/lujunziyuanstore', [\App\Http\Controllers\Marketing\XianxiatoufangController::class, 'lujunziyuanStore'])->name('marketing.xianxiatoufang.lujunziyuanstore');


    // 市场检核表
//    Route::match(['get'],'/marketing/jianhe/index', [\App\Http\Controllers\Marketing\JianheController::class, 'index'])->name('marketing.jianhe.index');



    Route::get('/marketing/jianhe', [\App\Http\Controllers\Marketing\JianheController::class, 'index'])->name('marketing.jianhe.index');
    Route::get('/marketing/jianhe/list', [\App\Http\Controllers\Marketing\JianheController::class, 'list'])->name('marketing.jianhe.list');
    Route::post('/marketing/jianhe/uploadimg', [\App\Http\Controllers\Marketing\JianheController::class, 'uploadImg'])->name('marketing.jianhe.uploadimg');
    Route::post('/marketing/jianhe/shanchuimg', [\App\Http\Controllers\Marketing\JianheController::class, 'shanchuImg'])->name('marketing.jianhe.shanchuimg');

    Route::post('/marketing/jianhe/uploadjianhe', [\App\Http\Controllers\Marketing\JianheController::class, 'uploadJianhe'])->name('marketing.jianhe.uploadjianhe');
    Route::post('/marketing/jianhe/beizhustore', [\App\Http\Controllers\Marketing\JianheController::class, 'beizhuStore'])->name('marketing.jianhe.beizhustore');



    Route::post('/marketing/jianhe/querenstore', [\App\Http\Controllers\Marketing\JianheController::class, 'querenStore'])->name('marketing.jianhe.querenstore');


    Route::get('/marketing/jianhe/create', [\App\Http\Controllers\Marketing\JianheController::class, 'create'])->name('marketing.jianhe.create');
    Route::post('/marketing/jianhe', [\App\Http\Controllers\Marketing\JianheController::class, 'store'])->name('marketing.jianhe.store');
    Route::get('/marketing/jianhe/{id}', [\App\Http\Controllers\Marketing\JianheController::class, 'show'])->name('marketing.jianhe.show');
    Route::get('/marketing/jianhe/{id}/edit', [\App\Http\Controllers\Marketing\JianheController::class, 'edit'])->name('marketing.jianhe.edit');
    Route::put('/marketing/jianhe/{id}', [\App\Http\Controllers\Marketing\JianheController::class, 'update'])->name('marketing.jianhe.update');
    Route::delete('/marketing/jianhe/{id}', [\App\Http\Controllers\Marketing\JianheController::class, 'destroy'])->name('marketing.jianhe.destroy');





    // IT物料领用
    // Route::match(['get'],'/material/material/index', [\App\Http\Controllers\Material\MaterialController::class, 'index'])->name('material.material.index');

    Route::match(['get'],'/material/material/wupin', [\App\Http\Controllers\Material\MaterialController::class, 'Wupin'])->name('material.material.wupin');
    Route::match(['get'],'/material/material/wupinlist', [\App\Http\Controllers\Material\MaterialController::class, 'wupinList'])->name('material.material.wupinlist');
    Route::match(['post', 'delete', 'put'],'/material/material/wupinstore', [\App\Http\Controllers\Material\MaterialController::class, 'wupinStore'])->name('material.material.wupinstore');
    Route::match(['post', 'delete', 'put', 'patch'],'/material/material/wupinstatechangestore', [\App\Http\Controllers\Material\MaterialController::class, 'wupinStateChangeStore'])->name('material.material.wupinstatechangestore');

    Route::match(['post', 'delete', 'put'],'/material/material/wupinleibiestore', [\App\Http\Controllers\Material\MaterialController::class, 'wupinleibieStore'])->name('material.material.wupinleibiestore');
    Route::match(['get'],'/material/material/wupinleibielist', [\App\Http\Controllers\Material\MaterialController::class, 'wupinleibieList'])->name('material.material.wupinleibielist');

    Route::match(['get'],'/material/material/kucunlist', [\App\Http\Controllers\Material\MaterialController::class, 'kucunList'])->name('material.material.kucunlist');



    Route::match(['get'],'/material/material/kucuntiaozheng', [\App\Http\Controllers\Material\MaterialController::class, 'Kucuntiaozheng'])->name('material.material.kucuntiaozheng');
    Route::match(['post', 'delete', 'put', 'patch'],'/material/material/kucuntiaozhengstore', [\App\Http\Controllers\Material\MaterialController::class, 'KucuntiaozhengStore'])->name('material.material.kucuntiaozhengstore');

    Route::match(['get'],'/material/material/kucun', [\App\Http\Controllers\Material\MaterialController::class, 'Kucun'])->name('material.material.kucun');
    Route::match(['get'],'/material/material/jinchukujilu', [\App\Http\Controllers\Material\MaterialController::class, 'jinchukujilu'])->name('material.material.jinchukujilu');
    Route::match(['get'],'/material/material/jinchukujilulist', [\App\Http\Controllers\Material\MaterialController::class, 'jinchukujiluList'])->name('material.material.jinchukujilulist');
    Route::match(['get'],'/material/material/chujiemingxi', [\App\Http\Controllers\Material\MaterialController::class, 'chujiemingxi'])->name('material.material.chujiemingxi');
    Route::match(['get'],'/material/material/chujiemingxilist', [\App\Http\Controllers\Material\MaterialController::class, 'chujiemingxiList'])->name('material.material.chujiemingxilist');


    // 观远
    Route::match(['get'],'/gy/shangchuan/index', [\App\Http\Controllers\Gy\ShangchuanController::class, 'index'])->name('gy.shangchuan.index');
    Route::match(['get'],'/gy/utils/index', [\App\Http\Controllers\Gy\UtilsController::class, 'index'])->name('gy.utils.index');
    Route::match(['get'],'/gy/utils/iframe', [\App\Http\Controllers\Gy\UtilsController::class, 'iframe'])->name('gy.utils.iframe');

    Route::match(['get'],'/gy/utils/calcapisso', [\App\Http\Controllers\Gy\UtilsController::class, 'calcapisso'])->name('gy.utils.calcapisso');
    Route::match(['get'],'/gy/utils/calcyxtapisso', [\App\Http\Controllers\Gy\UtilsController::class, 'calcyxtapisso'])->name('gy.utils.calcyxtapisso');




    // 财务园-学生台账
    Route::match(['get'],'/yzjcaiwuyuan/xueshengtaizhang/index', [\App\Http\Controllers\YzjCaiwuyuan\XueshengtaizhangController::class, 'index'])->name('yzjcaiwuyuan.xueshengtaizhang.index');
    Route::match(['get'],'/yzjcaiwuyuan/xueshengtaizhang/xueshengtaizhanglist', [\App\Http\Controllers\YzjCaiwuyuan\XueshengtaizhangController::class, 'xueshengtaizhangList'])->name('yzjcaiwuyuan.xueshengtaizhang.xueshengtaizhanglist');
    Route::match(['get'],'/yzjcaiwuyuan/xueshengtaizhang/quanxianlist', [\App\Http\Controllers\YzjCaiwuyuan\XueshengtaizhangController::class, 'quanxianList'])->name('yzjcaiwuyuan.xueshengtaizhang.quanxianlist');
    Route::match(['get'],'/yzjcaiwuyuan/xueshengtaizhang/shoufeibiaozhunlist', [\App\Http\Controllers\YzjCaiwuyuan\XueshengtaizhangController::class, 'shoufeibiaozhunList'])->name('yzjcaiwuyuan.xueshengtaizhang.shoufeibiaozhunlist');
    Route::match([ 'delete'],'/yzjcaiwuyuan/xueshengtaizhang/xueshengtaizhangstore', [\App\Http\Controllers\YzjCaiwuyuan\XueshengtaizhangController::class, 'xueshengtaizhangStore'])->name('yzjcaiwuyuan.xueshengtaizhang.xueshengtaizhangstore');
    Route::match([ 'get'],'/yzjcaiwuyuan/xueshengtaizhang/shanchurizhilist', [\App\Http\Controllers\YzjCaiwuyuan\XueshengtaizhangController::class, 'shanchurizhiList'])->name('yzjcaiwuyuan.xueshengtaizhang.shanchurizhilist');
    Route::match([ 'get'],'/yzjcaiwuyuan/xueshengtaizhang/xueshengtaizhangexport', [\App\Http\Controllers\YzjCaiwuyuan\XueshengtaizhangController::class, 'xueshengtaizhangExport'])->name('yzjcaiwuyuan.xueshengtaizhang.xueshengtaizhangexport');

    // 财务园-园薪酬
    Route::match(['get'],'/yzjcaiwuyuan/yuanxinchou/index', [\App\Http\Controllers\YzjCaiwuyuan\YuanxinchouController::class, 'index'])->name('yzjcaiwuyuan.yuanxinchou.index');
    Route::match(['get'],'/yzjcaiwuyuan/yuanxinchou/xinchoulist', [\App\Http\Controllers\YzjCaiwuyuan\YuanxinchouController::class, 'xinchouList'])->name('yzjcaiwuyuan.yuanxinchou.xinchoulist');
    Route::match(['post', 'delete', 'put', 'patch'],'/yzjcaiwuyuan/yuanxinchou/xinchoustore', [\App\Http\Controllers\YzjCaiwuyuan\YuanxinchouController::class, 'xinchouStore'])->name('yzjcaiwuyuan.yuanxinchou.xinchoustore');
    Route::match(['get'],'/yzjcaiwuyuan/yuanxinchou/quanxianlist', [\App\Http\Controllers\YzjCaiwuyuan\YuanxinchouController::class, 'quanxianList'])->name('yzjcaiwuyuan.yuanxinchou.quanxianlist');

    // 财务园-预算
    Route::match(['get'],'/yzjcaiwuyuan/yusuan/index', [\App\Http\Controllers\YzjCaiwuyuan\YusuanController::class, 'index'])->name('yzjcaiwuyuan.yusuan.index');
    Route::match(['get'],'/yzjcaiwuyuan/yusuan/yusuanlist', [\App\Http\Controllers\YzjCaiwuyuan\YusuanController::class, 'yusuanList'])->name('yzjcaiwuyuan.yusuan.yusuanlist');
    Route::match([ 'delete'],'/yzjcaiwuyuan/yusuan/yusuanstore', [\App\Http\Controllers\YzjCaiwuyuan\YusuanController::class, 'yusuanStore'])->name('yzjcaiwuyuan.yusuan.yusuanstore');
    Route::match([ 'get'],'/yzjcaiwuyuan/yusuan/shanchurizhilist', [\App\Http\Controllers\YzjCaiwuyuan\YusuanController::class, 'shanchurizhiList'])->name('yzjcaiwuyuan.yusuan.shanchurizhilist');
    Route::match(['get'],'/yzjcaiwuyuan/yusuan/quanxianlist', [\App\Http\Controllers\YzjCaiwuyuan\YusuanController::class, 'quanxianList'])->name('yzjcaiwuyuan.yusuan.quanxianlist');
    Route::match(['get'],'/yzjcaiwuyuan/yusuan/quanxiankemulist', [\App\Http\Controllers\YzjCaiwuyuan\YusuanController::class, 'quanxiankemuList'])->name('yzjcaiwuyuan.yusuan.quanxiankemulist');
    Route::match([ 'get'],'/yzjcaiwuyuan/yusuan/yusuanexport', [\App\Http\Controllers\YzjCaiwuyuan\YusuanController::class, 'yusuanExport'])->name('yzjcaiwuyuan.yusuan.yusuanexport');

    Route::match(['get'],'/yzjcaiwuyuan/yusuan/yusuansuodinglist', [\App\Http\Controllers\YzjCaiwuyuan\YusuanController::class, 'yusuanSuodingList'])->name('yzjcaiwuyuan.yusuan.yusuansuodinglist');
    Route::match(['put'],'/yzjcaiwuyuan/yusuan/updateyusuansuoding', [\App\Http\Controllers\YzjCaiwuyuan\YusuanController::class, 'updateYusuanSuoding'])->name('yzjcaiwuyuan.yusuan.updateyusuansuoding');

    Route::match(['get'],'/yzjcaiwuyuan/yusuan/yusuanxuanzhongexport', [\App\Http\Controllers\YzjCaiwuyuan\YusuanController::class, 'yusuanxuanzhongExport'])->name('yzjcaiwuyuan.yusuan.yusuanxuanzhongexport');

    Route::match(['post'],'/yzjcaiwuyuan/yusuan/yusuanimport', [\App\Http\Controllers\YzjCaiwuyuan\YusuanController::class, 'yusuanImport'])->name('yzjcaiwuyuan.yusuan.yusuanimport');
    Route::match(['post'],'/yzjcaiwuyuan/yusuan/yusuanjuesuanimport', [\App\Http\Controllers\YzjCaiwuyuan\YusuanController::class, 'yusuanjuesuanImport'])->name('yzjcaiwuyuan.yusuan.yusuanjuesuanimport');
    Route::match(['post'],'/yzjcaiwuyuan/yusuan/jianchajuesuanquanxian', [\App\Http\Controllers\YzjCaiwuyuan\YusuanController::class, 'jianchajuesuanquanxian'])->name('yzjcaiwuyuan.yusuan.jianchajuesuanquanxian');





    // IT服务中心
    Route::match([ 'get'],'/itfuwuzhongxin/index', [\App\Http\Controllers\ItFuwuzhongxin\IndexController::class, 'index'])->name('itfuwuzhongxin.index');
});



Route::match(['get', 'post'],'/yzjchat/robotduihua/index', [\App\Http\Controllers\YzjChat\RobotDuihuaController::class, 'index'])->name('yzj.yzjchat.robotduihua.index');


Route::match(['get', 'post'],'/yzjflow/marketingrobot/callback', [\App\Http\Controllers\YzjFlow\MarketingRobotController::class, 'callback'])->name('yzj.yzjflow.marketingrobot.callback');
Route::match(['get', 'post'],'/yzjflow/marketingrobot/datawidget', [\App\Http\Controllers\YzjFlow\MarketingRobotController::class, 'datawidget'])->name('yzj.yzjflow.marketingrobot.datawidget');
Route::match(['get', 'post'],'/yzjflow/marketingrobot/shenpijiedian', [\App\Http\Controllers\YzjFlow\MarketingRobotController::class, 'shenpijiedian'])->name('yzj.yzjflow.marketingrobot.shenpijiedian');
Route::match(['get', 'post'],'/yzjflow/marketingrobot/schoollist', [\App\Http\Controllers\YzjFlow\MarketingRobotController::class, 'schoolList'])->name('yzj.yzjflow.marketingrobot.schoollist');





// API 接口
Route::match(['get'],'/api/aliyunjiankong/ping', [\App\Http\Controllers\Api\AliYunjiankongController::class, 'ping'])->name('api.aliyunjiankong.ping');
Route::match(['get'],'/api/clbhealth', [\App\Http\Controllers\Api\ClbHealthController::class, 'index'])->name('api.clbhealth.index');



Route::match(['get'],'/api/dtb/person', [\App\Http\Controllers\Api\DtbController::class, 'person'])->name('api.dtb.person');
Route::match(['get'],'/api/gy/sso', [\App\Http\Controllers\Api\GyController::class, 'sso'])->name('api.gy.sso');
Route::match(['get'],'/api/gy/ceogongzuotaisso', [\App\Http\Controllers\Api\GyController::class, 'ceogongzuotaisso'])->name('api.gy.ceogongzuotaisso');
Route::match(['get'],'/api/kddqiwei/lianxiwoerweima', [\App\Http\Controllers\Api\KddQiweiController::class, 'lianxiwoerweima'])->name('api.kddqiwei.lianxiwoerweima');

//云学堂
Route::match(['get'],'/api/yxt/sso', [\App\Http\Controllers\Api\YxtController::class, 'sso'])->name('api.yxt.sso');
Route::match(['get'],'/api/yxt/huoqudandiandengluxinxi', [\App\Http\Controllers\Api\YxtController::class, 'huoqudandiandengluxinxi'])->name('api.yxt.huoqudandiandengluxinxi');

//酷学院
Route::match(['get'],'/api/kxy/huoqudandiandengluxinxi', [\App\Http\Controllers\Api\KxyController::class, 'huoqudandiandengluxinxi'])->name('api.kxy.huoqudandiandengluxinxi');
//Route::match(['get'],'/api/kxy/xuexixiangmuliebiao', [\App\Http\Controllers\Api\KxyController::class, 'xuexixiangmuliebiao'])->name('api.kxy.xuexixiangmuliebiao');
//Route::match(['get'],'/api/kxy/xuexixiangmujiankong', [\App\Http\Controllers\Api\KxyController::class, 'xuexixiangmujiankong'])->name('api.kxy.xuexixiangmujiankong');
//Route::match(['get'],'/api/kxy/userslist', [\App\Http\Controllers\Api\KxyController::class, 'userslist'])->name('api.kxy.userslist');



Route::match(['get'],'/api/yxt/zhishijindu', [\App\Http\Controllers\Api\YxtController::class, 'zhishijindu'])->name('api.yxt.zhishijindu');

Route::match(['get'],'/api/fanwei/huoqufanweiuserid', [\App\Http\Controllers\Api\FanweiController::class, 'huoqufanweiuserid'])->name('api.fanwei.huoqufanweiuserid');
Route::match(['get'],'/api/fanwei/liulankuangshenqingyuanxiao', [\App\Http\Controllers\Api\FanweiController::class, 'liulankuangshenqingyuanxiao'])->name('api.fanwei.liulankuangshenqingyuanxiao');
Route::match(['get'],'/api/fanwei/shenqingtiaokebanzu', [\App\Http\Controllers\Api\FanweiController::class, 'shenqingtiaokebanzu'])->name('api.fanwei.shenqingtiaokebanzu');
Route::match(['get'],'/api/fanwei/shenqingtiaokebanzuv2', [\App\Http\Controllers\Api\FanweiController::class, 'shenqingtiaokebanzuv2'])->name('api.fanwei.shenqingtiaokebanzuv2');







Route::match(['get'],'/api/beisen/zhuzhixinxi', [\App\Http\Controllers\Api\BeisenController::class, 'zhuzhixinxi'])->name('api.beisen.zhuzhixinxi');


// 泛微接口测试
Route::match(['get', 'post'],'/api/fanwei/qjd', [\App\Http\Controllers\Api\FanweiController::class, 'qjd'])->name('api.fanwei.qjd');

// 泛微资质保管人
Route::match(['get'],'/api/fanweizizhibaoguan/zizhibaoguanren', [\App\Http\Controllers\Api\FanweiZizhibaoguanController::class, 'zizhibaoguanren'])->name('api.fanweizizhibaoguan.zizhibaoguanren');

// 钉钉
Route::match(['get'],'/api/dd/yuangongxinxi', [\App\Http\Controllers\Api\DdController::class, 'yuangongxinxi'])->name('api.dd.yuangongxinxi');
Route::match(['get'],'/api/dd/yuangongxinxibyjobnumber', [\App\Http\Controllers\Api\DdController::class, 'yuangongxinxibyjobnumber'])->name('api.dd.yuangongxinxibyjobnumber');



// 企微回调
Route::match(['get', 'post'],'/api/qiwei/callback', [\App\Http\Controllers\Api\QiweiCallbackController::class, 'index'])->name('api.qiwei.callback');
Route::match(['get', 'post'],'/api/qiwei/callbackforkidcastle', [\App\Http\Controllers\Api\QiweiCallbackController::class, 'indexforkidcastle'])->name('api.qiwei.callbackforkidcastle');

// tb列表
Route::match(['get', 'post'],'/api/ceogongzuotaitb/tblist', [\App\Http\Controllers\Api\CeogongzuotaiTbControllerController::class, 'tblist'])->name('api.ceogongzuotaitb.tblist');
Route::match(['get', 'post'],'/api/ceogongzuotaitb/tblistzhuananguanli', [\App\Http\Controllers\Api\CeogongzuotaiTbControllerController::class, 'tblistzhuananguanli'])->name('api.ceogongzuotaitb.tblistzhuananguanli');



// 云之家审批流回传
Route::match(['get', 'post'],'/yzjflow/chailvjihuacallback/completeevent', [\App\Http\Controllers\YzjFlow\ChailvjihuaCallbackController::class, 'completeEvent'])->name('yzj.yzjflow.chailvjihuacallback.completeevent');
Route::match(['get', 'post'],'/material/yzjcallback/kucunlist', [\App\Http\Controllers\Material\YzjCallbackController::class, 'kucunList'])->name('material.yzjcallback.kucunlist');
Route::match(['get', 'post'],'/material/yzjcallback/kucunjiancha', [\App\Http\Controllers\Material\YzjCallbackController::class, 'kucunjiancha'])->name('material.yzjcallback.kucunjiancha');

Route::match(['get', 'post'],'/material/yzjcallback/jieshebei', [\App\Http\Controllers\Material\YzjCallbackController::class, 'jieShebei'])->name('material.yzjcallback.jieshebei');


Route::match(['get', 'post'],'/material/yzjcallback/jieitshenhe', [\App\Http\Controllers\Material\YzjCallbackController::class, 'jieItShenhe'])->name('material.yzjcallback.jieitshenhe');
Route::match(['get', 'post'],'/material/yzjcallback/huanshebei', [\App\Http\Controllers\Material\YzjCallbackController::class, 'huanShebei'])->name('material.yzjcallback.huanshebei');
Route::match(['get', 'post'],'/material/yzjcallback/huanitshenhe', [\App\Http\Controllers\Material\YzjCallbackController::class, 'huanItShenhe'])->name('material.yzjcallback.huanitshenhe');


// IT设备领用表单跳转
Route::get('/yzjitshebeilingyong/index', [\App\Http\Controllers\YzjItShebeiLingyongController::class, 'index'])->name('yzjitshebeilingyong.index');



Route::get('/nologin', [\App\Http\Controllers\YzjIndexController::class, 'nologin'])->name('yzj.nologin');


Route::match(['post','get'], '/log', [\App\Http\Controllers\Log\IndexController::class, 'index'])->name('log.index');



Route::get('/demo/demopage', [\App\Http\Controllers\DemoController::class, 'demopage'])->name('demo.demopage');




Route::get('/demo/demopage', [\App\Http\Controllers\DemoController::class, 'demopage'])->name('demo.demopage');
Route::get('/demo/iframe', [\App\Http\Controllers\DemoController::class, 'iframe'])->name('demo.iframe');


Route::get('/demo/forms', [\App\Http\Controllers\DemoController::class, 'forms'])->name('demo.forms');
Route::post('/demo/formspost', [\App\Http\Controllers\DemoController::class, 'formsPost'])->name('demo.formspost');
Route::get('/demo/providertest', [\App\Http\Controllers\DemoController::class, 'demoTestProvider'])->name('demo.providertest');
Route::get('/demo/ceshibaobiao', [\App\Http\Controllers\DemoController::class, 'ceshibaobiao'])->name('demo.ceshibaobiao');
Route::get('/demo/testtree', [\App\Http\Controllers\DemoController::class, 'testtree'])->name('demo.testtree');
Route::get('/demo/testkdd', [\App\Http\Controllers\DemoController::class, 'testkdd'])->name('demo.testkdd');

Route::get('/demo/wechatkejizhongxin', [\App\Http\Controllers\DemoController::class, 'wechatkejizhongxin'])->name('demo.wechatkejizhongxin');
Route::get('/demo/wechatkejizhongxincomplete', [\App\Http\Controllers\DemoController::class, 'wechatkejizhongxincomplete'])->name('demo.wechatkejizhongxincomplete');
Route::get('/demo/calcsignature', [\App\Http\Controllers\DemoController::class, 'calcSignature'])->name('demo.calcsignature');
Route::get('/demo/calcyingyongsignature', [\App\Http\Controllers\DemoController::class, 'calcYingyongSignature'])->name('demo.calcyingyongsignature');

Route::get('/demo/dingdingkejizhongxinmiandenglu', [\App\Http\Controllers\DemoController::class, 'dingdingkejizhongxinmiandenglu'])->name('demo.dingdingkejizhongxinmiandenglu');
Route::get('/demo/dingdingkejizhongxin', [\App\Http\Controllers\DemoController::class, 'dingdingkejizhongxin'])->name('demo.dingdingkejizhongxin');
Route::get('/demo/dingdingkejizhongxincomplete', [\App\Http\Controllers\DemoController::class, 'dingdingkejizhongxincomplete'])->name('demo.dingdingkejizhongxincomplete');

Route::get('/demo/helloworld', [\App\Http\Controllers\DemoController::class, 'helloworld'])->name('demo.helloworld');

Route::get('/demo/yunzhijiamiandenglu', [\App\Http\Controllers\DemoController::class, 'yunzhijiamiandenglu'])->name('demo.yunzhijiamiandenglu');




//Route::middleware('auth.weixindingdingdev')->group(function () {

    Route::get('/demo/tiaozhuan', [\App\Http\Controllers\DemoController::class, 'tiaozhuan'])->name('demo.tiaozhuan');

//});



Route::get('/demo/tiaozhuanb', [\App\Http\Controllers\DemoController::class, 'tiaozhuanb'])->name('demo.tiaozhuanb');

//https://eduappv2dev.kidcastle.com.cn/nologin


//
//
//Route::get('/index.html', function () {
//
//    echo "index.html";
////    return Inertia::render('Welcome', [
////        'canLogin' => Route::has('login'),
////        'canRegister' => Route::has('register'),
////        'laravelVersion' => Application::VERSION,
////        'phpVersion' => PHP_VERSION,
////    ]);
//});
////
//Route::get('/dashboard', function () {
//    return Inertia::render('Dashboard');
//})->middleware(['auth', 'verified'])->name('dashboard');





require __DIR__.'/auth.php';
